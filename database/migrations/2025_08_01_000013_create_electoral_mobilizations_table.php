<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar tablas dependientes si existen
        Schema::dropIfExists('electoral_mobilization_details');
        Schema::dropIfExists('electoral_mobilizations');

        // Crear la tabla electoral_mobilizations
        Schema::create('electoral_mobilizations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('voting_center_id');
            $table->dateTime('mobilization_date');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->integer('target_voters')->default(0);
            $table->integer('mobilized_voters')->default(0);
            $table->integer('confirmed_votes')->default(0);
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('coordinator_id')->nullable();
            $table->unsignedBigInteger('registered_by')->nullable();
            $table->unsignedBigInteger('estate_id')->nullable();
            $table->unsignedBigInteger('municipality_id')->nullable();
            $table->unsignedBigInteger('parish_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('voting_center_id')->references('id')->on('electoral_voting_centers')->onDelete('cascade');
            $table->foreign('coordinator_id')->references('id')->on('people')->onDelete('set null');
            $table->foreign('registered_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('estate_id')->references('id')->on('estates')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('voting_center_id', 'em_voting_center_idx');
            $table->index('coordinator_id', 'em_coordinator_idx');
            $table->index('mobilization_date', 'em_date_idx');
            $table->index('status', 'em_status_idx');
            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'em_location_idx');
        });

        // Crear la tabla electoral_mobilization_details
        Schema::create('electoral_mobilization_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('mobilization_id');
            $table->unsignedBigInteger('person_id');
            $table->enum('status', ['pending', 'contacted', 'confirmed', 'mobilized', 'voted', 'no_show'])->default('pending');
            $table->dateTime('contact_time')->nullable();
            $table->dateTime('confirmation_time')->nullable();
            $table->dateTime('mobilization_time')->nullable();
            $table->dateTime('voting_time')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('registered_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('mobilization_id')->references('id')->on('electoral_mobilizations')->onDelete('cascade');
            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');
            $table->foreign('registered_by')->references('id')->on('users')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('mobilization_id', 'emd_mobilization_idx');
            $table->index('person_id', 'emd_person_idx');
            $table->index(['mobilization_id', 'person_id'], 'emd_mob_person_idx');
            $table->index('status', 'emd_status_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::dropIfExists('electoral_mobilization_details');
        Schema::dropIfExists('electoral_mobilizations');

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
};
