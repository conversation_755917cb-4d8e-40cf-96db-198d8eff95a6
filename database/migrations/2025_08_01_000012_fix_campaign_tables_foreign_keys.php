<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // No usamos system_logs

        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Corregir la tabla campaign_targets si existe
        if (Schema::hasTable('campaign_targets')) {
            // Corregir la clave foránea campaign_id
            if (Schema::hasColumn('campaign_targets', 'campaign_id')) {
                $this->fixForeignKey('campaign_targets', 'campaign_id', 'campaigns', 'id', 'cascade');
            }
        }

        // Corregir la tabla campaign_details si existe
        if (Schema::hasTable('campaign_details')) {
            // Corregir la clave foránea campaign_id
            if (Schema::hasColumn('campaign_details', 'campaign_id')) {
                $this->fixForeignKey('campaign_details', 'campaign_id', 'campaigns', 'id', 'cascade');
            }

            // Corregir la clave foránea campaign_target_id
            if (Schema::hasColumn('campaign_details', 'campaign_target_id')) {
                $this->fixForeignKey('campaign_details', 'campaign_target_id', 'campaign_targets', 'id', 'set null');
            }
        }

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Método para corregir una clave foránea
     */
    private function fixForeignKey($table, $column, $referencedTable, $referencedColumn, $onDelete = 'cascade')
    {
        try {
            // Ejecutar SQL directo para eliminar cualquier clave foránea existente
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = '{$table}'
                AND COLUMN_NAME = '{$column}'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");

            foreach ($constraints as $constraint) {
                DB::statement("ALTER TABLE {$table} DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
            }

            // Agregar la nueva clave foránea
            DB::statement("
                ALTER TABLE {$table}
                ADD CONSTRAINT {$table}_{$column}_foreign
                FOREIGN KEY ({$column})
                REFERENCES {$referencedTable}({$referencedColumn})
                ON DELETE {$onDelete}
            ");

            // Agregar índice si no existe
            $indexName = "{$table}_{$column}_idx";
            if (!Schema::hasIndex($table, $indexName)) {
                DB::statement("CREATE INDEX {$indexName} ON {$table}({$column})");
            }

            // No registramos éxito
        } catch (\Exception $e) {
            // No registramos error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en el down para evitar eliminar las tablas
    }
};
