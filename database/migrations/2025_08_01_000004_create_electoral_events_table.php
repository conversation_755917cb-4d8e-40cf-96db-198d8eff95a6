<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar tablas dependientes si existen
        Schema::dropIfExists('electoral_event_attendances');
        Schema::dropIfExists('electoral_events');

        // Crear la tabla electoral_events
        Schema::create('electoral_events', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->dateTime('start_date');
            $table->dateTime('end_date')->nullable();
            $table->string('location')->nullable();
            $table->string('address')->nullable();
            $table->unsignedBigInteger('estate_id')->nullable();
            $table->unsignedBigInteger('municipality_id')->nullable();
            $table->unsignedBigInteger('parish_id')->nullable();
            $table->enum('type', ['training', 'mobilization', 'voting', 'meeting', 'other'])->default('meeting');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->integer('capacity')->nullable();
            $table->unsignedBigInteger('organizer_id')->nullable();
            $table->string('qr_code')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('estate_id')->references('id')->on('estates')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');
            $table->foreign('organizer_id')->references('id')->on('users')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('start_date', 'ee_start_date_idx');
            $table->index('status', 'ee_status_idx');
            $table->index('type', 'ee_type_idx');
            $table->index('name', 'ee_name_idx');
            $table->index('end_date', 'ee_end_date_idx');
            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'ee_location_idx');
        });

        // Crear la tabla electoral_event_attendances
        Schema::create('electoral_event_attendances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('event_id');
            $table->unsignedBigInteger('person_id');
            $table->dateTime('check_in_time')->nullable();
            $table->dateTime('check_out_time')->nullable();
            $table->enum('status', ['registered', 'confirmed', 'attended', 'absent'])->default('registered');
            $table->string('confirmation_code')->nullable();
            $table->string('qr_code')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('registered_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('event_id')->references('id')->on('electoral_events')->onDelete('cascade');
            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');
            $table->foreign('registered_by')->references('id')->on('users')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('event_id', 'eea_event_idx');
            $table->index('person_id', 'eea_person_idx');
            $table->index('status', 'eea_status_idx');
            $table->index(['event_id', 'person_id'], 'eea_event_person_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::dropIfExists('electoral_event_attendances');
        Schema::dropIfExists('electoral_events');

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
};
