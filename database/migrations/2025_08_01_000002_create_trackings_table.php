<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trackings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('person_id')->constrained('people')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('trackable_type')->nullable();
            $table->unsignedBigInteger('trackable_id')->nullable();
            $table->enum('tracking_type', ['mobilization', 'voter_mark', 'contact', 'event'])->nullable();
            $table->enum('status', ['pending', 'completed', 'cancelled', 'Contacted', 'Confirmed', 'Voted'])->default('pending');
            $table->text('notes')->nullable();
            $table->string('location')->nullable();
            $table->timestamp('tracking_date')->nullable();
            $table->timestamp('last_updated')->useCurrent();
            $table->string('contact_method')->nullable();
            $table->text('response')->nullable();
            $table->timestamp('follow_up_date')->nullable();
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->timestamps();
            $table->softDeletes();

            // Crear índice para búsquedas polimórficas
            $table->index(['trackable_type', 'trackable_id'], 'track_polymorphic_idx');
            // Índices adicionales
            $table->index('person_id', 'track_person_idx');
            $table->index('user_id', 'track_user_idx');
            $table->index('tracking_date', 'track_date_idx');
            $table->index('tracking_type', 'track_type_idx');
            $table->index('status', 'track_status_idx');
            $table->index('priority', 'track_priority_idx');
            $table->index('follow_up_date', 'track_follow_up_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trackings');
    }
};
