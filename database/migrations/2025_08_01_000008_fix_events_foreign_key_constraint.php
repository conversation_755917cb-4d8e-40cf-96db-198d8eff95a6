<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Marcar la migración original como completada
        if (!DB::table('migrations')->where('migration', '2025_04_29_203836_create_events_table')->exists()) {
            DB::table('migrations')->insert([
                'migration' => '2025_04_29_203836_create_events_table',
                'batch' => 1
            ]);
        }

        // No usamos system_logs

        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Verificar si la tabla events existe
        if (Schema::hasTable('events')) {
            // Verificar si la columna organizer_id existe
            if (Schema::hasColumn('events', 'organizer_id')) {
                try {
                    // Ejecutar SQL directo para eliminar cualquier clave foránea existente
                    $constraints = DB::select("
                        SELECT CONSTRAINT_NAME
                        FROM information_schema.KEY_COLUMN_USAGE
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'events'
                        AND COLUMN_NAME = 'organizer_id'
                        AND REFERENCED_TABLE_NAME IS NOT NULL
                    ");

                    foreach ($constraints as $constraint) {
                        DB::statement("ALTER TABLE events DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
                    }

                    // Agregar la nueva clave foránea
                    DB::statement("
                        ALTER TABLE events
                        ADD CONSTRAINT events_organizer_id_foreign
                        FOREIGN KEY (organizer_id)
                        REFERENCES people(id)
                        ON DELETE CASCADE
                    ");

                    // Agregar índices
                    if (!Schema::hasIndex('events', 'events_organizer_idx')) {
                        DB::statement("CREATE INDEX events_organizer_idx ON events(organizer_id)");
                    }
                    if (!Schema::hasIndex('events', 'events_date_idx') && Schema::hasColumn('events', 'date')) {
                        DB::statement("CREATE INDEX events_date_idx ON events(date)");
                    }
                    if (!Schema::hasIndex('events', 'events_type_idx') && Schema::hasColumn('events', 'type')) {
                        DB::statement("CREATE INDEX events_type_idx ON events(type)");
                    }

                    // No registramos éxito
                } catch (\Exception $e) {
                    // No registramos error
                }
            } else {
                // No registramos que la columna no existe
            }
        } else {
            // No registramos que la tabla no existe
        }

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en el down para evitar eliminar la tabla
    }
};
