<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('electoral_reports', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['attendance', 'mobilization', 'participation', 'performance', 'custom'])->default('custom');
            $table->json('parameters')->nullable();
            $table->json('data')->nullable();
            $table->dateTime('report_date');
            $table->dateTime('date_range_start')->nullable();
            $table->dateTime('date_range_end')->nullable();
            $table->foreignId('estate_id')->nullable()->constrained('estates')->onDelete('set null');
            $table->foreignId('municipality_id')->nullable()->constrained('municipalities')->onDelete('set null');
            $table->foreignId('parish_id')->nullable()->constrained('parishes')->onDelete('set null');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->string('file_path')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('electoral_reports');
    }
};
