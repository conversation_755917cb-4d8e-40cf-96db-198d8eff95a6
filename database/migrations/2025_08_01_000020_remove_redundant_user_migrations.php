<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Esta migración marca como completadas las migraciones redundantes
     * y elimina los archivos físicos correspondientes.
     */
    public function up(): void
    {
        // Lista de migraciones a marcar como completadas
        $migrations = [
            '2025_08_01_000003_modify_users_table',
            '2025_08_01_000010_remove_asociado_idasociado_from_users_table',
        ];

        // Obtener el batch actual
        $batch = DB::table('migrations')->max('batch') + 1;

        // Marcar cada migración como completada si no lo está ya
        foreach ($migrations as $migration) {
            if (!DB::table('migrations')->where('migration', $migration)->exists()) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => $batch
                ]);
                Log::info("Migración {$migration} marcada como ejecutada.");
            }
        }

        // Lista de archivos de migración a eliminar
        $filesToRemove = [
            database_path('migrations/2025_08_01_000003_modify_users_table.php'),
            database_path('migrations/2025_08_01_000010_remove_asociado_idasociado_from_users_table.php'),
        ];

        // Eliminar cada archivo si existe
        foreach ($filesToRemove as $file) {
            if (File::exists($file)) {
                try {
                    File::delete($file);
                    Log::info("Archivo de migración eliminado: {$file}");
                } catch (\Exception $e) {
                    Log::error("Error al eliminar el archivo de migración {$file}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No es posible restaurar los archivos eliminados
        Log::warning("No es posible restaurar los archivos de migración eliminados.");
    }
};
