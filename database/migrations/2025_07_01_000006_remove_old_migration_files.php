<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Lista de archivos de migración a eliminar
        $filesToRemove = [
            // Migraciones de people
            database_path('migrations/2025_04_29_203808_create_people_table.php'),
            database_path('migrations/2025_05_07_000012_add_user_id_to_people_table.php'),
            database_path('migrations/2025_05_10_130053_change_identification_to_cedula_in_people_table.php'),
            database_path('migrations/2025_05_10_131057_change_cedula_to_bigint_in_people_table.php'),
            database_path('migrations/2023_07_15_000000_modify_identification_field_in_people_table.php'),
            database_path('migrations/2025_07_01_000001_consolidated_create_people_table.php'),

            // Migraciones de trackings
            database_path('migrations/2025_04_29_203817_create_trackings_table.php'),
            database_path('migrations/2025_04_30_000000_create_trackings_table.php'),
            database_path('migrations/2025_05_06_190734_add_deleted_at_to_trackings_table.php'),
            database_path('migrations/2025_05_06_191036_add_tracking_type_to_trackings_table.php'),
            database_path('migrations/2025_05_06_191110_add_user_id_to_trackings_table.php'),
            database_path('migrations/2025_05_07_000013_add_additional_fields_to_trackings_table.php'),
            database_path('migrations/2025_05_10_200000_add_trackable_columns_to_trackings_table.php'),
            database_path('migrations/2025_07_01_000002_consolidated_create_trackings_table.php'),

            // Migraciones de users
            database_path('migrations/2025_05_15_000001_add_two_factor_auth_to_users_table.php'),
            database_path('migrations/2025_05_10_150001_modify_users_table.php'),
            database_path('migrations/2025_07_01_000003_consolidated_modify_users_table.php'),

            // Migraciones de patrol groups
            database_path('migrations/2025_05_11_075919_create_patrol_groups_table.php'),
            database_path('migrations/2025_05_11_075939_create_patrol_group_members_table.php'),
            database_path('migrations/2025_05_11_080020_create_patrol_activities_table.php'),
            database_path('migrations/2025_05_11_080100_create_patrol_activity_attendances_table.php'),
            database_path('migrations/2025_06_01_000001_create_patrol_groups_table.php'),
            database_path('migrations/2025_06_01_000002_create_patrol_group_members_table.php'),
            database_path('migrations/2025_06_01_000003_create_patrol_activities_table.php'),
            database_path('migrations/2025_06_01_000004_create_patrol_activity_attendances_table.php'),
            database_path('migrations/2025_07_01_000007_consolidated_create_patrol_tables.php'),

            // Migraciones de campaigns
            database_path('migrations/2025_04_10_195628_create_campaigns_table.php'),
            database_path('migrations/2025_04_10_195657_create_campaign_details_table.php'),
            database_path('migrations/2025_05_07_000010_create_campaign_targets_table.php'),
            database_path('migrations/2025_07_01_000008_consolidated_create_campaign_tables.php'),

            // Migraciones de events
            database_path('migrations/2025_04_29_203836_create_events_table.php'),

            // Migraciones de electoral_events
            database_path('migrations/2025_06_15_000001_fix_electoral_events_table.php'),
            database_path('migrations/2025_06_20_000001_recreate_electoral_events_table.php'),
            database_path('migrations/2025_06_25_000001_drop_and_recreate_electoral_events_table.php'),
            database_path('migrations/2025_06_26_000001_fix_electoral_events_with_raw_sql.php'),
            database_path('migrations/2025_07_01_000004_consolidated_create_electoral_events_table.php'),

            // Migraciones de índices
            database_path('migrations/2025_05_15_000001_add_indexes_to_tables.php'),
            database_path('migrations/2025_05_20_000001_add_performance_indexes.php'),
            database_path('migrations/2025_05_30_000001_add_missing_indexes.php'),
            database_path('migrations/2025_05_11_080311_fix_long_index_names.php'),
            database_path('migrations/2025_05_11_081059_fix_duplicate_indexes.php'),

            // Otras migraciones de corrección
            database_path('migrations/2025_05_11_081516_fix_electoral_events_indexes.php'),
            database_path('migrations/2025_05_11_081737_fix_all_migration_issues.php'),
            database_path('migrations/2025_05_11_082116_mark_pending_migrations_as_completed.php'),
            database_path('migrations/2025_05_10_160000_remove_old_migrations.php'),
            database_path('migrations/2025_06_12_000001_fix_migration_files_for_duplicate_indexes.php'),

            // Migraciones unificadas antiguas
            database_path('migrations/2025_05_10_150000_create_unified_tables.php'),
        ];

        // Eliminar cada archivo si existe
        foreach ($filesToRemove as $file) {
            if (File::exists($file)) {
                try {
                    File::delete($file);
                    Log::info("Archivo de migración eliminado: {$file}");
                } catch (\Exception $e) {
                    Log::error("Error al eliminar el archivo de migración {$file}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No es posible restaurar los archivos eliminados
        Log::warning("No es posible restaurar los archivos de migración eliminados.");
    }
};
