<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Lista de migraciones antiguas a marcar como completadas
        $migrations = [
            // Migraciones de people
            '2025_04_29_203808_create_people_table',
            '2025_05_07_000012_add_user_id_to_people_table',
            '2025_05_10_130053_change_identification_to_cedula_in_people_table',
            '2025_05_10_131057_change_cedula_to_bigint_in_people_table',
            '2023_07_15_000000_modify_identification_field_in_people_table',
            '2025_07_01_000001_consolidated_create_people_table',

            // Migraciones de trackings
            '2025_04_29_203817_create_trackings_table',
            '2025_04_30_000000_create_trackings_table',
            '2025_05_06_190734_add_deleted_at_to_trackings_table',
            '2025_05_06_191036_add_tracking_type_to_trackings_table',
            '2025_05_06_191110_add_user_id_to_trackings_table',
            '2025_05_07_000013_add_additional_fields_to_trackings_table',
            '2025_05_10_200000_add_trackable_columns_to_trackings_table',
            '2025_07_01_000002_consolidated_create_trackings_table',

            // Migraciones de users
            '2025_05_15_000001_add_two_factor_auth_to_users_table',
            '2025_05_10_150001_modify_users_table',
            '2025_07_01_000003_consolidated_modify_users_table',

            // Migraciones de patrol groups
            '2025_05_11_075919_create_patrol_groups_table',
            '2025_05_11_075939_create_patrol_group_members_table',
            '2025_05_11_080020_create_patrol_activities_table',
            '2025_05_11_080100_create_patrol_activity_attendances_table',
            '2025_06_01_000001_create_patrol_groups_table',
            '2025_06_01_000002_create_patrol_group_members_table',
            '2025_06_01_000003_create_patrol_activities_table',
            '2025_06_01_000004_create_patrol_activity_attendances_table',
            '2025_07_01_000007_consolidated_create_patrol_tables',

            // Migraciones de campaigns
            '2025_04_10_195628_create_campaigns_table',
            '2025_04_10_195657_create_campaign_details_table',
            '2025_05_07_000010_create_campaign_targets_table',
            '2025_07_01_000008_consolidated_create_campaign_tables',

            // Migraciones de events
            '2025_04_29_203836_create_events_table',

            // Migraciones de electoral_events
            '2025_06_15_000001_fix_electoral_events_table',
            '2025_06_20_000001_recreate_electoral_events_table',
            '2025_06_25_000001_drop_and_recreate_electoral_events_table',
            '2025_06_26_000001_fix_electoral_events_with_raw_sql',
            '2025_07_01_000004_consolidated_create_electoral_events_table',

            // Migraciones de índices
            '2025_05_15_000001_add_indexes_to_tables',
            '2025_05_20_000001_add_performance_indexes',
            '2025_05_30_000001_add_missing_indexes',
            '2025_05_11_080311_fix_long_index_names',
            '2025_05_11_081059_fix_duplicate_indexes',

            // Otras migraciones de corrección
            '2025_05_11_081516_fix_electoral_events_indexes',
            '2025_05_11_081737_fix_all_migration_issues',
            '2025_05_11_082116_mark_pending_migrations_as_completed',
            '2025_05_10_160000_remove_old_migrations',
            '2025_06_12_000001_fix_migration_files_for_duplicate_indexes',
        ];

        // Obtener el batch actual
        $batch = DB::table('migrations')->max('batch') + 1;

        // Marcar cada migración como completada si no lo está ya
        foreach ($migrations as $migration) {
            if (!DB::table('migrations')->where('migration', $migration)->exists()) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => $batch
                ]);
                Log::info("Migración {$migration} marcada como ejecutada.");
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No es necesario hacer nada en el método down
    }
};
