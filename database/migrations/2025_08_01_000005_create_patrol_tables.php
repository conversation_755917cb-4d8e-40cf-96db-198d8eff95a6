<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar tablas dependientes si existen
        Schema::dropIfExists('patrol_activity_attendances');
        Schema::dropIfExists('patrol_activities');
        Schema::dropIfExists('patrol_group_members');
        Schema::dropIfExists('patrol_groups');

        // Crear la tabla patrol_groups
        Schema::create('patrol_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('leader_id');
            $table->unsignedBigInteger('estate_id')->nullable();
            $table->unsignedBigInteger('municipality_id')->nullable();
            $table->unsignedBigInteger('parish_id')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->enum('type', ['electoral', 'community', 'special', 'other'])->default('electoral');
            $table->integer('goal_members')->default(10);
            $table->integer('goal_activities')->default(5);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('zone')->nullable();
            $table->string('sector')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('leader_id')->references('id')->on('people')->onDelete('cascade');
            $table->foreign('estate_id')->references('id')->on('estates')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('name', 'pg_name_idx');
            $table->index('leader_id', 'pg_leader_idx');
            $table->index('status', 'pg_status_idx');
            $table->index('type', 'pg_type_idx');
            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'pg_location_idx');
        });

        // Crear la tabla patrol_group_members
        Schema::create('patrol_group_members', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id');
            $table->unsignedBigInteger('person_id');
            $table->enum('role', ['leader', 'member', 'coordinator'])->default('member');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->date('joined_date')->nullable();
            $table->date('left_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('group_id')->references('id')->on('patrol_groups')->onDelete('cascade');
            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');

            // Ensure a person can only be a member of a group once
            $table->unique(['group_id', 'person_id'], 'pgm_group_person_unique');

            // Agregar índices estandarizados
            $table->index(['group_id', 'person_id'], 'pgm_group_person_idx');
            $table->index('role', 'pgm_role_idx');
            $table->index('status', 'pgm_status_idx');
            $table->index('joined_date', 'pgm_joined_date_idx');
        });

        // Crear la tabla patrol_activities
        Schema::create('patrol_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['patrol', 'meeting', 'training', 'other'])->default('patrol');
            $table->enum('status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->dateTime('scheduled_date');
            $table->dateTime('end_date')->nullable();
            $table->string('location')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('group_id')->references('id')->on('patrol_groups')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('group_id', 'pa_group_idx');
            $table->index('title', 'pa_title_idx');
            $table->index('scheduled_date', 'pa_scheduled_date_idx');
            $table->index('status', 'pa_status_idx');
            $table->index('type', 'pa_type_idx');
        });

        // Crear la tabla patrol_activity_attendances
        Schema::create('patrol_activity_attendances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('activity_id');
            $table->unsignedBigInteger('person_id');
            $table->boolean('attended')->default(false);
            $table->enum('status', ['confirmed', 'pending', 'declined'])->default('pending');
            $table->text('notes')->nullable();
            $table->dateTime('check_in_time')->nullable();
            $table->dateTime('check_out_time')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('activity_id')->references('id')->on('patrol_activities')->onDelete('cascade');
            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');

            // Ensure a person can only register once per activity
            $table->unique(['activity_id', 'person_id'], 'paa_activity_person_unique');

            // Agregar índices estandarizados
            $table->index('activity_id', 'paa_activity_idx');
            $table->index('person_id', 'paa_person_idx');
            $table->index('status', 'paa_status_idx');
            $table->index('attended', 'paa_attended_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::dropIfExists('patrol_activity_attendances');
        Schema::dropIfExists('patrol_activities');
        Schema::dropIfExists('patrol_group_members');
        Schema::dropIfExists('patrol_groups');

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
};
