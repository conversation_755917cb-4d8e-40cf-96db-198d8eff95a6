<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Esta migración se ejecutará al final para prevenir la creación de índices duplicados
        
        // Lista de índices a verificar y eliminar si existen
        $indexesToCheck = [
            'electoral_events' => [
                'electoral_events_start_date_index',
                'electoral_events_status_index',
                'electoral_events_type_index',
                'electoral_events_name_index',
                'electoral_events_end_date_index',
                'electoral_events_estate_id_municipality_id_parish_id_index'
            ]
        ];
        
        foreach ($indexesToCheck as $table => $indexes) {
            if (Schema::hasTable($table)) {
                foreach ($indexes as $index) {
                    try {
                        // Verificar si el índice existe
                        $indexExists = !empty(DB::select("SHOW INDEX FROM {$table} WHERE Key_name = '{$index}'"));
                        
                        if ($indexExists) {
                            // Eliminar el índice
                            DB::statement("DROP INDEX {$index} ON {$table}");
                            Log::info("Índice {$index} eliminado de la tabla {$table}.");
                        }
                    } catch (\Exception $e) {
                        Log::warning("Error al verificar o eliminar el índice {$index} de la tabla {$table}: " . $e->getMessage());
                    }
                }
            }
        }
        
        // Asegurarse de que existan los índices estandarizados
        if (Schema::hasTable('electoral_events')) {
            Schema::table('electoral_events', function (Blueprint $table) {
                // Verificar y crear índices con nombres estandarizados
                $standardIndexes = [
                    'start_date' => 'ee_start_date_idx',
                    'status' => 'ee_status_idx',
                    'type' => 'ee_type_idx',
                    'name' => 'ee_name_idx',
                    'end_date' => 'ee_end_date_idx'
                ];
                
                foreach ($standardIndexes as $column => $indexName) {
                    if (Schema::hasColumn('electoral_events', $column)) {
                        try {
                            // Verificar si el índice ya existe
                            $indexExists = !empty(DB::select("SHOW INDEX FROM electoral_events WHERE Key_name = '{$indexName}'"));
                            
                            if (!$indexExists) {
                                $table->index($column, $indexName);
                                Log::info("Índice {$indexName} creado correctamente.");
                            }
                        } catch (\Exception $e) {
                            Log::warning("Error al crear el índice {$indexName}: " . $e->getMessage());
                        }
                    }
                }
                
                // Verificar y crear el índice de ubicación
                if (Schema::hasColumns('electoral_events', ['estate_id', 'municipality_id', 'parish_id'])) {
                    try {
                        // Verificar si el índice ya existe
                        $indexExists = !empty(DB::select("SHOW INDEX FROM electoral_events WHERE Key_name = 'ee_location_idx'"));
                        
                        if (!$indexExists) {
                            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'ee_location_idx');
                            Log::info("Índice ee_location_idx creado correctamente.");
                        }
                    } catch (\Exception $e) {
                        Log::warning("Error al crear el índice ee_location_idx: " . $e->getMessage());
                    }
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No es necesario hacer nada en el método down
    }
};