0<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Migración consolidada que crea las tablas relacionadas con datos financieros:
     * - currencies
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar las tablas si existen
        Schema::dropIfExists('currencies');

        // 1. Crear la tabla currencies
        Schema::create('currencies', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('symbol', 4)->comment('Símbolo de la moneda');
            $table->string('name', 40)->comment('Nombre de la moneda');
            $table->boolean('default')->default(false)->comment('Moneda por defecto');
            $table->integer('decimal_places')->default(2)
                ->comment('Máximo número de decimales a gestionar. El máximo es 10');
            $table->foreignId('country_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');

            // Índices para optimizar consultas
            $table->index('symbol', 'curr_symbol_idx');
            $table->index('name', 'curr_name_idx');
            $table->index('default', 'curr_default_idx');
            $table->index('country_id', 'curr_country_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
