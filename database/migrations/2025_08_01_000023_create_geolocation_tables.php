<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migración consolidada que crea las tablas relacionadas con geolocalización:
     * - countries
     * - estates
     * - municipalities
     * - parishes
     * - cities
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar las tablas si existen
        Schema::dropIfExists('cities');
        Schema::dropIfExists('parishes');
        Schema::dropIfExists('municipalities');
        Schema::dropIfExists('estates');
        Schema::dropIfExists('countries');

        // 1. Crear la tabla countries
        Schema::create('countries', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 100)->comment('Nombre del Pais');
            $table->string('prefix', 3)->comment('Prefijo único del Pais');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            $table->unique(['name', 'prefix'])->comment('Clave única para el registro');
            
            // Índices para optimizar consultas
            $table->index('name', 'country_name_idx');
            $table->index('prefix', 'country_prefix_idx');
        });

        // 2. Crear la tabla estates
        Schema::create('estates', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 100)->comment('Nombre del Estado');
            $table->string('code', 10)->nullable()->comment('Código que identifica al Estado');
            $table->foreignId('country_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            $table->unique(['country_id', 'name'])->comment('Clave única para el registro');
            
            // Índices para optimizar consultas
            $table->index('name', 'estate_name_idx');
            $table->index('code', 'estate_code_idx');
            $table->index('country_id', 'estate_country_idx');
        });

        // 3. Crear la tabla municipalities
        Schema::create('municipalities', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 100)->comment('Nombre del Municipio');
            $table->string('code', 10)->nullable()->comment('Código que identifica al Municipio');
            $table->foreignId('estate_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            $table->unique(['estate_id', 'name'])->comment('Clave única para el registro');
            
            // Índices para optimizar consultas
            $table->index('name', 'muni_name_idx');
            $table->index('code', 'muni_code_idx');
            $table->index('estate_id', 'muni_estate_idx');
        });

        // 4. Crear la tabla parishes
        Schema::create('parishes', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 100)->comment('Nombre de la Parroquia');
            $table->string('code', 10)->nullable()->comment('Código que identifica a la Parroquia');
            $table->foreignId('municipality_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            $table->unique(['municipality_id', 'name'])->comment('Clave única para el registro');
            
            // Índices para optimizar consultas
            $table->index('name', 'parish_name_idx');
            $table->index('code', 'parish_code_idx');
            $table->index('municipality_id', 'parish_muni_idx');
        });

        // 5. Crear la tabla cities
        Schema::create('cities', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 100)->comment('Nombre de la Ciudad');
            $table->foreignId('estate_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->nullableMorphs('citiable');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            $table->unique(['estate_id', 'name'])->comment('Clave única para el registro');
            
            // Índices para optimizar consultas
            $table->index('name', 'city_name_idx');
            $table->index('estate_id', 'city_estate_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cities');
        Schema::dropIfExists('parishes');
        Schema::dropIfExists('municipalities');
        Schema::dropIfExists('estates');
        Schema::dropIfExists('countries');
    }
};
