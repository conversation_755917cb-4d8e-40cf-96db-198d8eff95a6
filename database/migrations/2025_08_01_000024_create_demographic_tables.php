<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migración consolidada que crea las tablas relacionadas con datos demográficos:
     * - genders
     * - marital_status
     * - professions
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar las tablas si existen
        Schema::dropIfExists('genders');
        Schema::dropIfExists('marital_status');
        Schema::dropIfExists('professions');

        // 1. Crear la tabla genders
        Schema::create('genders', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->nullable()->comment('Nombre del género');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            
            // Índices para optimizar consultas
            $table->index('name', 'gender_name_idx');
        });

        // 2. Crear la tabla marital_status
        Schema::create('marital_status', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 100)->unique()->comment('Nombre del estado civil');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            
            // Índices para optimizar consultas
            $table->index('name', 'marital_name_idx');
        });

        // 3. Crear la tabla professions
        Schema::create('professions', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Identificador único del registro');
            $table->string('name', 200)->unique()->comment('Nombre de la profesión');
            $table->string('acronym', 10)->nullable()->comment('Siglas o acrónimo de la profesión');
            $table->timestamps();
            $table->softDeletes()->comment('Fecha y hora en la que el registro fue eliminado');
            
            // Índices para optimizar consultas
            $table->index('name', 'prof_name_idx');
            $table->index('acronym', 'prof_acronym_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('professions');
        Schema::dropIfExists('marital_status');
        Schema::dropIfExists('genders');
    }
};
