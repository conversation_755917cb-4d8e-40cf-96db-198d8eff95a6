# Migraciones Consolidadas

Este directorio contiene las migraciones de la base de datos para el proyecto. Se han consolidado las migraciones para tener una estructura más limpia y organizada, con un archivo por modelo/tabla principal.

## Migraciones Principales

Las siguientes migraciones son las principales del sistema:

### Tablas Principales

1. `2025_08_01_000001_create_people_table.php` - Crea la tabla `people` con todos los campos necesarios.

2. `2025_08_01_000002_create_trackings_table.php` - <PERSON>rea la tabla `trackings` con todos los campos necesarios.

3. `0001_01_01_000000_create_users_table.php` - Crea la tabla `users` con todos los campos necesarios, incluyendo autenticación de dos factores y seguridad.

4. `2025_08_01_000004_create_electoral_events_table.php` - Crea la tabla `electoral_events` y tablas relacionadas.

5. `2025_08_01_000005_create_patrol_tables.php` - Crea las tablas relacionadas con patrullas (`patrol_groups`, `patrol_group_members`, etc.).

6. `2025_08_01_000006_create_campaign_tables.php` - Crea las tablas relacionadas con campañas.

7. `2025_08_01_000007_create_electoral_voting_centers_table.php` - Crea la tabla de centros de votación.

8. `2025_08_01_000021_create_registration_tokens_table.php` - Crea la tabla `registration_tokens` con todos los campos necesarios.

9. `2025_08_01_000022_create_families_tables.php` - Crea las tablas relacionadas con familias (`family_types`, `families`, `relationships`, `associate_families`).

10. `2025_08_01_000023_create_geolocation_tables.php` - Crea las tablas relacionadas con geolocalización (`countries`, `estates`, `municipalities`, `parishes`, `cities`).

11. `2025_08_01_000024_create_demographic_tables.php` - Crea las tablas relacionadas con datos demográficos (`genders`, `marital_status`, `professions`).

12. `2025_08_01_000025_create_financial_tables.php` - Crea las tablas relacionadas con datos financieros (`currencies`).

13. `2025_08_01_000026_create_system_tables.php` - Crea las tablas relacionadas con el sistema (`cache`, `jobs`, `activity_logs`, `notifications`, `system_logs`, `audits`).

14. `2025_08_01_000027_create_companies_table.php` - Crea la tabla `companies` con todos los campos necesarios.

### Correcciones y Ajustes

15. `2025_08_01_000008_fix_events_foreign_key_constraint.php` - Corrige la clave foránea de la tabla `events`.

16. `2025_08_01_000009_fix_all_foreign_keys_to_people_table.php` - Corrige todas las claves foráneas que hacen referencia a la tabla `people`.

17. `2025_08_01_000010_fix_electoral_events_foreign_keys.php` - Corrige las claves foráneas de las tablas `electoral_events`.

18. `2025_08_01_000011_fix_patrol_tables_foreign_keys.php` - Corrige las claves foráneas de las tablas de patrullas.

19. `2025_08_01_000012_fix_campaign_tables_foreign_keys.php` - Corrige las claves foráneas de las tablas de campañas.

### Mantenimiento de Migraciones

20. `2025_07_01_000005_mark_old_migrations_as_completed.php` - Marca todas las migraciones antiguas como completadas.

21. `2025_07_01_000006_remove_old_migration_files.php` - Elimina físicamente los archivos de migración antiguos.

22. `2025_08_01_000020_remove_redundant_user_migrations.php` - Elimina las migraciones redundantes de la tabla `users` que han sido consolidadas.

23. `2025_08_01_000030_remove_all_redundant_migrations.php` - Elimina todas las migraciones redundantes que han sido consolidadas en las nuevas migraciones.

## Cómo usar estas migraciones

Para usar estas migraciones principales, sigue estos pasos:

1. Ejecuta las migraciones:
   ```bash
   php artisan migrate
   ```

## Beneficios de la consolidación

1. **Estructura más limpia**: Menos archivos de migración para mantener.
2. **Mejor organización**: Cada tabla tiene una única migración que la crea con todos sus campos.
3. **Menos conflictos**: Se evitan problemas de índices duplicados y otras inconsistencias.
4. **Mejor rendimiento**: Las migraciones se ejecutan más rápido al haber menos archivos.
5. **Mayor claridad**: Es más fácil entender la estructura de la base de datos con un archivo por modelo.

## Esquema para futuras modificaciones

Para futuras modificaciones a la estructura de la base de datos, sigue estas pautas:

1. **Modificaciones menores**: Para cambios pequeños (como agregar una columna), crea una nueva migración con un nombre descriptivo:
   ```bash
   php artisan make:migration add_field_to_table_name --table=table_name
   ```

2. **Modificaciones mayores**: Para cambios significativos, considera actualizar la migración consolidada y ejecutar:
   ```bash
   php artisan migrate:fresh --seed
   ```
   Nota: Esto reiniciará la base de datos, así que solo hazlo en entornos de desarrollo.

3. **Índices y optimizaciones**: Agrupa las optimizaciones relacionadas en una sola migración:
   ```bash
   php artisan make:migration add_performance_indexes_to_module_name
   ```

## Notas importantes

- Las migraciones antiguas han sido marcadas como completadas en la base de datos.
- Los archivos de migración antiguos han sido eliminados para mantener el directorio limpio.
- Usa el seeder `FixMigrationsSeeder` para marcar migraciones como completadas si es necesario:
  ```bash
  php artisan db:seed --class=FixMigrationsSeeder
  ```
- Documenta cualquier cambio significativo en este README para mantener la coherencia.
