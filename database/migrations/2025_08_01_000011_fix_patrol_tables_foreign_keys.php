<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // No usamos system_logs

        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Corregir la tabla patrol_groups si existe
        if (Schema::hasTable('patrol_groups')) {
            // Corregir la clave foránea leader_id
            if (Schema::hasColumn('patrol_groups', 'leader_id')) {
                $this->fixForeignKey('patrol_groups', 'leader_id', 'people', 'id', 'cascade');
            }

            // Corregir la clave foránea estate_id
            if (Schema::hasColumn('patrol_groups', 'estate_id')) {
                $this->fixForeignKey('patrol_groups', 'estate_id', 'estates', 'id', 'set null');
            }

            // Corregir la clave foránea municipality_id
            if (Schema::hasColumn('patrol_groups', 'municipality_id')) {
                $this->fixForeignKey('patrol_groups', 'municipality_id', 'municipalities', 'id', 'set null');
            }

            // Corregir la clave foránea parish_id
            if (Schema::hasColumn('patrol_groups', 'parish_id')) {
                $this->fixForeignKey('patrol_groups', 'parish_id', 'parishes', 'id', 'set null');
            }
        }

        // Corregir la tabla patrol_group_members si existe
        if (Schema::hasTable('patrol_group_members')) {
            // Corregir la clave foránea group_id
            if (Schema::hasColumn('patrol_group_members', 'group_id')) {
                $this->fixForeignKey('patrol_group_members', 'group_id', 'patrol_groups', 'id', 'cascade');
            }

            // Corregir la clave foránea person_id
            if (Schema::hasColumn('patrol_group_members', 'person_id')) {
                $this->fixForeignKey('patrol_group_members', 'person_id', 'people', 'id', 'cascade');
            }
        }

        // Corregir la tabla patrol_activities si existe
        if (Schema::hasTable('patrol_activities')) {
            // Corregir la clave foránea group_id
            if (Schema::hasColumn('patrol_activities', 'group_id')) {
                $this->fixForeignKey('patrol_activities', 'group_id', 'patrol_groups', 'id', 'cascade');
            }

            // Corregir la clave foránea created_by
            if (Schema::hasColumn('patrol_activities', 'created_by')) {
                $this->fixForeignKey('patrol_activities', 'created_by', 'users', 'id', 'set null');
            }
        }

        // Corregir la tabla patrol_activity_attendances si existe
        if (Schema::hasTable('patrol_activity_attendances')) {
            // Corregir la clave foránea activity_id
            if (Schema::hasColumn('patrol_activity_attendances', 'activity_id')) {
                $this->fixForeignKey('patrol_activity_attendances', 'activity_id', 'patrol_activities', 'id', 'cascade');
            }

            // Corregir la clave foránea person_id
            if (Schema::hasColumn('patrol_activity_attendances', 'person_id')) {
                $this->fixForeignKey('patrol_activity_attendances', 'person_id', 'people', 'id', 'cascade');
            }
        }

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Método para corregir una clave foránea
     */
    private function fixForeignKey($table, $column, $referencedTable, $referencedColumn, $onDelete = 'cascade')
    {
        try {
            // Ejecutar SQL directo para eliminar cualquier clave foránea existente
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = '{$table}'
                AND COLUMN_NAME = '{$column}'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");

            foreach ($constraints as $constraint) {
                DB::statement("ALTER TABLE {$table} DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
            }

            // Agregar la nueva clave foránea
            DB::statement("
                ALTER TABLE {$table}
                ADD CONSTRAINT {$table}_{$column}_foreign
                FOREIGN KEY ({$column})
                REFERENCES {$referencedTable}({$referencedColumn})
                ON DELETE {$onDelete}
            ");

            // Agregar índice si no existe
            $indexName = "{$table}_{$column}_idx";
            if (!Schema::hasIndex($table, $indexName)) {
                DB::statement("CREATE INDEX {$indexName} ON {$table}({$column})");
            }

            // No registramos éxito
        } catch (\Exception $e) {
            // No registramos error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en el down para evitar eliminar las tablas
    }
};
