<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migración consolidada que crea la tabla companies.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar la tabla si existe
        Schema::dropIfExists('companies');

        // Crear la tabla companies
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('country');
            $table->string('name');
            $table->string('company_type');
            $table->string('rif')->unique();
            $table->string('phone');
            $table->string('email');
            $table->text('address');
            $table->string('logo')->nullable();
            $table->boolean('status')->default(1);
            $table->string('tax_name');
            $table->string('tax_number');
            $table->string('postal_code');
            $table->timestamps();
            $table->softDeletes();
            
            // Índices para optimizar consultas
            $table->index('name', 'company_name_idx');
            $table->index('rif', 'company_rif_idx');
            $table->index('email', 'company_email_idx');
            $table->index('status', 'company_status_idx');
            $table->index('country', 'company_country_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
