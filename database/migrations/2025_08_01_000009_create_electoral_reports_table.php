<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar la tabla si ya existe
        Schema::dropIfExists('electoral_reports');

        // Crear la tabla electoral_reports
        Schema::create('electoral_reports', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['attendance', 'mobilization', 'participation', 'performance', 'custom'])->default('custom');
            $table->json('parameters')->nullable();
            $table->json('data')->nullable();
            $table->dateTime('report_date');
            $table->dateTime('date_range_start')->nullable();
            $table->dateTime('date_range_end')->nullable();
            $table->unsignedBigInteger('estate_id')->nullable();
            $table->unsignedBigInteger('municipality_id')->nullable();
            $table->unsignedBigInteger('parish_id')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->string('file_path')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('estate_id')->references('id')->on('estates')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('title', 'er_title_idx');
            $table->index('type', 'er_type_idx');
            $table->index('report_date', 'er_report_date_idx');
            $table->index(['date_range_start', 'date_range_end'], 'er_date_range_idx');
            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'er_location_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::dropIfExists('electoral_reports');

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
};
