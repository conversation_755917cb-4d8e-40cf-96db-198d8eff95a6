<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // No usamos system_logs

        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Corregir la tabla electoral_events si existe
        if (Schema::hasTable('electoral_events')) {
            // Corregir la clave foránea organizer_id
            if (Schema::hasColumn('electoral_events', 'organizer_id')) {
                $this->fixForeignKey('electoral_events', 'organizer_id', 'users', 'id', 'set null');
            }

            // Corregir la clave foránea estate_id
            if (Schema::hasColumn('electoral_events', 'estate_id')) {
                $this->fixForeignKey('electoral_events', 'estate_id', 'estates', 'id', 'set null');
            }

            // Corregir la clave foránea municipality_id
            if (Schema::hasColumn('electoral_events', 'municipality_id')) {
                $this->fixForeignKey('electoral_events', 'municipality_id', 'municipalities', 'id', 'set null');
            }

            // Corregir la clave foránea parish_id
            if (Schema::hasColumn('electoral_events', 'parish_id')) {
                $this->fixForeignKey('electoral_events', 'parish_id', 'parishes', 'id', 'set null');
            }
        }

        // Corregir la tabla electoral_event_attendances si existe
        if (Schema::hasTable('electoral_event_attendances')) {
            // Corregir la clave foránea event_id
            if (Schema::hasColumn('electoral_event_attendances', 'event_id')) {
                $this->fixForeignKey('electoral_event_attendances', 'event_id', 'electoral_events', 'id', 'cascade');
            }

            // Corregir la clave foránea person_id
            if (Schema::hasColumn('electoral_event_attendances', 'person_id')) {
                $this->fixForeignKey('electoral_event_attendances', 'person_id', 'people', 'id', 'cascade');
            }

            // Corregir la clave foránea registered_by
            if (Schema::hasColumn('electoral_event_attendances', 'registered_by')) {
                $this->fixForeignKey('electoral_event_attendances', 'registered_by', 'users', 'id', 'set null');
            }
        }

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Método para corregir una clave foránea
     */
    private function fixForeignKey($table, $column, $referencedTable, $referencedColumn, $onDelete = 'cascade')
    {
        try {
            // Ejecutar SQL directo para eliminar cualquier clave foránea existente
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = '{$table}'
                AND COLUMN_NAME = '{$column}'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");

            foreach ($constraints as $constraint) {
                DB::statement("ALTER TABLE {$table} DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
            }

            // Agregar la nueva clave foránea
            DB::statement("
                ALTER TABLE {$table}
                ADD CONSTRAINT {$table}_{$column}_foreign
                FOREIGN KEY ({$column})
                REFERENCES {$referencedTable}({$referencedColumn})
                ON DELETE {$onDelete}
            ");

            // Agregar índice si no existe
            $indexName = "{$table}_{$column}_idx";
            if (!Schema::hasIndex($table, $indexName)) {
                DB::statement("CREATE INDEX {$indexName} ON {$table}({$column})");
            }

            // No registramos éxito
        } catch (\Exception $e) {
            // No registramos error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en el down para evitar eliminar las tablas
    }
};
