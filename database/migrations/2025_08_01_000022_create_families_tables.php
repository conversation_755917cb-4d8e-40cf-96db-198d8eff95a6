<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migración consolidada que crea las tablas relacionadas con familias:
     * - family_types
     * - relationships
     * - families
     * - associate_families
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar las tablas si existen
        Schema::dropIfExists('associate_families');
        Schema::dropIfExists('relationships');
        Schema::dropIfExists('families');
        Schema::dropIfExists('family_types');

        // 1. Crear la tabla family_types
        Schema::create('family_types', function (Blueprint $table) {
            $table->id();
            $table->string('description');
            $table->timestamps();
            
            // Índices para optimizar consultas
            $table->index('description', 'fam_type_desc_idx');
        });

        // 2. Crear la tabla families
        Schema::create('families', function (Blueprint $table) {
            $table->id();
            $table->string('document_type');
            $table->integer('document_number')->nullable();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('gender_code', 10)->nullable();
            $table->text('address')->nullable();
            $table->date('birth_date')->nullable();
            $table->string('home_phone')->nullable();
            $table->string('mobile_phone')->nullable();
            $table->string('contact_phone')->nullable();
            $table->string('occupation')->nullable();
            $table->integer('marital_status')->nullable();
            $table->enum('status', [1, 0])->default(1);
            $table->timestamps();
            
            // Índices para optimizar consultas
            $table->index('document_type', 'fam_doc_type_idx');
            $table->index('document_number', 'fam_doc_number_idx');
            $table->index(['first_name', 'last_name'], 'fam_name_idx');
            $table->index('gender_code', 'fam_gender_idx');
            $table->index('marital_status', 'fam_marital_idx');
            $table->index('status', 'fam_status_idx');
        });

        // 3. Crear la tabla relationships
        Schema::create('relationships', function (Blueprint $table) {
            $table->id();
            $table->string('description');
            $table->boolean('is_unique')->default(false);
            $table->foreignId('family_type_id')->constrained('family_types')->onDelete('cascade');
            $table->timestamps();
            
            // Índices para optimizar consultas
            $table->index('description', 'rel_desc_idx');
            $table->index('is_unique', 'rel_unique_idx');
            $table->index('family_type_id', 'rel_family_type_idx');
        });

        // 4. Crear la tabla associate_families
        Schema::create('associate_families', function (Blueprint $table) {
            $table->id();
            // Nota: La columna associate_id se ha comentado ya que la tabla asociados no existe más
            // $table->foreignId('associate_id')->constrained('asociados', 'idasociado')->onDelete('cascade');
            $table->foreignId('family_id')->constrained('families', 'id')->onDelete('cascade');
            $table->foreignId('family_type_id')->constrained('family_types', 'id')->onDelete('cascade');
            $table->foreignId('relationship_id')->constrained('relationships')->onDelete('cascade');
            $table->timestamps();
            
            // Índices para optimizar consultas
            $table->index('family_id', 'assoc_fam_family_idx');
            $table->index('family_type_id', 'assoc_fam_type_idx');
            $table->index('relationship_id', 'assoc_fam_rel_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('associate_families');
        Schema::dropIfExists('relationships');
        Schema::dropIfExists('families');
        Schema::dropIfExists('family_types');
    }
};
