<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar tablas dependientes si existen
        Schema::dropIfExists('campaign_details');
        Schema::dropIfExists('campaign_targets');
        Schema::dropIfExists('campaigns');

        // Crear la tabla campaigns
        Schema::create('campaigns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description');
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['active', 'inactive', 'processing', 'draft', 'scheduled', 'paused', 'completed', 'cancelled']);
            $table->timestamps();

            // Índices para optimizar consultas
            $table->index('name', 'camp_name_idx');
            $table->index('status', 'camp_status_idx');
            $table->index('start_date', 'camp_start_date_idx');
            $table->index('end_date', 'camp_end_date_idx');
        });

        // Crear la tabla campaign_targets
        Schema::create('campaign_targets', function (Blueprint $table) {
            $table->id();
            $table->string('campaign_id');
            $table->enum('target_type', ['person', 'group', 'electoral_event', 'voting_center']);
            $table->unsignedBigInteger('target_id');
            $table->enum('status', ['pending', 'sent', 'delivered', 'failed'])->default('pending');
            $table->timestamps();

            // Agregar claves foráneas
            $table->foreign('campaign_id')->references('id')->on('campaigns')->onDelete('cascade');

            // Índice para búsquedas polimórficas
            $table->index(['target_type', 'target_id'], 'ct_target_idx');
            $table->index('campaign_id', 'ct_campaign_idx');
            $table->index('status', 'ct_status_idx');
        });

        // Crear la tabla campaign_details
        Schema::create('campaign_details', function (Blueprint $table) {
            $table->id();
            $table->string('campaign_id');
            $table->unsignedBigInteger('campaign_target_id')->nullable();
            $table->string('phone_number');
            $table->text('message');
            $table->enum('status', ['pending', 'sent', 'failed', 'processing'])->default('pending');
            $table->timestamp('sent_at')->nullable();
            $table->text('response')->nullable();
            $table->timestamps();

            // Agregar claves foráneas
            $table->foreign('campaign_id')->references('id')->on('campaigns')->onDelete('cascade');
            $table->foreign('campaign_target_id')->references('id')->on('campaign_targets')->onDelete('set null');

            // Índices para optimizar consultas
            $table->index('campaign_id', 'cd_campaign_idx');
            $table->index('campaign_target_id', 'cd_target_idx');
            $table->index('status', 'cd_status_idx');
            $table->index('phone_number', 'cd_phone_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::dropIfExists('campaign_details');
        Schema::dropIfExists('campaign_targets');
        Schema::dropIfExists('campaigns');

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
};
