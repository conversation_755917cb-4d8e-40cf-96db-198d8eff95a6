<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar la tabla si ya existe
        Schema::dropIfExists('electoral_alerts');

        // Crear la tabla electoral_alerts
        Schema::create('electoral_alerts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['reminder', 'mobilization', 'emergency', 'information'])->default('information');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->dateTime('scheduled_for')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->enum('status', ['draft', 'scheduled', 'sent', 'cancelled'])->default('draft');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('group_id')->nullable()->comment('Previously referenced group1x10s table which has been removed');
            $table->unsignedBigInteger('voting_center_id')->nullable();
            $table->unsignedBigInteger('estate_id')->nullable();
            $table->unsignedBigInteger('municipality_id')->nullable();
            $table->unsignedBigInteger('parish_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('voting_center_id')->references('id')->on('electoral_voting_centers')->onDelete('set null');
            $table->foreign('estate_id')->references('id')->on('estates')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');

            // Agregar índices estandarizados
            $table->index('title', 'ea_title_idx');
            $table->index('type', 'ea_type_idx');
            $table->index('priority', 'ea_priority_idx');
            $table->index('status', 'ea_status_idx');
            $table->index('scheduled_for', 'ea_scheduled_for_idx');
            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'ea_location_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::dropIfExists('electoral_alerts');

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
};
