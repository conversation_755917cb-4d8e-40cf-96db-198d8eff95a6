<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Esta migración marca como completadas todas las migraciones redundantes
     * y elimina los archivos físicos correspondientes.
     */
    public function up(): void
    {
        // Lista de migraciones a marcar como completadas
        $migrations = [
            // Migraciones de registration_tokens
            '2025_05_13_153454_create_registration_tokens_table',
            '2025_08_01_000013_fix_registration_tokens_foreign_key',
            '2025_08_01_000014_add_foreign_key_to_registration_tokens',
            '2025_08_01_000015_recreate_registration_tokens_table',
            '2025_08_01_000016_add_foreign_key_to_registration_tokens_table',
            
            // Migraciones de familias
            '2025_04_22_142522_create_families_table',
            '2025_04_22_143314_create_family_types_table',
            '2025_04_22_145505_create_relationships_table',
            '2025_04_22_145506_create_associate_families_table',
            
            // Migraciones de geolocalización
            '2018_03_31_155523_create_countries_table',
            '2018_03_31_155614_create_estates_table',
            '2018_03_31_155645_create_municipalities_table',
            '2018_03_31_155715_create_parishes_table',
            '2018_03_31_155740_create_cities_table',
            
            // Migraciones de datos demográficos
            '2018_02_02_142907_create_genders_table',
            '2018_04_05_012124_create_marital_status_table',
            '2018_04_06_232636_create_professions_table',
            
            // Migraciones de datos financieros
            '2018_10_16_150406_create_currencies_table',
            
            // Migraciones de sistema
            '0001_01_01_000001_create_cache_table',
            '0001_01_01_000002_create_jobs_table',
            '2025_04_29_203829_create_activity_logs_table',
            '2025_05_10_000000_create_notifications_table',
            '2025_05_15_000000_create_system_logs_table',
            '2025_05_15_000002_create_audits_table',
            
            // Migraciones de compañías
            '2025_04_09_204556_create_companies_table',
        ];

        // Obtener el batch actual
        $batch = DB::table('migrations')->max('batch') + 1;

        // Marcar cada migración como completada si no lo está ya
        foreach ($migrations as $migration) {
            if (!DB::table('migrations')->where('migration', $migration)->exists()) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => $batch
                ]);
                Log::info("Migración {$migration} marcada como ejecutada.");
            }
        }

        // Lista de archivos de migración a eliminar
        $filesToRemove = [
            // Migraciones de registration_tokens
            database_path('migrations/2025_05_13_153454_create_registration_tokens_table.php'),
            database_path('migrations/2025_08_01_000013_fix_registration_tokens_foreign_key.php'),
            database_path('migrations/2025_08_01_000014_add_foreign_key_to_registration_tokens.php'),
            database_path('migrations/2025_08_01_000015_recreate_registration_tokens_table.php'),
            database_path('migrations/2025_08_01_000016_add_foreign_key_to_registration_tokens_table.php'),
            
            // Migraciones de familias
            database_path('migrations/2025_04_22_142522_create_families_table.php'),
            database_path('migrations/2025_04_22_143314_create_family_types_table.php'),
            database_path('migrations/2025_04_22_145505_create_relationships_table.php'),
            database_path('migrations/2025_04_22_145506_create_associate_families_table.php'),
            
            // Migraciones de geolocalización
            database_path('migrations/2018_03_31_155523_create_countries_table.php'),
            database_path('migrations/2018_03_31_155614_create_estates_table.php'),
            database_path('migrations/2018_03_31_155645_create_municipalities_table.php'),
            database_path('migrations/2018_03_31_155715_create_parishes_table.php'),
            database_path('migrations/2018_03_31_155740_create_cities_table.php'),
            
            // Migraciones de datos demográficos
            database_path('migrations/2018_02_02_142907_create_genders_table.php'),
            database_path('migrations/2018_04_05_012124_create_marital_status_table.php'),
            database_path('migrations/2018_04_06_232636_create_professions_table.php'),
            
            // Migraciones de datos financieros
            database_path('migrations/2018_10_16_150406_create_currencies_table.php'),
            
            // Migraciones de sistema
            database_path('migrations/0001_01_01_000001_create_cache_table.php'),
            database_path('migrations/0001_01_01_000002_create_jobs_table.php'),
            database_path('migrations/2025_04_29_203829_create_activity_logs_table.php'),
            database_path('migrations/2025_05_10_000000_create_notifications_table.php'),
            database_path('migrations/2025_05_15_000000_create_system_logs_table.php'),
            database_path('migrations/2025_05_15_000002_create_audits_table.php'),
            
            // Migraciones de compañías
            database_path('migrations/2025_04_09_204556_create_companies_table.php'),
        ];

        // Eliminar cada archivo si existe
        foreach ($filesToRemove as $file) {
            if (File::exists($file)) {
                try {
                    File::delete($file);
                    Log::info("Archivo de migración eliminado: {$file}");
                } catch (\Exception $e) {
                    Log::error("Error al eliminar el archivo de migración {$file}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No es posible restaurar los archivos eliminados
        Log::warning("No es posible restaurar los archivos de migración eliminados.");
    }
};
