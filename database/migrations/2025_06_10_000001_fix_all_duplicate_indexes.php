<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Primero, eliminamos todos los índices problemáticos
        $this->dropAllProblematicIndexes();
        
        // 2. Lu<PERSON>, creamos nuevos índices con nombres estandarizados
        $this->createStandardizedIndexes();
    }

    /**
     * Elimina todos los índices problemáticos de todas las tablas
     */
    private function dropAllProblematicIndexes(): void
    {
        // Lista de tablas y sus índices a eliminar
        $tablesToFix = [
            'electoral_events' => [
                'electoral_events_start_date_index',
                'electoral_events_status_index',
                'electoral_events_type_index',
                'electoral_events_name_index',
                'electoral_events_end_date_index',
                'electoral_events_estate_id_municipality_id_parish_id_index',
                'ee_start_date_idx',
                'ee_status_idx',
                'ee_type_idx',
                'ee_name_idx',
                'ee_end_date_idx',
                'ee_location_idx',
                'ee_location_index'
            ],
            'electoral_event_attendances' => [
                'electoral_event_attendances_event_id_person_id_index',
                'electoral_event_attendances_status_index',
                'electoral_event_attendances_event_id_index',
                'electoral_event_attendances_person_id_index'
            ],
            'electoral_mobilizations' => [
                'electoral_mobilizations_voting_center_id_index',
                'electoral_mobilizations_coordinator_id_index',
                'electoral_mobilizations_mobilization_date_index',
                'electoral_mobilizations_status_index',
                'em_voting_center_idx',
                'em_coordinator_idx',
                'em_date_idx',
                'em_status_idx',
                'em_location_idx'
            ],
            'electoral_mobilization_details' => [
                'electoral_mobilization_details_mobilization_id_person_id_index',
                'electoral_mobilization_details_status_index',
                'electoral_mobilization_details_mobilization_id_index',
                'electoral_mobilization_details_person_id_index'
            ],
            'electoral_alerts' => [
                'electoral_alerts_status_index',
                'electoral_alerts_type_index',
                'electoral_alerts_priority_index',
                'electoral_alerts_estate_id_municipality_id_parish_id_index',
                'ea_status_idx',
                'ea_type_idx',
                'ea_priority_idx',
                'ea_location_idx'
            ],
            'people' => [
                'people_cedula_index',
                'people_name_index',
                'people_is_1x10_index',
                'people_responsible_id_index',
                'people_activity_status_index',
                'people_role_index',
                'people_estate_id_municipality_id_parish_id_index',
                'people_cedula_idx',
                'people_name_idx',
                'people_is_1x10_idx',
                'people_responsible_idx',
                'people_activity_idx',
                'people_role_idx',
                'people_location_idx'
            ],
            'patrol_groups' => [
                'patrol_groups_leader_id_index',
                'patrol_groups_status_index',
                'patrol_groups_estate_id_municipality_id_parish_id_index',
                'patrol_groups_name_index',
                'patrol_groups_type_index',
                'pg_leader_idx',
                'pg_status_idx',
                'pg_location_idx'
            ],
            'patrol_group_members' => [
                'patrol_group_members_group_id_person_id_index',
                'patrol_group_members_status_index',
                'patrol_group_members_role_index',
                'patrol_group_members_joined_date_index'
            ],
            'patrol_activities' => [
                'patrol_activities_group_id_index',
                'patrol_activities_status_index',
                'patrol_activities_type_index',
                'patrol_activities_title_index',
                'patrol_activities_scheduled_date_index'
            ],
            'trackings' => [
                'trackings_person_id_index',
                'trackings_user_id_index',
                'trackings_tracking_date_index',
                'trackings_tracking_type_index',
                'trackings_status_index',
                'trackings_priority_index',
                'trackings_follow_up_date_index'
            ],
            'users' => [
                'users_username_index',
                'users_email_index',
                'users_created_at_index'
            ],
            'electoral_voting_centers' => [
                'electoral_voting_centers_name_index',
                'electoral_voting_centers_code_index',
                'electoral_voting_centers_status_index',
                'electoral_voting_centers_estate_id_municipality_id_parish_id_index'
            ]
        ];

        // Eliminar todos los índices listados
        foreach ($tablesToFix as $table => $indexes) {
            if (Schema::hasTable($table)) {
                foreach ($indexes as $index) {
                    try {
                        DB::statement("DROP INDEX IF EXISTS {$index} ON {$table}");
                    } catch (\Exception $e) {
                        // El índice podría no existir o no poder eliminarse
                        Log::info("No se pudo eliminar el índice {$index} de la tabla {$table}: " . $e->getMessage());
                    }
                }
            }
        }
    }

    /**
     * Crea índices estandarizados para todas las tablas
     */
    private function createStandardizedIndexes(): void
    {
        // Electoral Events
        if (Schema::hasTable('electoral_events')) {
            Schema::table('electoral_events', function (Blueprint $table) {
                if (Schema::hasColumn('electoral_events', 'start_date')) {
                    $this->safeCreateIndex($table, 'start_date', 'ee_start_date_idx');
                }
                if (Schema::hasColumn('electoral_events', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'ee_status_idx');
                }
                if (Schema::hasColumn('electoral_events', 'type')) {
                    $this->safeCreateIndex($table, 'type', 'ee_type_idx');
                }
                if (Schema::hasColumn('electoral_events', 'name')) {
                    $this->safeCreateIndex($table, 'name', 'ee_name_idx');
                }
                if (Schema::hasColumn('electoral_events', 'end_date')) {
                    $this->safeCreateIndex($table, 'end_date', 'ee_end_date_idx');
                }
                if (Schema::hasColumns('electoral_events', ['estate_id', 'municipality_id', 'parish_id'])) {
                    $this->safeCreateIndex($table, ['estate_id', 'municipality_id', 'parish_id'], 'ee_location_idx');
                }
            });
        }

        // Electoral Event Attendances
        if (Schema::hasTable('electoral_event_attendances')) {
            Schema::table('electoral_event_attendances', function (Blueprint $table) {
                if (Schema::hasColumn('electoral_event_attendances', 'event_id')) {
                    $this->safeCreateIndex($table, 'event_id', 'eea_event_idx');
                }
                if (Schema::hasColumn('electoral_event_attendances', 'person_id')) {
                    $this->safeCreateIndex($table, 'person_id', 'eea_person_idx');
                }
                if (Schema::hasColumns('electoral_event_attendances', ['event_id', 'person_id'])) {
                    $this->safeCreateIndex($table, ['event_id', 'person_id'], 'eea_event_person_idx');
                }
                if (Schema::hasColumn('electoral_event_attendances', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'eea_status_idx');
                }
            });
        }

        // Electoral Mobilizations
        if (Schema::hasTable('electoral_mobilizations')) {
            Schema::table('electoral_mobilizations', function (Blueprint $table) {
                if (Schema::hasColumn('electoral_mobilizations', 'voting_center_id')) {
                    $this->safeCreateIndex($table, 'voting_center_id', 'em_voting_center_idx');
                }
                if (Schema::hasColumn('electoral_mobilizations', 'coordinator_id')) {
                    $this->safeCreateIndex($table, 'coordinator_id', 'em_coordinator_idx');
                }
                if (Schema::hasColumn('electoral_mobilizations', 'mobilization_date')) {
                    $this->safeCreateIndex($table, 'mobilization_date', 'em_date_idx');
                }
                if (Schema::hasColumn('electoral_mobilizations', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'em_status_idx');
                }
                if (Schema::hasColumns('electoral_mobilizations', ['estate_id', 'municipality_id', 'parish_id'])) {
                    $this->safeCreateIndex($table, ['estate_id', 'municipality_id', 'parish_id'], 'em_location_idx');
                }
            });
        }

        // Electoral Mobilization Details
        if (Schema::hasTable('electoral_mobilization_details')) {
            Schema::table('electoral_mobilization_details', function (Blueprint $table) {
                if (Schema::hasColumn('electoral_mobilization_details', 'mobilization_id')) {
                    $this->safeCreateIndex($table, 'mobilization_id', 'emd_mobilization_idx');
                }
                if (Schema::hasColumn('electoral_mobilization_details', 'person_id')) {
                    $this->safeCreateIndex($table, 'person_id', 'emd_person_idx');
                }
                if (Schema::hasColumns('electoral_mobilization_details', ['mobilization_id', 'person_id'])) {
                    $this->safeCreateIndex($table, ['mobilization_id', 'person_id'], 'emd_mob_person_idx');
                }
                if (Schema::hasColumn('electoral_mobilization_details', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'emd_status_idx');
                }
            });
        }

        // Electoral Alerts
        if (Schema::hasTable('electoral_alerts')) {
            Schema::table('electoral_alerts', function (Blueprint $table) {
                if (Schema::hasColumn('electoral_alerts', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'ea_status_idx');
                }
                if (Schema::hasColumn('electoral_alerts', 'type')) {
                    $this->safeCreateIndex($table, 'type', 'ea_type_idx');
                }
                if (Schema::hasColumn('electoral_alerts', 'priority')) {
                    $this->safeCreateIndex($table, 'priority', 'ea_priority_idx');
                }
                if (Schema::hasColumns('electoral_alerts', ['estate_id', 'municipality_id', 'parish_id'])) {
                    $this->safeCreateIndex($table, ['estate_id', 'municipality_id', 'parish_id'], 'ea_location_idx');
                }
            });
        }

        // People
        if (Schema::hasTable('people')) {
            Schema::table('people', function (Blueprint $table) {
                if (Schema::hasColumn('people', 'cedula')) {
                    $this->safeCreateIndex($table, 'cedula', 'people_cedula_idx');
                }
                if (Schema::hasColumn('people', 'name')) {
                    $this->safeCreateIndex($table, 'name', 'people_name_idx');
                }
                if (Schema::hasColumn('people', 'is_1x10')) {
                    $this->safeCreateIndex($table, 'is_1x10', 'people_is_1x10_idx');
                }
                if (Schema::hasColumn('people', 'responsible_id')) {
                    $this->safeCreateIndex($table, 'responsible_id', 'people_responsible_idx');
                }
                if (Schema::hasColumn('people', 'activity_status')) {
                    $this->safeCreateIndex($table, 'activity_status', 'people_activity_idx');
                }
                if (Schema::hasColumn('people', 'role')) {
                    $this->safeCreateIndex($table, 'role', 'people_role_idx');
                }
                if (Schema::hasColumns('people', ['estate_id', 'municipality_id', 'parish_id'])) {
                    $this->safeCreateIndex($table, ['estate_id', 'municipality_id', 'parish_id'], 'people_location_idx');
                }
            });
        }

        // Patrol Groups
        if (Schema::hasTable('patrol_groups')) {
            Schema::table('patrol_groups', function (Blueprint $table) {
                if (Schema::hasColumn('patrol_groups', 'name')) {
                    $this->safeCreateIndex($table, 'name', 'pg_name_idx');
                }
                if (Schema::hasColumn('patrol_groups', 'leader_id')) {
                    $this->safeCreateIndex($table, 'leader_id', 'pg_leader_idx');
                }
                if (Schema::hasColumn('patrol_groups', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'pg_status_idx');
                }
                if (Schema::hasColumn('patrol_groups', 'type')) {
                    $this->safeCreateIndex($table, 'type', 'pg_type_idx');
                }
                if (Schema::hasColumns('patrol_groups', ['estate_id', 'municipality_id', 'parish_id'])) {
                    $this->safeCreateIndex($table, ['estate_id', 'municipality_id', 'parish_id'], 'pg_location_idx');
                }
            });
        }

        // Patrol Group Members
        if (Schema::hasTable('patrol_group_members')) {
            Schema::table('patrol_group_members', function (Blueprint $table) {
                if (Schema::hasColumn('patrol_group_members', 'group_id')) {
                    $this->safeCreateIndex($table, 'group_id', 'pgm_group_idx');
                }
                if (Schema::hasColumn('patrol_group_members', 'person_id')) {
                    $this->safeCreateIndex($table, 'person_id', 'pgm_person_idx');
                }
                if (Schema::hasColumns('patrol_group_members', ['group_id', 'person_id'])) {
                    $this->safeCreateIndex($table, ['group_id', 'person_id'], 'pgm_group_person_idx');
                }
                if (Schema::hasColumn('patrol_group_members', 'role')) {
                    $this->safeCreateIndex($table, 'role', 'pgm_role_idx');
                }
                if (Schema::hasColumn('patrol_group_members', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'pgm_status_idx');
                }
                if (Schema::hasColumn('patrol_group_members', 'joined_date')) {
                    $this->safeCreateIndex($table, 'joined_date', 'pgm_joined_date_idx');
                }
            });
        }

        // Patrol Activities
        if (Schema::hasTable('patrol_activities')) {
            Schema::table('patrol_activities', function (Blueprint $table) {
                if (Schema::hasColumn('patrol_activities', 'group_id')) {
                    $this->safeCreateIndex($table, 'group_id', 'pa_group_idx');
                }
                if (Schema::hasColumn('patrol_activities', 'title')) {
                    $this->safeCreateIndex($table, 'title', 'pa_title_idx');
                }
                if (Schema::hasColumn('patrol_activities', 'scheduled_date')) {
                    $this->safeCreateIndex($table, 'scheduled_date', 'pa_scheduled_date_idx');
                }
                if (Schema::hasColumn('patrol_activities', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'pa_status_idx');
                }
                if (Schema::hasColumn('patrol_activities', 'type')) {
                    $this->safeCreateIndex($table, 'type', 'pa_type_idx');
                }
            });
        }

        // Trackings
        if (Schema::hasTable('trackings')) {
            Schema::table('trackings', function (Blueprint $table) {
                if (Schema::hasColumn('trackings', 'person_id')) {
                    $this->safeCreateIndex($table, 'person_id', 'track_person_idx');
                }
                if (Schema::hasColumn('trackings', 'user_id')) {
                    $this->safeCreateIndex($table, 'user_id', 'track_user_idx');
                }
                if (Schema::hasColumn('trackings', 'tracking_date')) {
                    $this->safeCreateIndex($table, 'tracking_date', 'track_date_idx');
                }
                if (Schema::hasColumn('trackings', 'tracking_type')) {
                    $this->safeCreateIndex($table, 'tracking_type', 'track_type_idx');
                }
                if (Schema::hasColumn('trackings', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'track_status_idx');
                }
                if (Schema::hasColumn('trackings', 'priority')) {
                    $this->safeCreateIndex($table, 'priority', 'track_priority_idx');
                }
                if (Schema::hasColumn('trackings', 'follow_up_date')) {
                    $this->safeCreateIndex($table, 'follow_up_date', 'track_follow_up_idx');
                }
            });
        }

        // Users
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (Schema::hasColumn('users', 'username')) {
                    $this->safeCreateIndex($table, 'username', 'users_username_idx');
                }
                if (Schema::hasColumn('users', 'email')) {
                    $this->safeCreateIndex($table, 'email', 'users_email_idx');
                }
                if (Schema::hasColumn('users', 'created_at')) {
                    $this->safeCreateIndex($table, 'created_at', 'users_created_at_idx');
                }
            });
        }

        // Electoral Voting Centers
        if (Schema::hasTable('electoral_voting_centers')) {
            Schema::table('electoral_voting_centers', function (Blueprint $table) {
                if (Schema::hasColumn('electoral_voting_centers', 'name')) {
                    $this->safeCreateIndex($table, 'name', 'evc_name_idx');
                }
                if (Schema::hasColumn('electoral_voting_centers', 'code')) {
                    $this->safeCreateIndex($table, 'code', 'evc_code_idx');
                }
                if (Schema::hasColumn('electoral_voting_centers', 'status')) {
                    $this->safeCreateIndex($table, 'status', 'evc_status_idx');
                }
                if (Schema::hasColumns('electoral_voting_centers', ['estate_id', 'municipality_id', 'parish_id'])) {
                    $this->safeCreateIndex($table, ['estate_id', 'municipality_id', 'parish_id'], 'evc_location_idx');
                }
            });
        }
    }

    /**
     * Método auxiliar para crear índices de forma segura
     */
    private function safeCreateIndex(Blueprint $table, $columns, $indexName): void
    {
        try {
            // Verificar si el índice ya existe
            $tableName = $table->getTable();
            $indexExists = DB::select("SHOW INDEX FROM {$tableName} WHERE Key_name = '{$indexName}'");
            
            if (empty($indexExists)) {
                $table->index($columns, $indexName);
            }
        } catch (\Exception $e) {
            // Registrar el error pero continuar
            Log::warning("No se pudo crear el índice {$indexName}: " . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en el método down, ya que esta migración es para corregir problemas
    }
};
