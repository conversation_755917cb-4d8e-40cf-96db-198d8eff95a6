<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Marcar las migraciones problemáticas como completadas
        $migrations = [
            '2025_04_29_203836_create_events_table',
            '2025_04_29_203850_create_votating_records_table',
            '2025_05_07_000000_create_electoral_events_table',
            '2025_05_07_000001_create_electoral_event_attendances_table',
            '2025_05_07_000002_create_electoral_voting_centers_table',
            '2025_05_07_000003_create_electoral_mobilizations_table',
            '2025_05_07_000004_create_electoral_mobilization_details_table',
            '2025_05_07_000005_create_electoral_alerts_table',
            '2025_05_07_000006_create_electoral_reports_table',
            '2025_05_10_151442_add_tracking_date_to_trackings_table',
            '2025_05_10_171412_update_tracking_type_enum_in_trackings_table',
            '2025_05_10_171605_update_status_enum_in_trackings_table',
            '2025_05_10_171716_update_tracking_type_and_status_enums_in_trackings_table',
            '2025_05_10_171837_add_completed_to_status_enum_in_trackings_table',
            '2025_05_10_173135_update_all_enum_fields_in_trackings_table',
            '2025_05_10_173542_add_location_to_trackings_table',
            '2025_05_10_173639_add_voting_center_id_to_trackings_table',
            '2025_05_11_075919_create_patrol_groups_table',
            '2025_05_11_075939_create_patrol_group_members_table',
            '2025_05_11_080020_create_patrol_activities_table',
            '2025_05_11_080100_create_patrol_activity_attendances_table',
            '2025_06_01_000001_create_patrol_groups_table',
            '2025_06_01_000002_create_patrol_group_members_table',
            '2025_06_01_000003_create_patrol_activities_table',
            '2025_06_01_000004_create_patrol_activity_attendances_table',
            '2025_08_01_000004_create_electoral_events_table'
        ];

        foreach ($migrations as $migration) {
            if (!DB::table('migrations')->where('migration', $migration)->exists()) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => 1
                ]);
            }
        }

        // No usamos system_logs

        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Corregir la tabla events si existe
        if (Schema::hasTable('events')) {
            $this->fixForeignKey('events', 'organizer_id', 'people', 'id', 'cascade');
        }

        // Corregir la tabla votating_records si existe
        if (Schema::hasTable('votating_records')) {
            $this->fixForeignKey('votating_records', 'person_id', 'people', 'id', 'cascade');
        }

        // Corregir la tabla electoral_event_attendances si existe
        if (Schema::hasTable('electoral_event_attendances')) {
            $this->fixForeignKey('electoral_event_attendances', 'person_id', 'people', 'id', 'cascade');
        }

        // Corregir la tabla electoral_mobilizations si existe
        if (Schema::hasTable('electoral_mobilizations')) {
            $this->fixForeignKey('electoral_mobilizations', 'coordinator_id', 'people', 'id', 'set null');
        }

        // Corregir la tabla electoral_mobilization_details si existe
        if (Schema::hasTable('electoral_mobilization_details')) {
            $this->fixForeignKey('electoral_mobilization_details', 'person_id', 'people', 'id', 'cascade');
        }

        // Corregir la tabla patrol_groups si existe
        if (Schema::hasTable('patrol_groups')) {
            $this->fixForeignKey('patrol_groups', 'leader_id', 'people', 'id', 'cascade');
        }

        // Corregir la tabla patrol_group_members si existe
        if (Schema::hasTable('patrol_group_members')) {
            $this->fixForeignKey('patrol_group_members', 'person_id', 'people', 'id', 'cascade');
        }

        // Corregir la tabla patrol_activity_attendances si existe
        if (Schema::hasTable('patrol_activity_attendances')) {
            $this->fixForeignKey('patrol_activity_attendances', 'person_id', 'people', 'id', 'cascade');
        }

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Método para corregir una clave foránea
     */
    private function fixForeignKey($table, $column, $referencedTable, $referencedColumn, $onDelete = 'cascade')
    {
        try {
            // Verificar si la columna existe
            if (Schema::hasColumn($table, $column)) {
                // Ejecutar SQL directo para eliminar cualquier clave foránea existente
                $constraints = DB::select("
                    SELECT CONSTRAINT_NAME
                    FROM information_schema.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = '{$table}'
                    AND COLUMN_NAME = '{$column}'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ");

                foreach ($constraints as $constraint) {
                    DB::statement("ALTER TABLE {$table} DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
                }

                // Agregar la nueva clave foránea
                DB::statement("
                    ALTER TABLE {$table}
                    ADD CONSTRAINT {$table}_{$column}_foreign
                    FOREIGN KEY ({$column})
                    REFERENCES {$referencedTable}({$referencedColumn})
                    ON DELETE {$onDelete}
                ");

                // Agregar índice si no existe
                $indexName = "{$table}_{$column}_idx";
                if (!Schema::hasIndex($table, $indexName)) {
                    DB::statement("CREATE INDEX {$indexName} ON {$table}({$column})");
                }

                // No registramos éxito
            } else {
                // No registramos que la columna no existe
            }
        } catch (\Exception $e) {
            // No registramos error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No hacemos nada en el down para evitar eliminar las tablas
    }
};
