<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('people', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('name');
            $table->bigInteger('cedula')->unique();
            $table->string('phone');
            $table->string('email')->nullable();
            $table->text('address')->nullable();
            $table->string('polling_center')->nullable();
            $table->unsignedBigInteger('estate_id')->nullable();
            $table->unsignedBigInteger('municipality_id')->nullable();
            $table->unsignedBigInteger('parish_id')->nullable();
            $table->enum('role', ['Militante', 'Votante']);
            $table->string('party')->default('PSUV');
            $table->enum('activity_status', ['Activo', 'Inactivo'])->default('Activo');
            $table->boolean('is_1x10')->default(false);
            $table->unsignedBigInteger('responsible_id')->nullable();
            $table->text('observations')->nullable();
            $table->timestamp('registration_date')->useCurrent();
            $table->timestamps();
            $table->softDeletes();

            // Agregar claves foráneas
            $table->foreign('estate_id')->references('id')->on('estates')->onDelete('set null');
            $table->foreign('municipality_id')->references('id')->on('municipalities')->onDelete('set null');
            $table->foreign('parish_id')->references('id')->on('parishes')->onDelete('set null');
            $table->foreign('responsible_id')->references('id')->on('people')->onDelete('set null');

            // Índices
            $table->index('cedula', 'people_cedula_idx');
            $table->index('name', 'people_name_idx');
            $table->index('is_1x10', 'people_is_1x10_idx');
            $table->index('responsible_id', 'people_responsible_idx');
            $table->index('activity_status', 'people_activity_idx');
            $table->index('role', 'people_role_idx');
            $table->index(['estate_id', 'municipality_id', 'parish_id'], 'people_location_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('people');
    }
};
