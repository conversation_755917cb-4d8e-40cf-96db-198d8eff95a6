<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migración consolidada que crea las tablas relacionadas con el sistema:
     * - cache
     * - cache_locks
     * - jobs
     * - job_batches
     * - failed_jobs
     * - activity_logs
     * - notifications
     * - system_logs
     * - audits
     */
    public function up(): void
    {
        // Desactivar temporalmente las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Eliminar las tablas si existen
        Schema::dropIfExists('audits');
        Schema::dropIfExists('system_logs');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('activity_logs');
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('cache_locks');
        Schema::dropIfExists('cache');

        // 1. Crear las tablas de caché
        Schema::create('cache', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->mediumText('value');
            $table->integer('expiration');
            
            // Índices para optimizar consultas
            $table->index('expiration', 'cache_exp_idx');
        });

        Schema::create('cache_locks', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->string('owner');
            $table->integer('expiration');
            
            // Índices para optimizar consultas
            $table->index('owner', 'cache_lock_owner_idx');
            $table->index('expiration', 'cache_lock_exp_idx');
        });

        // 2. Crear las tablas de trabajos
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
            
            // Índices para optimizar consultas
            $table->index(['queue', 'reserved_at'], 'jobs_queue_reserved_idx');
            $table->index(['queue', 'available_at'], 'jobs_queue_available_idx');
            $table->index('created_at', 'jobs_created_idx');
        });

        Schema::create('job_batches', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->integer('total_jobs');
            $table->integer('pending_jobs');
            $table->integer('failed_jobs');
            $table->longText('failed_job_ids');
            $table->mediumText('options')->nullable();
            $table->integer('cancelled_at')->nullable();
            $table->integer('created_at');
            $table->integer('finished_at')->nullable();
            
            // Índices para optimizar consultas
            $table->index('name', 'job_batch_name_idx');
            $table->index('created_at', 'job_batch_created_idx');
            $table->index('finished_at', 'job_batch_finished_idx');
        });

        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
            
            // Índices para optimizar consultas
            $table->index('uuid', 'failed_jobs_uuid_idx');
            $table->index('failed_at', 'failed_jobs_failed_at_idx');
        });

        // 3. Crear la tabla activity_logs
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->text('action');
            $table->timestamp('date')->useCurrent();
            $table->timestamps();
            
            // Índices para optimizar consultas
            $table->index('user_id', 'activity_user_idx');
            $table->index('date', 'activity_date_idx');
        });

        // 4. Crear la tabla notifications
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['info', 'success', 'warning', 'error'])->default('info');
            $table->string('link')->nullable();
            $table->json('data')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Índices para optimizar consultas
            $table->index('user_id', 'notif_user_idx');
            $table->index('type', 'notif_type_idx');
            $table->index('read_at', 'notif_read_idx');
            $table->index('created_at', 'notif_created_idx');
        });

        // 5. Crear la tabla system_logs
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            $table->string('level')->index();
            $table->text('message');
            $table->json('context')->nullable();
            $table->timestamp('created_at')->index();
            
            // Índices para optimizar consultas
            $table->index('level', 'sys_log_level_idx');
            $table->index('created_at', 'sys_log_created_idx');
        });

        // 6. Crear la tabla audits
        Schema::create('audits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->string('action')->index();
            $table->string('entity')->index();
            $table->string('entity_id')->index();
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->text('notes')->nullable();
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamp('created_at')->index();
            
            // Índices para optimizar consultas
            $table->index('user_id', 'audit_user_idx');
            $table->index('action', 'audit_action_idx');
            $table->index('entity', 'audit_entity_idx');
            $table->index('entity_id', 'audit_entity_id_idx');
            $table->index('created_at', 'audit_created_idx');
        });

        // Reactivar las restricciones de clave foránea
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audits');
        Schema::dropIfExists('system_logs');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('activity_logs');
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('cache_locks');
        Schema::dropIfExists('cache');
    }
};
