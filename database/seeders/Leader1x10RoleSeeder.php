<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class Leader1x10RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear el rol de líder 1x10 si no existe
        $role = Role::firstOrCreate(['name' => '1x10_leader']);

        // Permisos básicos para el líder 1x10
        $permissions = [
            'view dashboard',
            'manage own group',
            'view own members',
            'add members',
            'edit members',
            'remove members',
            'view electoral events',
            'participate in mobilizations',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Asignar permisos al rol
        $role->syncPermissions($permissions);

        $this->command->info('Rol de líder 1x10 creado con éxito.');
    }
}
