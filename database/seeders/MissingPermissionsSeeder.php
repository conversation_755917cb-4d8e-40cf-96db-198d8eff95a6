<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class MissingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos para el módulo de configuración
            'manage settings',
            'view company',
            'update company',

            // Permisos para el módulo de reportes de campañas
            'view campaign reports',
            'export campaign reports',

            // Permisos para el módulo de reportes electorales
            'view electoral reports',
            'export electoral reports',

            // Permisos para el módulo de comunicación
            'view communication',
            'create communication',
            'update communication',
            'delete communication',
            'send messages',
            'view message history',
            'create reminders',
            'view reminders',

            // Permisos para el módulo de material de campaña
            'view material',
            'create material',
            'update material',
            'delete material',
            'manage distribution',
            'manage stock',

            // Permisos para el módulo de contactos
            'view contacts',
            'create contacts',
            'update contacts',
            'delete contacts',

            // Permisos para el frontend
            'manage own 1x10 group',
            'view own 1x10 members',
            'add own 1x10 members',
            'edit own 1x10 members',
            'remove own 1x10 members',

            // Permisos para integración
            'view integration',
            'manage integration',
            'configure integration',

            // Permisos para exportación e importación
            'import data',
            'export data',

            // Permisos para estadísticas y dashboards
            'view statistics',
            'view dashboards',

            // Permisos para la gestión de ubicaciones
            'manage locations',
            'view estates',
            'create estates',
            'update estates',
            'delete estates',
            'view municipalities',
            'create municipalities',
            'update municipalities',
            'delete municipalities',
            'view parishes',
            'create parishes',
            'update parishes',
            'delete parishes',

            // Permisos para la gestión de centros de votación
            'view voting centers',
            'create voting centers',
            'update voting centers',
            'delete voting centers',

            // Permisos para la gestión de movilización
            'view mobilization',
            'create mobilization',
            'update mobilization',
            'delete mobilization',
            'manage mobilization',

            // Permisos para la gestión de eventos electorales
            'view electoral events',
            'create electoral events',
            'update electoral events',
            'delete electoral events',
            'manage electoral events',

            // Permisos para la gestión de asistencia a eventos
            'view event attendance',
            'create event attendance',
            'update event attendance',
            'delete event attendance',
            'manage event attendance',

            // Permisos para la gestión de seguimiento
            'view tracking contacts',
            'create tracking contacts',
            'update tracking contacts',
            'delete tracking contacts',

            // Permisos para la gestión de marcado de votantes
            'view voter marking',
            'create voter marking',
            'update voter marking',
            'delete voter marking',

            // Permisos para la gestión de historial de seguimiento
            'view tracking history',
            'export tracking history',

            // Permisos para la gestión de reportes de seguimiento
            'view tracking reports',
            'export tracking reports',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (! $exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        $this->command->info("Se han creado $count permisos nuevos.");
    }
}
