<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CampaignRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear rol de administrador de campañas
        $role = Role::query()->updateOrCreate(['name' => 'Campaign Manager']);

        // Asignar todos los permisos relacionados con campañas
        $permissions = Permission::query()
            ->where(function ($query) {
                $query->where('name', 'like', '%campaign%')
                    ->orWhere('name', 'like', '%message%');
            })
            ->pluck('name')
            ->toArray();

        $role->syncPermissions($permissions);

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::query()->where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }
    }
}
