<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LocationPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos para la gestión de ubicaciones
            'manage locations',
            'view estates',
            'create estates',
            'update estates',
            'delete estates',
            'view municipalities',
            'create municipalities',
            'update municipalities',
            'delete municipalities',
            'view parishes',
            'create parishes',
            'update parishes',
            'delete parishes',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos de ubicaciones nuevos.");
        }
    }
}
