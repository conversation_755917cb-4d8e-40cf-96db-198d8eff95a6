<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use <PERSON><PERSON>\Permission\Models\Role;

class FrontendPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos para el frontend (líderes 1x10)
            'manage own 1x10 group',
            'view own 1x10 members',
            'add own 1x10 members',
            'edit own 1x10 members',
            'remove own 1x10 members',
            'view dashboard',
            'manage own group',
            'view own members',
            'add members',
            'edit members',
            'remove members',
            'participate in mobilizations',

        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        // Asignar permisos al rol de líder 1x10
        $leaderRole = Role::where('name', '1x10_leader')->first();
        if ($leaderRole) {
            $leaderRole->givePermissionTo([
                'manage own 1x10 group',
                'view own 1x10 members',
                'add own 1x10 members',
                'edit own 1x10 members',
                'remove own 1x10 members',
                'view dashboard',
                'manage own group',
                'view own members',
                'add members',
                'edit members',
                'remove members',
                'participate in mobilizations',
            ]);
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos de frontend nuevos.");
        }
    }
}
