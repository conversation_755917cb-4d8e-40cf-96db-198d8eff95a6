<?php

namespace Database\Seeders;

use App\Models\Relationship;
use Illuminate\Database\Seeder;

class RelationshipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $relationships = [
            [
                'id' => 1,
                'description' => 'ESPOSO (A)',
                'is_unique' => 1,
                'family_type_id' => 1,
            ],
            [
                'id' => 2,
                'description' => 'CONCUBINO (A)',
                'is_unique' => 1,
                'family_type_id' => 1,
            ],
            [
                'id' => 3,
                'description' => 'MADRE',
                'is_unique' => 1,
                'family_type_id' => 1,
            ],
            [
                'id' => 4,
                'description' => 'PADRE',
                'is_unique' => 1,
                'family_type_id' => 1,
            ],
            [
                'id' => 5,
                'description' => 'HIJO(A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 6,
                'description' => 'ABUELO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 7,
                'description' => 'HERMANO(A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 8,
                'description' => 'TIO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 9,
                'description' => 'SOBRINO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 10,
                'description' => 'HIJASTRO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 11,
                'description' => 'NIETO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 12,
                'description' => 'SUEGRO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 13,
                'description' => 'HIJO(A) - ADICIONAL',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 14,
                'description' => 'GUARDIA Y CUSTODIA',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 15,
                'description' => 'CUÑADO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 16,
                'description' => 'YERNO / NUERA',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 17,
                'description' => 'PRIMO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 18,
                'description' => 'EX ESPOSO (A)',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 19,
                'description' => 'MADRASTRA',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 20,
                'description' => 'PADRASTRO',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 21,
                'description' => 'MADRINA / PADRINO',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
            [
                'id' => 99,
                'description' => 'NO DEFINIDO',
                'is_unique' => 0,
                'family_type_id' => 2,
            ],
        ];

        foreach ($relationships as $relationship) {
            Relationship::updateOrCreate(
                ['id' => $relationship['id']],
                $relationship
            );
        }
    }
}
