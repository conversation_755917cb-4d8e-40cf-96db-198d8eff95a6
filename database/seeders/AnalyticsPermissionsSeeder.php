<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AnalyticsPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos básicos para el módulo de analytics
            'view analytics',
            'export analytics',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::query()->where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        // Asignar permisos al rol Admin
        $adminRole = Role::query()->where('name', 'Admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo('view analytics');
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos de analytics nuevos.");
        }
    }
}
