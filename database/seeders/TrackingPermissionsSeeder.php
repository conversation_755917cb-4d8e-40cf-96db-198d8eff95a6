<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TrackingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos básicos para el módulo de seguimiento
            'view tracking',
            'create tracking',
            'update tracking',
            'delete tracking',

            // Permisos específicos para funcionalidades de seguimiento
            'mobilize voters',
            'mark voters',
            'view tracking history',
            'view tracking reports',
            'export tracking data',
            'export tracking history',
            'export tracking reports',

            // Permisos para contactos de seguimiento
            'view tracking contacts',
            'create tracking contacts',
            'update tracking contacts',
            'delete tracking contacts',

            // Permisos para marcado de votantes
            'view voter marking',
            'create voter marking',
            'update voter marking',
            'delete voter marking',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos de seguimiento nuevos.");
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::query()->where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }
    }
}
