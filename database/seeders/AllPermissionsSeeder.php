<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spa<PERSON>\Permission\Models\Role;

class
AllPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos generales del sistema
            'access dashboard',
            'impersonate',
            'manage settings',
            'view company',
            'update company',
            'import data',
            'export data',
            'view statistics',
            'view dashboards',

            // Permisos para usuarios
            'view users',
            'create users',
            'update users',
            'delete users',
            'edit users',

            // Permisos para roles
            'view roles',
            'create roles',
            'update roles',
            'delete roles',
            'edit roles',

            // Permisos para permisos
            'view permissions',
            'create permissions',
            'update permissions',
            'delete permissions',
            'edit permissions',

            // Permisos para asociados
            'view associates',
            'create associates',
            'update associates',
            'delete associates',
            'edit associates',

            // Permisos para personas
            'view persons',
            'create persons',
            'update persons',
            'delete persons',
            'edit persons',
            'import persons',
            'export persons',
            'assign 1x10',
            'search persons',
            'view person statistics',

            // Permisos para el módulo electoral
            'view electoral',
            'create electoral',
            'update electoral',
            'delete electoral',
            'view electoral events',
            'create electoral events',
            'update electoral events',
            'delete electoral events',
            'manage electoral events',
            'manage electoral event attendance',
            'view voting centers',
            'create voting centers',
            'update voting centers',
            'delete voting centers',
            'view electoral mobilization',
            'create electoral mobilization',
            'update electoral mobilization',
            'delete electoral mobilization',
            'manage electoral mobilization',
            'manage electoral mobilization details',
            'view mobilization map',
            'view voting tracker',
            'update voting tracker',
            'manage voting tracker',
            'view electoral alerts',
            'create electoral alerts',
            'update electoral alerts',
            'delete electoral alerts',
            'send electoral alerts',
            'view electoral reports',
            'create electoral reports',
            'export electoral reports',
            'delete electoral reports',
            'view electoral dashboard',
            'manage electoral settings',

            // Permisos para eventos electorales
            'view event attendance',
            'create event attendance',
            'update event attendance',
            'delete event attendance',
            'manage event attendance',

            // Permisos para el módulo de patrullaje
            'view patrol',
            'create patrol',
            'update patrol',
            'delete patrol',
            'manage patrol members',
            'manage patrol activities',
            'view patrol reports',
            'export patrol data',

            // Permisos para el módulo de seguimiento
            'view tracking',
            'create tracking',
            'update tracking',
            'delete tracking',
            'mobilize voters',
            'mark voters',
            'view tracking history',
            'view tracking reports',
            'export tracking data',
            'view tracking contacts',
            'create tracking contacts',
            'update tracking contacts',
            'delete tracking contacts',
            'view voter marking',
            'create voter marking',
            'update voter marking',
            'delete voter marking',
            'export tracking history',
            'export tracking reports',

            // Permisos para el módulo de analítica
            'view analytics',
            'export analytics',

            // Permisos para el módulo de campañas
            'view campaigns',
            'create campaigns',
            'update campaigns',
            'delete campaigns',
            'edit campaigns',
            'import campaign contacts',
            'export campaign data',
            'send campaign messages',
            'schedule campaigns',
            'view campaign statistics',
            'view message templates',
            'create message templates',
            'update message templates',
            'delete message templates',
            'configure campaign settings',
            'manage campaign providers',
            'view campaign reports',
            'export campaign reports',

            // Permisos para el módulo de comunicación
            'view communication',
            'create communication',
            'update communication',
            'delete communication',
            'send messages',
            'view message history',
            'create reminders',
            'view reminders',

            // Permisos para el módulo de material de campaña
            'view material',
            'create material',
            'update material',
            'delete material',
            'manage distribution',
            'manage stock',

            // Permisos para el módulo de contactos
            'view contacts',
            'create contacts',
            'update contacts',
            'delete contacts',

            // Permisos para el frontend (líderes 1x10)
            'manage own 1x10 group',
            'view own 1x10 members',
            'add own 1x10 members',
            'edit own 1x10 members',
            'remove own 1x10 members',
            'view dashboard',
            'manage own group',
            'view own members',
            'add members',
            'edit members',
            'remove members',
            'participate in mobilizations',

            // Permisos para integración
            'view integration',
            'manage integration',
            'configure integration',

            // Permisos para la gestión de ubicaciones
            'manage locations',
            'view estates',
            'create estates',
            'update estates',
            'delete estates',
            'view municipalities',
            'create municipalities',
            'update municipalities',
            'delete municipalities',
            'view parishes',
            'create parishes',
            'update parishes',
            'delete parishes',

            // Permisos para la gestión de movilización
            'view mobilization',
            'create mobilization',
            'update mobilization',
            'delete mobilization',
            'manage mobilization',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos nuevos.");
            $this->command->info("Total de permisos en el sistema: " . Permission::count());
        }
    }
}
