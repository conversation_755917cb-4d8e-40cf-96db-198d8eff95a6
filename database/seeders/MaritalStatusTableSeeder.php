<?php

namespace Database\Seeders;

use App\Models\MaritalStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class MaritalStatusTableSeeder extends Seeder
{
    protected $count;

    protected $countP;

    public function __construct()
    {
        $this->count = 0;
        $this->countP = 0;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        $maritalStatuses = [
            'Soltero(a)',
            'Casado(a)',
            'Divorciado(a)',
            'Viudo(a)',
            'Unión Libre',
            'Separado(a)',
            'Comprometido(a)',
            'Concubinato',
        ];

        foreach ($maritalStatuses as $status) {
            MaritalStatus::withTrashed()->updateOrCreate(
                ['name' => $status],
                ['deleted_at' => null]
            );
        }
    }
}
