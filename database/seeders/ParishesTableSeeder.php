<?php

namespace Database\Seeders;

use App\Models\Municipality;
use App\Models\Parish;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class ParishesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        $filename = base_path('database/seeders/Data/parishes.csv');

        if (! file_exists($filename) || ! is_readable($filename)) {
            return false;
        }
        $csvFile = fopen($filename, 'r');
        $count = 0;
        $firstline = true;
        while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
            if (! $firstline) {
                $parish = Parish::where('code', $data[1])->first();
                if (! $parish) {
                    $mun = Municipality::where('code', substr($data[1], 0, 4))->first();
                    if ($mun) {
                        Parish::create(
                            [
                                'code' => $data[1], 'name' => $data[0], 'municipality_id' => $mun->id,
                                'created_at' => Carbon::now(),
                            ]
                        );
                        $count++;
                    }
                } elseif ($parish->name !== $data[0]) {
                    $parish->update(
                        ['name' => $data[0]]
                    );
                }
            }
            $firstline = false;
        }

    }
}
