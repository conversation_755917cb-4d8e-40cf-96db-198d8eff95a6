<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [

            'access dashboard',

            'impersonate',

            'view users',
            'create users',
            'update users',
            'delete users',
            'edit users',

            'view roles',
            'create roles',
            'update roles',
            'delete roles',
            'edit roles',

            'view permissions',
            'create permissions',
            'update permissions',
            'delete permissions',
            'edit permissions',

            'view associates',
            'create associates',
            'update associates',
            'delete associates',
            'edit associates',
        ];

        foreach ($permissions as $permission) {
            Permission::query()->updateOrCreate([
                'name' => $permission,
            ]);
        }

    }
}
