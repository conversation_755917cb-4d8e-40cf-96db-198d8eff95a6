<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FixMigrationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the current batch number
        $batch = DB::table('migrations')->max('batch') + 1;

        // Lista completa de migraciones antiguas a marcar como completadas
        $migrations = [
            // Migraciones de people
            '2025_04_29_203808_create_people_table',
            '2025_05_07_000012_add_user_id_to_people_table',
            '2025_05_10_130053_change_identification_to_cedula_in_people_table',
            '2025_05_10_131057_change_cedula_to_bigint_in_people_table',
            '2023_07_15_000000_modify_identification_field_in_people_table',
            '2025_07_01_000001_consolidated_create_people_table',

            // Migraciones de trackings
            '2025_04_29_203817_create_trackings_table',
            '2025_04_30_000000_create_trackings_table',
            '2025_05_06_190734_add_deleted_at_to_trackings_table',
            '2025_05_06_191036_add_tracking_type_to_trackings_table',
            '2025_05_06_191110_add_user_id_to_trackings_table',
            '2025_05_07_000013_add_additional_fields_to_trackings_table',
            '2025_05_10_200000_add_trackable_columns_to_trackings_table',
            '2025_07_01_000002_consolidated_create_trackings_table',
            '2025_05_10_171837_add_completed_to_status_enum_in_trackings_table',

            // Migraciones de users
            '2025_05_15_000001_add_two_factor_auth_to_users_table',
            '2025_05_10_150001_modify_users_table',
            '2025_07_01_000003_consolidated_modify_users_table',
            '2025_08_01_000003_modify_users_table',
            '2025_08_01_000010_remove_asociado_idasociado_from_users_table',
            '2025_08_01_000020_remove_redundant_user_migrations',

            // Migraciones de registration_tokens
            '2025_05_13_153454_create_registration_tokens_table',
            '2025_08_01_000013_fix_registration_tokens_foreign_key',
            '2025_08_01_000014_add_foreign_key_to_registration_tokens',
            '2025_08_01_000015_recreate_registration_tokens_table',
            '2025_08_01_000016_add_foreign_key_to_registration_tokens_table',
            '2025_08_01_000021_create_registration_tokens_table',

            // Migraciones de familias
            '2025_04_22_142522_create_families_table',
            '2025_04_22_143314_create_family_types_table',
            '2025_04_22_145505_create_relationships_table',
            '2025_04_22_145506_create_associate_families_table',
            '2025_08_01_000022_create_families_tables',

            // Migraciones de geolocalización
            '2018_03_31_155523_create_countries_table',
            '2018_03_31_155614_create_estates_table',
            '2018_03_31_155645_create_municipalities_table',
            '2018_03_31_155715_create_parishes_table',
            '2018_03_31_155740_create_cities_table',
            '2025_08_01_000023_create_geolocation_tables',

            // Migraciones de datos demográficos
            '2018_02_02_142907_create_genders_table',
            '2018_04_05_012124_create_marital_status_table',
            '2018_04_06_232636_create_professions_table',
            '2025_08_01_000024_create_demographic_tables',

            // Migraciones de datos financieros
            '2018_10_16_150406_create_currencies_table',
            '2025_08_01_000025_create_financial_tables',

            // Migraciones de sistema
            '0001_01_01_000001_create_cache_table',
            '0001_01_01_000002_create_jobs_table',
            '2025_04_29_203829_create_activity_logs_table',
            '2025_05_10_000000_create_notifications_table',
            '2025_05_15_000000_create_system_logs_table',
            '2025_05_15_000002_create_audits_table',
            '2025_08_01_000026_create_system_tables',

            // Migraciones de compañías
            '2025_04_09_204556_create_companies_table',
            '2025_08_01_000027_create_companies_table',

            // Migración para eliminar todas las migraciones redundantes
            '2025_08_01_000030_remove_all_redundant_migrations',

            // Migraciones de patrol groups
            '2025_05_11_075919_create_patrol_groups_table',
            '2025_05_11_075939_create_patrol_group_members_table',
            '2025_05_11_080020_create_patrol_activities_table',
            '2025_05_11_080100_create_patrol_activity_attendances_table',
            '2025_06_01_000001_create_patrol_groups_table',
            '2025_06_01_000002_create_patrol_group_members_table',
            '2025_06_01_000003_create_patrol_activities_table',
            '2025_06_01_000004_create_patrol_activity_attendances_table',
            '2025_07_01_000007_consolidated_create_patrol_tables',

            // Migraciones de campaigns
            '2025_04_10_195628_create_campaigns_table',
            '2025_04_10_195657_create_campaign_details_table',
            '2025_05_07_000010_create_campaign_targets_table',
            '2025_07_01_000008_consolidated_create_campaign_tables',

            // Migraciones de events
            '2025_04_29_203836_create_events_table',

            // Migraciones de electoral_events
            '2025_06_15_000001_fix_electoral_events_table',
            '2025_06_20_000001_recreate_electoral_events_table',
            '2025_06_25_000001_drop_and_recreate_electoral_events_table',
            '2025_06_26_000001_fix_electoral_events_with_raw_sql',
            '2025_07_01_000004_consolidated_create_electoral_events_table',

            // Migraciones de índices
            '2025_05_15_000001_add_indexes_to_tables',
            '2025_05_20_000001_add_performance_indexes',
            '2025_05_30_000001_add_missing_indexes',
            '2025_05_11_080311_fix_long_index_names',
            '2025_05_11_081059_fix_duplicate_indexes',

            // Otras migraciones de corrección
            '2025_05_11_081516_fix_electoral_events_indexes',
            '2025_05_11_081737_fix_all_migration_issues',
            '2025_05_11_082116_mark_pending_migrations_as_completed',
            '2025_05_10_160000_remove_old_migrations',
            '2025_06_12_000001_fix_migration_files_for_duplicate_indexes',
            '2025_05_15_000002_create_audits_table',
        ];

        // Insertar cada migración como completada si no existe ya
        foreach ($migrations as $migration) {
            // Verificar si la migración ya existe
            $exists = DB::table('migrations')->where('migration', $migration)->exists();

            if (!$exists) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => $batch,
                ]);
                $this->command->info("Migración {$migration} marcada como ejecutada.");
            }
        }

        $this->command->info('All pending migrations have been marked as completed.');
    }
}
