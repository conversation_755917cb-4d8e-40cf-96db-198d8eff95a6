<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ElectoralRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear rol de administrador electoral
        $role = Role::query()->updateOrCreate(['name' => 'Electoral Manager']);

        // Asignar todos los permisos relacionados con el módulo electoral
        $permissions = Permission::query()
            ->where(function ($query) {
                $query->where('name', 'like', '%electoral%')
                    ->orWhere('name', 'like', '%voting%');
            })
            ->pluck('name')
            ->toArray();

        $role->syncPermissions($permissions);

        // Crear rol de coordinador electoral (con permisos limitados)
        $coordinatorRole = Role::query()->updateOrCreate(['name' => 'Electoral Coordinator']);

        // Asignar permisos básicos para coordinadores
        $coordinatorPermissions = [
            'view electoral',
            'view electoral events',
            'view electoral mobilization',
            'create electoral mobilization',
            'update electoral mobilization',
            'manage electoral mobilization details',
            'view mobilization map',
            'view voting tracker',
            'update voting tracker',
            'view electoral reports',
        ];

        $coordinatorRole->syncPermissions($coordinatorPermissions);

        // Crear rol de operador electoral (con permisos muy limitados)
        $operatorRole = Role::query()->updateOrCreate(['name' => 'Electoral Operator']);

        // Asignar permisos básicos para operadores
        $operatorPermissions = [
            'view electoral',
            'view electoral events',
            'view electoral mobilization',
            'view mobilization map',
            'view voting tracker',
        ];

        $operatorRole->syncPermissions($operatorPermissions);
    }
}
