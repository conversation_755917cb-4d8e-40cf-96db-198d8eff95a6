<?php

namespace Database\Seeders;

use App\Models\Gender;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class GendersTableSeeder extends Seeder
{
    public function run()
    {
        Model::unguard();

        $genders = [
            ['name' => 'Masculino'],
            ['name' => 'Femenino'],
        ];

        foreach ($genders as $gender) {
            Gender::updateOrCreate(['name' => $gender['name']]);
        }
    }
}
