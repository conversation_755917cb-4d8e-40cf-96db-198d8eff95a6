<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        $this->call([

            // Permisos
            PermissionSeeder::class,
            GeneralPermissionsSeeder::class,
            UserPermissionsSeeder::class,
            RolePermissionsSeeder::class,
            PermissionPermissionsSeeder::class,
            AssociatePermissionsSeeder::class,
            PersonPermissionsSeeder::class,
            ElectoralPermissionsSeeder::class,
            PatrolPermissionsSeeder::class,
            TrackingPermissionsSeeder::class,
            AnalyticsPermissionsSeeder::class,
            CampaignPermissionsSeeder::class,
            CommunicationPermissionsSeeder::class,
            MaterialPermissionsSeeder::class,
            ContactPermissionsSeeder::class,
            FrontendPermissionsSeeder::class,
            IntegrationPermissionsSeeder::class,
            LocationPermissionsSeeder::class,
            MobilizationPermissionsSeeder::class,
            // Group1x10PermissionsSeeder::class, // Eliminado
            MissingPermissionsSeeder::class,
            AllPermissionsSeeder::class, // Todos los permisos del sistema

            // Roles
            RoleSeeder::class,
            CampaignRoleSeeder::class,
            ElectoralRoleSeeder::class,
            Leader1x10RoleSeeder::class,
            AssignMissingPermissionsToRolesSeeder::class,

            // Usuarios
            UserSeeder::class,

            // FAMILIARES
            FamilyTypeSeeder::class,
            RelationshipSeeder::class,

            // Demographic Data
            GendersTableSeeder::class,
            MaritalStatusTableSeeder::class,

            // Geopolitical Data
            // Geopolitical Data
            CountriesTableSeeder::class,
            EstatesTableSeeder::class,
            MunicipalitiesTableSeeder::class,
            CitiesTableSeeder::class,
            ParishesTableSeeder::class,

            // Financial Data
            CurrenciesTableSeeder::class,

            // Professional Data
            ProfessionsTableSeeder::class,

            PersonsPermissionsSeeder::class,
        ]);
    }
}
