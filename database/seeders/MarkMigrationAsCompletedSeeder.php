<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MarkMigrationAsCompletedSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Verificar si la migración ya está marcada como completada
        if (!DB::table('migrations')->where('migration', '2025_05_13_153454_create_registration_tokens_table')->exists()) {
            // Marcar la migración como completada
            DB::table('migrations')->insert([
                'migration' => '2025_05_13_153454_create_registration_tokens_table',
                'batch' => DB::table('migrations')->max('batch')
            ]);
            
            $this->command->info('Migración marcada como completada.');
        } else {
            $this->command->info('La migración ya estaba marcada como completada.');
        }
    }
}
