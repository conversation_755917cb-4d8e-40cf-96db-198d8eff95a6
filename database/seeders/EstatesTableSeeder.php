<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\Estate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

/**
 * @class EstatesTableSeeder
 *
 * @brief Información por defecto para Estados
 *
 * Gestiona la información por defecto a registrar inicialmente para las Estados
 *
 * <AUTHOR> <PERSON><PERSON><PERSON> <<EMAIL>> | <<EMAIL>>
 * @license
 *      [LICENCIA DE SOFTWARE CENDITEL](http://conocimientolibre.cenditel.gob.ve/licencia-de-software-v-1-3/)
 */
class EstatesTableSeeder extends Seeder
{
    protected $count;

    protected $countP;

    public function __construct()
    {
        $this->count = 0;
        $this->countP = 0;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        $country_default = Country::where('name', 'Venezuela')->first();

        $estates = [
            '01' => 'Distrito Capital',
            '02' => 'Amazonas',
            '03' => 'Anzoategui',
            '04' => 'Apure',
            '05' => 'Aragua',
            '06' => 'Barinas',
            '07' => 'Bolívar',
            '08' => 'Carabobo',
            '09' => 'Cojedes',
            '10' => 'Delta Amacuro',
            '11' => 'Falcón',
            '12' => 'Guárico',
            '13' => 'Lara',
            '14' => 'Mérida',
            '15' => 'Miranda',
            '16' => 'Monagas',
            '17' => 'Nueva Esparta',
            '18' => 'Portuguesa',
            '19' => 'Sucre',
            '20' => 'Táchira',
            '21' => 'Trujillo',
            '22' => 'Yaracuy',
            '23' => 'Zulia',
            '24' => 'Vargas',
        ];

        foreach ($estates as $code => $state) {
            Estate::withTrashed()->updateOrCreate(
                ['code' => $code],
                ['name' => $state, 'country_id' => $country_default->id, 'deleted_at' => null]
            );
            $this->count++;
        }

    }
}
