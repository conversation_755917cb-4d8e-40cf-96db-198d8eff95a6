<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spa<PERSON>\Permission\Models\Permission;
use <PERSON><PERSON>\Permission\Models\Role;

class ElectoralPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos básicos para el módulo electoral
            'view electoral',
            'create electoral',
            'update electoral',
            'delete electoral',

            // Permisos para eventos electorales
            'view electoral events',
            'create electoral events',
            'update electoral events',
            'delete electoral events',
            'manage electoral events',
            'manage electoral event attendance',

            // Permisos para centros de votación
            'view voting centers',
            'create voting centers',
            'update voting centers',
            'delete voting centers',

            // Permisos para movilización electoral
            'view electoral mobilization',
            'create electoral mobilization',
            'update electoral mobilization',
            'delete electoral mobilization',
            'manage electoral mobilization',
            'manage electoral mobilization details',
            'view mobilization map',

            // Permisos para seguimiento de votación
            'view voting tracker',
            'update voting tracker',
            'manage voting tracker',

            // Permisos para alertas electorales
            'view electoral alerts',
            'create electoral alerts',
            'update electoral alerts',
            'delete electoral alerts',
            'send electoral alerts',

            // Permisos para reportes electorales
            'view electoral reports',
            'create electoral reports',
            'export electoral reports',
            'export electoral data',
            'delete electoral reports',

            // Permisos para el panel de control electoral
            'view electoral dashboard',
            'manage electoral settings',

            // Permisos para asistencia a eventos
            'view event attendance',
            'create event attendance',
            'update event attendance',
            'delete event attendance',
            'manage event attendance',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos electorales nuevos.");
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::query()->where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }
    }
}
