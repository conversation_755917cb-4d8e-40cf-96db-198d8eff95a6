<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PersonsPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos básicos para el módulo de personas
            'view persons',
            'create persons',
            'update persons',
            'delete persons',
            'edit persons',

            // Permisos específicos para funcionalidades de personas
            'import persons',
            'export persons',
            'assign 1x10',
            'search persons',
            'view person statistics',
        ];

        foreach ($permissions as $permission) {
            Permission::query()->updateOrCreate([
                'name' => $permission,
            ]);
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::query()->where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }
    }
}
