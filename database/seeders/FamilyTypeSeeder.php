<?php

namespace Database\Seeders;

use App\Models\FamilyType;
use Illuminate\Database\Seeder;

class FamilyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $familyTypes = [
            [
                'id' => 1,
                'description' => 'BASICA',
            ],
            [
                'id' => 2,
                'description' => 'ADICIONAL',
            ],
        ];

        foreach ($familyTypes as $familyType) {
            FamilyType::updateOrCreate(
                ['id' => $familyType['id']],
                $familyType
            );
        }
    }
}
