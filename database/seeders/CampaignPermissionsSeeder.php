<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class CampaignPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos básicos para el módulo de campañas
            'view campaigns',
            'create campaigns',
            'update campaigns',
            'delete campaigns',
            'edit campaigns',

            // Permisos específicos para funcionalidades de campañas
            'import campaign contacts',
            'export campaign data',
            'send campaign messages',
            'schedule campaigns',
            'view campaign statistics',
            'view campaign reports',
            'export campaign reports',

            // Permisos para gestión de plantillas de mensajes
            'view message templates',
            'create message templates',
            'update message templates',
            'delete message templates',

            // Permisos para configuración de campañas
            'configure campaign settings',
            'manage campaign providers',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos de campañas nuevos.");
        }
    }
}
