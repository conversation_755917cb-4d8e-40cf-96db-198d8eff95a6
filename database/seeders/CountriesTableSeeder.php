<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class CountriesTableSeeder extends Seeder
{
    protected $count;

    protected $countP;

    public function __construct()
    {
        $this->count = 0;
        $this->countP = 0;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        $countries = [
            ['name' => 'Venezuela', 'prefix' => '58'],
            ['name' => 'Colombia', 'prefix' => '57'],
            ['name' => 'Ecuador', 'prefix' => '593'],
            ['name' => 'Perú', 'prefix' => '51'],
            ['name' => 'Brasil', 'prefix' => '55'],
            ['name' => 'Uruguay', 'prefix' => '598'],
            ['name' => 'Paraguay', 'prefix' => '595'],
            ['name' => 'Bolivia', 'prefix' => '591'],
            ['name' => 'Chile', 'prefix' => '56'],
            ['name' => 'Argentina', 'prefix' => '54'],
            ['name' => 'Guyana Francesa', 'prefix' => '594'],
            ['name' => 'Surinam', 'prefix' => '597'],
            ['name' => 'Guyana', 'prefix' => '592'],
            ['name' => 'Trinidad y Tobago', 'prefix' => '868'],
            ['name' => 'Barbados', 'prefix' => '246'],
            ['name' => 'Santa Lucía', 'prefix' => '758'],
            ['name' => 'San Vicente y las Granadinas', 'prefix' => '784'],
            ['name' => 'Grenada', 'prefix' => '473'],
            ['name' => 'San Cristóbal y Nieves', 'prefix' => '869'],
            ['name' => 'Antigua y Barbuda', 'prefix' => '268'],
            ['name' => 'Rep blica Dominicana', 'prefix' => '809'],
            ['name' => 'Haití', 'prefix' => '509'],
            ['name' => 'Cuba', 'prefix' => '53'],
            ['name' => 'Jamaica', 'prefix' => '876'],
            ['name' => 'Panamá', 'prefix' => '507'],
            ['name' => 'Costa Rica', 'prefix' => '506'],
            ['name' => 'Nicaragua', 'prefix' => '505'],
            ['name' => 'Honduras', 'prefix' => '504'],
            ['name' => 'El Salvador', 'prefix' => '503'],
            ['name' => 'Guatemala', 'prefix' => '502'],
            ['name' => 'Belize', 'prefix' => '501'],
            ['name' => 'México', 'prefix' => '52'],
        ];

        foreach ($countries as $country) {
            Country::withTrashed()->updateOrCreate(
                ['name' => $country['name']],
                ['prefix' => $country['prefix'], 'deleted_at' => null]
            );
            $this->count++;
        }

    }
}
