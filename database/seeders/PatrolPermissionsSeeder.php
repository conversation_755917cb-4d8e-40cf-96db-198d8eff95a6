<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PatrolPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Basic permissions for the patrol module
            'view patrol',
            'create patrol',
            'update patrol',
            'delete patrol',

            // Specific permissions for patrol functionalities
            'manage patrol members',
            'manage patrol activities',
            'view patrol reports',
            'export patrol data',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        // Assign permissions to roles
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        $coordinatorRole = Role::where('name', 'coordinator')->first();
        if ($coordinatorRole) {
            $coordinatorRole->givePermissionTo([
                'view patrol',
                'create patrol',
                'update patrol',
                'manage patrol members',
                'manage patrol activities',
                'view patrol reports',
            ]);
        }

        if (isset($this->command)) {
            $this->command->info("Se han creado $count permisos de patrullaje nuevos.");
        }
    }
}
