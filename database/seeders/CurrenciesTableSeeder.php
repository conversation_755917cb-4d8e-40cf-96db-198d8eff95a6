<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\Currency;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrenciesTableSeeder extends Seeder
{
    public function run()
    {
        Model::unguard();
        $country = Country::where('name', 'Venezuela')->first();

        DB::transaction(function () use ($country) {
            $currencies = [
                ['symbol' => 'Bs.', 'name' => 'Bolívar', 'default' => true],
                ['symbol' => '$', 'name' => 'Dólar', 'default' => false],
                ['symbol' => '€', 'name' => 'Euro', 'default' => false],
                ['symbol' => '£', 'name' => 'Libra Esterlina', 'default' => false],
                ['symbol' => '¥', 'name' => 'Yen Japonés', 'default' => false],
                ['symbol' => 'R$', 'name' => 'Real Brasileño', 'default' => false],
                ['symbol' => '$MXN', 'name' => 'Peso Mexicano', 'default' => false],
                ['symbol' => '$COP', 'name' => 'Peso Colombiano', 'default' => false],
            ];
            foreach ($currencies as $currencyAttributes) {
                Currency::updateOrCreate(
                    ['country_id' => $country->id, 'symbol' => $currencyAttributes['symbol']],
                    $currencyAttributes
                );
            }
        });
    }
}
