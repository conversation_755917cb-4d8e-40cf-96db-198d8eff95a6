<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserSettingsPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Permisos para configuración de usuario
            'update profile',
            'update password',
            'update appearance',
            'update locale',
            'view notifications',
        ];

        $count = 0;
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            if (!$exists) {
                Permission::create(['name' => $permission]);
                $count++;
            }
        }

        // Asegurarse de que el rol Super Admin tenga todos los permisos
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $allPermissions = Permission::all()->pluck('name')->toArray();
            $superAdminRole->syncPermissions($allPermissions);
        }

        // Asignar permisos al rol de líder 1x10
        $leaderRole = Role::where('name', '1x10_leader')->first();
        if ($leaderRole) {
            $leaderRole->givePermissionTo([
                'update profile',
                'update password',
                'update appearance',
                'update locale',
                'view notifications',
            ]);
        }

        // Asignar permisos al rol de usuario normal
        $userRole = Role::where('name', 'User')->first();
        if ($userRole) {
            $userRole->givePermissionTo([
                'update profile',
                'update password',
                'update appearance',
                'update locale',
                'view notifications',
            ]);
        }

        $this->command->info("Se han creado $count permisos nuevos para configuración de usuario.");
    }
}
