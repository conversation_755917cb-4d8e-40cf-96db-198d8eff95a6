<?php

namespace Database\Seeders;

use App\Models\Profession;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;

class ProfessionsTableSeeder extends Seeder
{
    protected $count;

    protected $countP;

    public function __construct()
    {
        $this->count = 0;
        $this->countP = 0;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        $professions = [
            ['name' => 'Abogado(a)', 'acronym' => 'Abg'],
            ['name' => 'Agrimensor(a)', 'acronym' => 'Ag'],
            ['name' => 'Agrónomo(a)', 'acronym' => 'Ag'],
            ['name' => 'Antropólogo(a)', 'acronym' => 'Ant'],
            ['name' => 'Arquitecto(a)', 'acronym' => 'Arq'],
            ['name' => 'Arqueólogo(a)', 'acronym' => 'Arq'],
            ['name' => 'Auditor(a)', 'acronym' => 'Aud'],
            ['name' => 'Bachiller', 'acronym' => 'Bach'],
            ['name' => 'Bioanálitico(a)', 'acronym' => 'Bio'],
            ['name' => 'Biólogo(a)', 'acronym' => 'Bio'],
            ['name' => 'Cirujano(a)', 'acronym' => 'Cir'],
            ['name' => 'Criminólogo(a)', 'acronym' => 'Crim'],
            ['name' => 'Diseñador(a) en Publicidad', 'acronym' => 'D.P.'],
            ['name' => 'Diseñador(a) en Comunicación Visual', 'acronym' => 'D.C.V.'],
            ['name' => 'Diseñador(a) en Comunicación Gráfica', 'acronym' => 'D.C.G.'],
            ['name' => 'Diseñador(a) Gráfico', 'acronym' => 'D.G.'],
            ['name' => 'Diseñador(a) en Arquitectura', 'acronym' => 'D.A.'],
            ['name' => 'Diseñador(a) en Interiores', 'acronym' => 'D.I.'],
            ['name' => 'Diseñador(a) en Paisajismo', 'acronym' => 'D.P.'],
            ['name' => 'Diseñador(a) en Diseño de Modas', 'acronym' => 'D.D.M.'],
            ['name' => 'Diseñador(a) en Diseño de Productos', 'acronym' => 'D.D.P.'],
            ['name' => 'Diseñador(a) en Diseño de Servicios', 'acronym' => 'D.D.S.'],
            ['name' => 'Diseñador(a) en Diseño de Experiencias', 'acronym' => 'D.D.E.'],
            ['name' => 'Doctor(a)', 'acronym' => 'Dr'],
            ['name' => 'Doctor(a) en Ciencias Computacionales', 'acronym' => 'Dr'],
            ['name' => 'Economista', 'acronym' => 'Eco'],
            ['name' => 'Enfermero(a)', 'acronym' => 'Enf'],
            ['name' => 'Estadístico(a)', 'acronym' => 'Est'],
            ['name' => 'Físico(a)', 'acronym' => 'Fís'],
            ['name' => 'Fotógrafo(a)', 'acronym' => 'Fot'],
            ['name' => 'Geólogo(a)', 'acronym' => 'Geo'],
            ['name' => 'Geógrafo(a)', 'acronym' => 'Geo'],
            ['name' => 'Ingeniero(a) Civil', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) de Sistemas', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Electricista', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) en Computación', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) en Electrónica', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) en Informática', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Industrial', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Mecánico', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Químico', 'acronym' => 'Ing'],
            ['name' => 'Licenciado(a) en Administración', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Ciencias Gerenciales', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Comunicación Social', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Contaduría Pública', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Estadística', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Filosofía', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Física', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Geografía', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Historia', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Lengua y Literatura', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Matemáticas', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Psicología', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Química', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Sociología', 'acronym' => 'Lic'],
            ['name' => 'Matemático(a)', 'acronym' => 'Mat'],
            ['name' => 'Médico(a)', 'acronym' => 'Med'],
            ['name' => 'Nutricionista', 'acronym' => 'Nut'],
            ['name' => 'Odontólogo(a)', 'acronym' => 'Odo'],
            ['name' => 'Oftalmólogo(a)', 'acronym' => 'Oft'],
            ['name' => 'Optometrista', 'acronym' => 'Opt'],
            ['name' => 'Periodista', 'acronym' => 'Per'],
            ['name' => 'Politologo(a)', 'acronym' => 'Pol'],
            ['name' => 'Psicólogo(a)', 'acronym' => 'Psic'],
            ['name' => 'Químico(a)', 'acronym' => 'Quím'],
            ['name' => 'Sociólogo(a)', 'acronym' => 'Soc'],
            ['name' => 'T.S.U. en Administración', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Contaduría', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Informática', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Diseño', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Electrónica', 'acronym' => 'T.S.U.'],
            // Profesiones adicionales
            ['name' => 'Administrador(a)', 'acronym' => 'Adm'],
            ['name' => 'Analista de Sistemas', 'acronym' => 'A.S.'],
            ['name' => 'Asistente Dental', 'acronym' => 'A.D.'],
            ['name' => 'Bioanalista', 'acronym' => 'Bio'],
            ['name' => 'Bombero(a)', 'acronym' => 'Bmb'],
            ['name' => 'Contador(a) Público', 'acronym' => 'C.P.'],
            ['name' => 'Dermatólogo(a)', 'acronym' => 'Derm'],
            ['name' => 'Farmacéutico(a)', 'acronym' => 'Farm'],
            ['name' => 'Fisioterapeuta', 'acronym' => 'Ftp'],
            ['name' => 'Ginecólogo(a)', 'acronym' => 'Gin'],
            ['name' => 'Ingeniero(a) Agrónomo', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Ambiental', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) de Alimentos', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) de Minas', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) de Petróleo', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) de Telecomunicaciones', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Forestal', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Geodesta', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Geofísico', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Hidráulico', 'acronym' => 'Ing'],
            ['name' => 'Ingeniero(a) Naval', 'acronym' => 'Ing'],
            ['name' => 'Licenciado(a) en Biología', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Educación', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Enfermería', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Farmacia', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Idiomas', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Nutrición', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Trabajo Social', 'acronym' => 'Lic'],
            ['name' => 'Licenciado(a) en Turismo', 'acronym' => 'Lic'],
            ['name' => 'Paramédico(a)', 'acronym' => 'Param'],
            ['name' => 'Pediatra', 'acronym' => 'Ped'],
            ['name' => 'Radiólogo(a)', 'acronym' => 'Rad'],
            ['name' => 'T.S.U. en Enfermería', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Fisioterapia', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Mecánica', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Radiología', 'acronym' => 'T.S.U.'],
            ['name' => 'T.S.U. en Turismo', 'acronym' => 'T.S.U.'],
            ['name' => 'Técnico(a) Dental', 'acronym' => 'T.D.'],
            ['name' => 'Técnico(a) en Electrónica', 'acronym' => 'T.E.'],
            ['name' => 'Técnico(a) en Laboratorio', 'acronym' => 'T.L.'],
            ['name' => 'Técnico(a) en Radiología', 'acronym' => 'T.R.'],
            ['name' => 'Traumatólogo(a)', 'acronym' => 'Traum'],
            ['name' => 'Veterinario(a)', 'acronym' => 'Vet'],
        ];

        foreach ($professions as $profession) {
            Profession::withTrashed()->updateOrCreate(
                ['name' => $profession['name']],
                [
                    'acronym' => ($profession['acronym']) ? $profession['acronym'] : null,
                    'deleted_at' => null,
                ]
            );
            $this->count++;
        }

    }
}
