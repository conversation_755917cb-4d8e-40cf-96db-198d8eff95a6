<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Role;

class AssignMissingPermissionsToRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Definir los permisos para cada rol
        $rolePermissions = [
            'Admin' => [
                'manage settings',
                'view company',
                'update company',
                'view campaign reports',
                'export campaign reports',
                'view electoral reports',
                'export electoral reports',

                'view communication',
                'create communication',
                'update communication',
                'delete communication',
                'send messages',
                'view message history',
                'create reminders',
                'view reminders',
                'view material',
                'create material',
                'update material',
                'delete material',
                'manage distribution',
                'manage stock',
                'view contacts',
                'create contacts',
                'update contacts',
                'delete contacts',
                'view integration',
                'manage integration',
                'configure integration',
                'import data',
                'export data',
                'view statistics',
                'view dashboards',
                'manage locations',
                'view estates',
                'create estates',
                'update estates',
                'delete estates',
                'view municipalities',
                'create municipalities',
                'update municipalities',
                'delete municipalities',
                'view parishes',
                'create parishes',
                'update parishes',
                'delete parishes',
                'view voting centers',
                'create voting centers',
                'update voting centers',
                'delete voting centers',
                'view mobilization',
                'create mobilization',
                'update mobilization',
                'delete mobilization',
                'manage mobilization',
                'view electoral events',
                'create electoral events',
                'update electoral events',
                'delete electoral events',
                'manage electoral events',
                'view event attendance',
                'create event attendance',
                'update event attendance',
                'delete event attendance',
                'manage event attendance',

                'view tracking contacts',
                'create tracking contacts',
                'update tracking contacts',
                'delete tracking contacts',
                'view voter marking',
                'create voter marking',
                'update voter marking',
                'delete voter marking',
                'view tracking history',
                'export tracking history',
                'view tracking reports',
                'export tracking reports',
            ],
            'Electoral Coordinator' => [
                'view electoral reports',
                'export electoral reports',
                'view communication',
                'create communication',
                'send messages',
                'view message history',
                'create reminders',
                'view reminders',
                'view contacts',
                'create contacts',
                'update contacts',
                'view statistics',
                'view dashboards',
                'view estates',
                'view municipalities',
                'view parishes',
                'view voting centers',
                'view mobilization',
                'create mobilization',
                'update mobilization',
                'manage mobilization',
                'view electoral events',
                'create electoral events',
                'update electoral events',
                'manage electoral events',
                'view event attendance',
                'create event attendance',
                'update event attendance',
                'manage event attendance',
                'view tracking contacts',
                'create tracking contacts',
                'update tracking contacts',
                'view voter marking',
                'create voter marking',
                'update voter marking',
                'view tracking history',
                'view tracking reports',
            ],
            '1x10 Coordinator' => [
                'view communication',
                'create communication',
                'send messages',
                'view message history',
                'create reminders',
                'view reminders',
                'view contacts',
                'create contacts',
                'update contacts',
                'view statistics',
                'view dashboards',
                'view tracking contacts',
                'create tracking contacts',
                'update tracking contacts',
                'view tracking history',
                'view tracking reports',
            ],
            '1x10_leader' => [
                'manage own 1x10 group',
                'view own 1x10 members',
                'add own 1x10 members',
                'edit own 1x10 members',
                'remove own 1x10 members',
                'view communication',
                'send messages',
                'view message history',
                'create reminders',
                'view reminders',
                'view contacts',
                'create contacts',
                'update contacts',
                'view tracking contacts',
                'create tracking contacts',
                'view voter marking',
                'create voter marking',
            ],
            'Campaign Manager' => [
                'view campaign reports',
                'export campaign reports',
                'view communication',
                'create communication',
                'update communication',
                'send messages',
                'view message history',
                'create reminders',
                'view reminders',
                'view material',
                'create material',
                'update material',
                'manage distribution',
                'manage stock',
                'view contacts',
                'create contacts',
                'update contacts',
                'view statistics',
                'view dashboards',
                'view tracking contacts',
                'create tracking contacts',
                'update tracking contacts',
                'view tracking history',
                'view tracking reports',
            ],
        ];

        $count = 0;
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                // Obtener los permisos actuales del rol
                $currentPermissions = $role->permissions->pluck('name')->toArray();

                // Añadir solo los permisos que no tiene actualmente
                $newPermissions = array_diff($permissions, $currentPermissions);

                if (! empty($newPermissions)) {
                    $role->givePermissionTo($newPermissions);
                    $count += count($newPermissions);
                    $this->command->info('Se han asignado '.count($newPermissions)." permisos nuevos al rol '$roleName'.");
                }
            }
        }

        $this->command->info("Se han asignado un total de $count permisos a roles.");
    }
}
