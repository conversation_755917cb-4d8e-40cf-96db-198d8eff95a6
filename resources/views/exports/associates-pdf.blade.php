<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Lista de Asociados</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: fixed;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            overflow: hidden;
            word-wrap: break-word;
        }
        th {
            background-color: #2d3748;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            text-align: center;
            font-size: 10px;
            padding: 10px 0;
            border-top: 1px solid #ddd;
        }
        /* Definir anchos específicos para cada columna */
        .col-id { width: 8%; }
        .col-cedula { width: 12%; }
        .col-nombres { width: 15%; }
        .col-apellidos { width: 15%; }
        .col-email { width: 25%; }
        .col-telefono { width: 12%; }
        .col-estado { width: 13%; }
    </style>
</head>
<body>
    <div class="header">
        <h2>Lista de Asociados</h2>
        <p>Fecha de generación: {{ now()->format('d/m/Y H:i:s') }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th class="col-id">ID</th>
                <th class="col-cedula">Cédula</th>
                <th class="col-nombres">Nombres</th>
                <th class="col-apellidos">Apellidos</th>
                <th class="col-email">Email</th>
                <th class="col-telefono">Teléfono</th>
                <th class="col-estado">Estado</th>
            </tr>
        </thead>
        <tbody>
            @foreach($associates as $associate)
                <tr>
                    <td class="col-id">{{ $associate->idasociado }}</td>
                    <td class="col-cedula">{{ $associate->cedula_soc }}</td>
                    <td class="col-nombres">{{ $associate->nombres }}</td>
                    <td class="col-apellidos">{{ $associate->apellidos }}</td>
                    <td class="col-email">{{ $associate->email }}</td>
                    <td class="col-telefono">{{ $associate->numero_telefpers }}</td>
                    <td class="col-estado">{{ $associate->condicion_soc ? 'Activo' : 'Inactivo' }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>{{ config('app.name') }} - Reporte generado el {{ now()->format('d/m/Y') }} a las {{ now()->format('H:i:s') }}</p>
        <p>Página {PAGE_NUM} de {PAGE_COUNT}</p>
    </div>
</body>
</html>
