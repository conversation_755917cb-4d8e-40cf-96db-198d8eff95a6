@props([
    'size' => 'lg',
    'level' => '2',
])

@php
    $classes = match($size) {
        'xs' => 'text-xs font-semibold',
        'sm' => 'text-sm font-semibold',
        'base' => 'text-base font-semibold',
        'lg' => 'text-lg font-semibold',
        'xl' => 'text-xl font-semibold',
        '2xl' => 'text-2xl font-semibold',
        '3xl' => 'text-3xl font-semibold',
        '4xl' => 'text-4xl font-semibold',
        default => 'text-lg font-semibold',
    };
    
    $tag = match($level) {
        '1' => 'h1',
        '2' => 'h2',
        '3' => 'h3',
        '4' => 'h4',
        '5' => 'h5',
        '6' => 'h6',
        default => 'h2',
    };
@endphp

<{{ $tag }} {{ $attributes->class("text-gray-900 dark:text-white {$classes}") }}>
    {{ $slot }}
</{{ $tag }}>
