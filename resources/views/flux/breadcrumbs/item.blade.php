@props([
    'href' => null,
    'icon' => null,
    'showDivider' => null,
    'first' => false,
])

@php
    // Si showDivider no se especifica explícitamente, no mostrar el divisor para el primer elemento
    $showDivider = $showDivider ?? !$first;
@endphp

<li class="inline-flex items-center">
    @if($showDivider)
        <svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
        </svg>
    @endif

    @if($href)
        <a href="{{ $href }}" {{ $attributes->class("inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary-600 dark:text-gray-400 dark:hover:text-white") }}>
            @if($icon)
                <flux:icon.{{ $icon }} class="w-4 h-4 mr-2" />
            @endif
            {{ $slot }}
        </a>
    @else
        <span {{ $attributes->class("inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400") }}>
            @if($icon)
                <flux:icon.{{ $icon }} class="w-4 h-4 mr-2" />
            @endif
            {{ $slot }}
        </span>
    @endif
</li>
