@props([
    'label' => null,
    'hint' => null,
    'disabled' => false,
])

<div {{ $attributes->class('flex flex-col space-y-1') }}>
    @if ($label)
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ $label }}
        </label>
    @endif
    
    <div class="flex items-center">
        <button 
            type="button"
            @if ($disabled) disabled @endif
            {{ $attributes->whereStartsWith('wire:model') }}
            {{ $attributes->whereStartsWith('x-') }}
            {{ $attributes->whereStartsWith('wire:click') }}
            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 {{ $disabled ? 'opacity-50 cursor-not-allowed' : '' }}"
            x-data="{ on: @entangle($attributes->wire('model')) }"
            :class="on ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            @click="on = !on"
        >
            <span 
                class="pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="on ? 'translate-x-5' : 'translate-x-0'"
            ></span>
        </button>
    </div>
    
    @if ($hint)
        <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ $hint }}
        </p>
    @endif
</div>
