@props([
    'value' => 0,
    'color' => 'primary',
    'height' => 'h-2',
    'rounded' => 'rounded-full',
])

@php
    // Ensure value is between 0 and 100
    $normalizedValue = max(0, min(100, (int) $value));
    
    // Define color classes based on the color prop
    $colorClasses = match($color) {
        'primary' => 'bg-primary-500',
        'secondary' => 'bg-gray-500',
        'success' => 'bg-green-500',
        'danger' => 'bg-red-500',
        'warning' => 'bg-yellow-500',
        'info' => 'bg-blue-500',
        default => 'bg-primary-500',
    };
@endphp

<div {{ $attributes->class("w-full bg-gray-200 dark:bg-gray-700 {$rounded} overflow-hidden") }}>
    <div 
        class="{{ $colorClasses }} {{ $height }} {{ $rounded }} transition-all duration-300" 
        style="width: {{ $normalizedValue }}%"
        role="progressbar" 
        aria-valuenow="{{ $normalizedValue }}" 
        aria-valuemin="0" 
        aria-valuemax="100"
    ></div>
</div>
