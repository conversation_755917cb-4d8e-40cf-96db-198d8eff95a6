@props([
    'label' => '',
    'value' => '',
    'description' => null,
    'icon' => null,
    'iconColor' => 'primary',
    'change' => null,
    'trend' => null,
    'trendDirection' => null,
])

<div {{ $attributes->class("flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm") }}>
    <div class="flex-1">
        @if($label)
            <flux:stat.label>{{ $label }}</flux:stat.label>
        @endif

        @if($value)
            <flux:stat.number size="2xl" class="mt-1" :trend="$change" :trendDirection="$trendDirection">{{ $value }}</flux:stat.number>
        @endif

        @if($description)
            <flux:stat.description>{{ $description }}</flux:stat.description>
        @endif

        @if($trend)
            <flux:stat.trend :direction="$trendDirection">{{ $trend }}</flux:stat.trend>
        @endif

        {{ $slot }}
    </div>

    @if($icon)
        <flux:stat.icon :name="$icon" :color="$iconColor" />
    @endif
</div>
