@props([
    'name' => null,
    'color' => 'primary',
])

@php
    $colorClasses = match($color) {
        'primary' => 'bg-primary-100 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400',
        'secondary' => 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400',
        'success' => 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
        'danger' => 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400',
        'warning' => 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400',
        'info' => 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
        default => 'bg-primary-100 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400',
    };
@endphp

<div {{ $attributes->class("p-3 rounded-full {$colorClasses}") }}>
    @if($name)
        <flux:icon.{{ $name }} class="size-6" />
    @else
        {{ $slot }}
    @endif
</div>
