@props([
    'size' => 'xl',
    'trend' => null,
    'trendDirection' => null,
])

@php
    $classes = match($size) {
        'xs' => 'text-xs',
        'sm' => 'text-sm',
        'base' => 'text-base',
        'lg' => 'text-lg',
        'xl' => 'text-xl',
        '2xl' => 'text-2xl',
        '3xl' => 'text-3xl',
        '4xl' => 'text-4xl',
        default => 'text-xl',
    };
    
    $trendClasses = '';
    if ($trendDirection === 'up') {
        $trendClasses = 'text-green-600 dark:text-green-400';
    } elseif ($trendDirection === 'down') {
        $trendClasses = 'text-red-600 dark:text-red-400';
    }
@endphp

<div class="flex items-baseline">
    <div {{ $attributes->class("font-semibold text-gray-900 dark:text-white {$classes}") }}>
        {{ $slot }}
    </div>
    
    @if($trend !== null)
        <span class="ml-2 text-sm {{ $trendClasses }}">
            @if(is_numeric($trend) && $trend > 0)
                <span class="flex items-center">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    {{ $trend }}%
                </span>
            @elseif(is_numeric($trend) && $trend < 0)
                <span class="flex items-center">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                    {{ abs($trend) }}%
                </span>
            @else
                {{ $trend }}
            @endif
        </span>
    @endif
</div>
