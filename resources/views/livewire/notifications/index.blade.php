<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">{{ __('notifications.title') }}</h1>

                    <div class="flex flex-col md:flex-row items-center gap-3">
                        <div class="relative w-full md:w-64">
                            <flux:input
                                wire:model.live.debounce.300ms="search"
                                placeholder="{{ __('notifications.search_placeholder') }}"
                                icon="magnifying-glass"
                                class="w-full pr-10 focus:ring-2 focus:ring-primary-500"
                            />
                            @if($search)
                                <button wire:click="$set('search', '')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <flux:icon.x-mark class="size-4" />
                                </button>
                            @endif
                        </div>

                        <flux:select
                            wire:model.live="filter"
                            class="w-full md:w-48 focus:ring-2 focus:ring-primary-500"
                        >
                            <flux:select.option value="all">{{ __('notifications.all') }}</flux:select.option>
                            <flux:select.option value="unread">{{ __('notifications.unread') }}</flux:select.option>
                            <flux:select.option value="read">{{ __('notifications.read') }}</flux:select.option>
                        </flux:select>

                        <div class="flex items-center gap-2">
                            <flux:button
                                wire:click="markAllAsRead"
                                variant="filled"
                                class="text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700"
                                wire:loading.attr="disabled"
                            >
                                <div class="flex items-center gap-1">
                                    <span wire:loading.remove wire:target="markAllAsRead">{{ __('notifications.mark_all_as_read') }}</span>
                                    <span wire:loading wire:target="markAllAsRead">{{ __('Loading...') }}</span>
                                    <flux:icon.loader-circle class="animate-spin size-4" wire:loading wire:target="markAllAsRead" />
                                </div>
                            </flux:button>

                            <flux:button
                                wire:click="deleteAllNotifications"
                                variant="filled"
                                class="text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30"
                                wire:confirm="{{ __('global.this_action_is_irreversible') }}"
                                wire:loading.attr="disabled"
                            >
                                <div class="flex items-center gap-1">
                                    <span wire:loading.remove wire:target="deleteAllNotifications">{{ __('notifications.clear_all') }}</span>
                                    <span wire:loading wire:target="deleteAllNotifications">{{ __('Deleting...') }}</span>
                                    <flux:icon.loader-circle class="animate-spin size-4" wire:loading wire:target="deleteAllNotifications" />
                                </div>
                            </flux:button>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
                    @forelse($notifications as $notification)
                        <div
                            class="p-4 border-b border-gray-200 dark:border-gray-700 {{ $notification->read_at ? 'bg-gray-50 dark:bg-gray-800/50' : 'bg-white dark:bg-gray-800' }}"
                            wire:key="notification-{{ $notification->id }}"
                        >
                            <div class="flex items-start gap-4">
                                <div class="flex-shrink-0">
                                    @php
                                        $iconColor = match($notification->type) {
                                            'success' => 'text-green-500 dark:text-green-400',
                                            'warning' => 'text-yellow-500 dark:text-yellow-400',
                                            'error' => 'text-red-500 dark:text-red-400',
                                            'info' => 'text-blue-500 dark:text-blue-400',
                                            default => 'text-gray-500 dark:text-gray-400',
                                        };

                                        $iconName = match($notification->type) {
                                            'success' => 'check-circle',
                                            'warning' => 'exclamation-circle',
                                            'error' => 'x-circle',
                                            'info' => 'information-circle',
                                            default => 'bell',
                                        };
                                    @endphp

                                    @if($iconName === 'check-circle')
                                        <flux:icon.check-circle class="size-6 {{ $iconColor }}" />
                                    @elseif($iconName === 'exclamation-circle')
                                        <flux:icon.exclamation-circle class="size-6 {{ $iconColor }}" />
                                    @elseif($iconName === 'x-circle')
                                        <flux:icon.x-circle class="size-6 {{ $iconColor }}" />
                                    @elseif($iconName === 'information-circle')
                                        <flux:icon.information-circle class="size-6 {{ $iconColor }}" />
                                    @else
                                        <flux:icon.bell class="size-6 {{ $iconColor }}" />
                                    @endif
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between">
                                        <div>
                                            <h3 class="text-base font-medium text-gray-900 dark:text-white">
                                                {{ $notification->title }}
                                            </h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                                {{ $notification->message }}
                                            </p>
                                            @if($notification->link)
                                                <a
                                                    href="{{ $notification->link }}"
                                                    class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-2 inline-block"
                                                >
                                                    Ver más
                                                </a>
                                            @endif
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                                {{ $notification->created_at->diffForHumans() }}
                                                @if($notification->read_at)
                                                    <span class="ml-2 text-gray-400 dark:text-gray-500">
                                                        · Leída {{ $notification->read_at->diffForHumans() }}
                                                    </span>
                                                @endif
                                            </p>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            @if(!$notification->read_at)
                                                <flux:button
                                                    wire:click="markAsRead({{ $notification->id }})"
                                                    variant="filled"
                                                    size="xs"
                                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                                    title="Marcar como leída"
                                                >
                                                    <flux:icon.check class="size-4 mr-1" />
                                                    Marcar como leída
                                                </flux:button>
                                            @endif
                                            <flux:button
                                                wire:click="deleteNotification({{ $notification->id }})"
                                                variant="filled"
                                                size="xs"
                                                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                                title="Eliminar notificación"
                                                wire:confirm="¿Estás seguro de que deseas eliminar esta notificación?"
                                            >
                                                <flux:icon.trash class="size-4 mr-1" />
                                                Eliminar
                                            </flux:button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-8 text-center">
                            <flux:icon.inbox class="size-12 mx-auto mb-4 text-gray-400 dark:text-gray-500" />
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">No hay notificaciones</h3>
                            <p class="text-gray-500 dark:text-gray-400">
                                @if($search)
                                    No se encontraron notificaciones que coincidan con "{{ $search }}".
                                @elseif($filter === 'unread')
                                    No tienes notificaciones sin leer.
                                @elseif($filter === 'read')
                                    No tienes notificaciones leídas.
                                @else
                                    No tienes notificaciones.
                                @endif
                            </p>
                        </div>
                    @endforelse
                </div>

                <div class="mt-4">
                    {{ $notifications->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
