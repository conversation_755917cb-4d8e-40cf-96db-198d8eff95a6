<div class="flex flex-col gap-6">
    <x-auth-header title="{{ __('global.create_an_account') }}" description="{{ __('auth.enter_your_id') }}" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    @if (session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <form wire:submit="register" class="flex flex-col gap-6">
        <!-- Cédula -->
        <flux:input
            wire:model="cedula"
            id="cedula"
            label="Cédula"
            type="number"
            name="cedula"
            required
            autofocus
            placeholder="{{ __('auth.enter_your_id') }}"
            :disabled="$processing"
        />

        <!-- Email Address -->
        <flux:input
            wire:model="email"
            id="email"
            :label="__('global.email_address')"
            type="email"
            name="email"
            required
            autocomplete="email"
            placeholder="<EMAIL>"
            :disabled="$processing"
        />

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full" :disabled="$processing">
                <div wire:loading.remove wire:target="register">
                    {{ __('auth.verify_and_continue') }}
                </div>
                <div wire:loading wire:target="register" class="flex items-center justify-center">
                    {{ __('auth.processing') }}
                </div>
            </flux:button>
        </div>
    </form>

    <div class="space-x-1 text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>
            {{ __('global.already_have_an_account') }}
        </span>
        <flux:link :href="route('login')" wire:navigate>
            {{ __('global.sign_in') }}
        </flux:link>
    </div>
</div>
