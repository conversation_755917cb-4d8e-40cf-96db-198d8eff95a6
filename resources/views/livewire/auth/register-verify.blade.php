<div class="flex flex-col gap-6">
    <x-auth-header title="{{ __('auth.register_verification') }}" description="{{ __('auth.check_email_description') }}" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <div class="flex flex-col items-center justify-center gap-4 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <flux:icon.envelope class="h-16 w-16 text-primary-500" />
        <p class="text-center text-gray-600 dark:text-gray-300">
            {{ __('auth.check_email') }}
        </p>
    </div>

    <div class="flex items-center justify-center">
        <flux:button :href="route('register')" wire:navigate variant="subtle" class="w-full md:w-auto">
            {{ __('global.register') }}
        </flux:button>
    </div>

    <div class="space-x-1 text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>
            {{ __('global.already_have_an_account') }}
        </span>
        <flux:link :href="route('login')" wire:navigate>
            {{ __('global.sign_in') }}
        </flux:link>
    </div>
</div>
