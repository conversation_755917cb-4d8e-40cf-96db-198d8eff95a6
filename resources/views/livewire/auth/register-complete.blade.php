<div class="flex flex-col gap-6">
    <x-auth-header title="{{ __('auth.complete_registration') }}" description="{{ __('auth.complete_registration_description') }}" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    @if (session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <form wire:submit="register" class="flex flex-col gap-6">
        <!-- Name -->
        <flux:input
            wire:model="name"
            id="name"
            :label="__('users.name')"
            type="text"
            name="name"
            required
            autofocus
            autocomplete="name"
            placeholder="{{ __('users.your_full_name') }}"
            :disabled="$processing"
        />

        <!-- Username -->
        <flux:input
            wire:model="username"
            id="username"
            label="{{ __('global.username_address') }}"
            type="text"
            name="username"
            required
            autocomplete="username"
            placeholder="{{ __('auth.enter_username') }}"
            :disabled="$processing"
            helper-text="{{ __('auth.username_helper') }}"
        />

        <!-- Email Address (readonly) -->
        <flux:input
            wire:model="email"
            id="email"
            :label="__('global.email_address')"
            type="email"
            name="email"
            readonly
            disabled
            helper-text="{{ __('auth.email_helper') }}"
        />

        <!-- Password -->
        <flux:input
            wire:model="password"
            id="password"
            :label="__('global.password')"
            type="password"
            name="password"
            required
            autocomplete="new-password"
            placeholder="{{ __('global.enter_your_password') }}"
            :disabled="$processing"
            helper-text="{{ __('auth.password_helper') }}"
        />

        <!-- Confirm Password -->
        <flux:input
            wire:model="password_confirmation"
            id="password_confirmation"
            :label="__('global.confirm_password')"
            type="password"
            name="password_confirmation"
            required
            autocomplete="new-password"
            placeholder="{{ __('global.confirm_your_password') }}"
            :disabled="$processing"
        />

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full" :disabled="$processing">
                <div wire:loading.remove wire:target="register">
                    {{ __('auth.create_account') }}
                </div>
                <div wire:loading wire:target="register" class="flex items-center justify-center">
                    {{ __('auth.processing') }}
                </div>
            </flux:button>
        </div>
    </form>

    <div class="space-x-1 text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>
            {{ __('global.already_have_an_account') }}
        </span>
        <flux:link :href="route('login')" wire:navigate>
            {{ __('global.sign_in') }}
        </flux:link>
    </div>
</div>
