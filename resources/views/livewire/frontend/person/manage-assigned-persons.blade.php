<div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-5xl mx-auto">
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('person.manage_assigned_persons') }}</h1>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {{ __('person.manage_assigned_persons_description') }}
            </p>
        </div>

        <!-- Tarjeta principal -->
        <flux:card>
            <div class="p-4">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <flux:heading>{{ __('person.my_assigned_persons') }}</flux:heading>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {{ __('person.my_assigned_persons_description') }}
                        </p>
                    </div>
                    <flux:button wire:click="openCreatePersonModal" variant="primary">
                        <flux:icon.user-plus class="h-5 w-5 mr-2" />
                        {{ __('person.add_person') }}
                    </flux:button>
                </div>

                <!-- Barra de búsqueda y filtros -->
                <div class="mb-6">
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="{{ __('person.search_placeholder') }}"
                        icon="magnifying-glass"
                    />
                </div>

                <!-- Tabla de personas asignadas -->
                <div>
                    @if($assignedPersons->isEmpty())
                        <div class="text-center py-8">
                            <flux:icon.user-group class="mx-auto h-12 w-12 text-gray-400" />
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('person.no_assigned_persons') }}</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                {{ __('person.no_assigned_persons_description') }}
                            </p>
                            <div class="mt-6">
                                <flux:button wire:click="openCreatePersonModal" variant="primary">
                                    <flux:icon.user-plus class="h-5 w-5 mr-2" />
                                    {{ __('person.add_person') }}
                                </flux:button>
                            </div>
                        </div>
                    @else
                        <div class="overflow-x-auto">
                            <flux:table>
                                <flux:table.header>
                                    <flux:table.row>
                                        <flux:table.head>{{ __('person.full_name') }}</flux:table.head>
                                        <flux:table.head>{{ __('person.id_number') }}</flux:table.head>
                                        <flux:table.head>{{ __('person.phone') }}</flux:table.head>
                                        <flux:table.head>{{ __('person.role') }}</flux:table.head>
                                        <flux:table.head class="text-right">{{ __('global.actions') }}</flux:table.head>
                                    </flux:table.row>
                                </flux:table.header>
                                <flux:table.body>
                                    @foreach($assignedPersons as $person)
                                        <flux:table.row wire:key="person-{{ $person->id }}">
                                            <flux:table.cell>
                                                <div class="flex items-center">
                                                    <div class="h-10 w-10 flex-shrink-0 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 font-medium">
                                                        {{ substr($person->name, 0, 1) }}
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="font-medium text-gray-900 dark:text-white">{{ $person->name }}</div>
                                                        @if($person->email)
                                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email }}</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </flux:table.cell>
                                            <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                                            <flux:table.cell>{{ $person->phone }}</flux:table.cell>
                                            <flux:table.cell>
                                                <div class="flex flex-col space-y-1">
                                                    <flux:badge size="sm" color="{{ $person->role === 'Militante' ? 'red' : 'blue' }}" inset="top bottom">
                                                        {{ $person->role }}
                                                    </flux:badge>
                                                    <flux:badge size="sm" color="{{ $person->activity_status === 'Activo' ? 'green' : 'gray' }}" inset="top bottom">
                                                        {{ $person->activity_status }}
                                                    </flux:badge>
                                                </div>
                                            </flux:table.cell>
                                            <flux:table.cell>
                                                <div class="flex justify-end space-x-2">
                                                    <flux:button wire:click="openEditPersonModal({{ $person->id }})" variant="ghost" size="xs">
                                                        <flux:icon.pencil class="h-4 w-4" />
                                                    </flux:button>
                                                    <flux:button
                                                        wire:click="confirmDelete({{ $person->id }})"
                                                        variant="ghost"
                                                        size="xs"
                                                    >
                                                        <flux:icon.trash class="h-4 w-4 text-red-500" />
                                                    </flux:button>
                                                </div>
                                            </flux:table.cell>
                                        </flux:table.row>
                                    @endforeach
                                </flux:table.body>
                            </flux:table>
                        </div>

                        <!-- Paginación -->
                        <div class="mt-4">
                            {{ $assignedPersons->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Modal para crear persona -->
    <flux:modal wire:model="showCreatePersonModal" variant="flyout">
        <div class="p-4 space-y-4">
            <flux:heading>{{ __('person.add_person') }}</flux:heading>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ __('person.my_assigned_persons_description') }}
            </p>

            <form wire:submit="createPerson" class="space-y-4">
                <!-- Información Personal -->
                <div class="space-y-4">
                    <flux:heading size="sm">{{ __('person.personal_information') }}</flux:heading>

                    <div>
                        <flux:input
                            wire:model="name"
                            label="{{ __('person.full_name') }}"
                            placeholder="{{ __('person.enter_full_name') }}"
                            required
                        />
                        @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input
                            wire:model="cedula"
                            label="{{ __('person.id_number') }}"
                            placeholder="{{ __('person.enter_id_number') }}"
                            type="number"
                            required
                        />
                        @error('cedula') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input
                            wire:model="phone"
                            label="{{ __('person.phone') }}"
                            placeholder="{{ __('person.enter_phone') }}"
                            required
                        />
                        @error('phone') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input
                            wire:model="email"
                            label="{{ __('person.email') }}"
                            placeholder="{{ __('person.enter_email') }}"
                            type="email"
                        />
                        @error('email') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:textarea
                            wire:model="address"
                            label="{{ __('person.address') }}"
                            placeholder="{{ __('person.enter_address') }}"
                            rows="2"
                        />
                        @error('address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <!-- Información Electoral -->
                <div class="space-y-4">
                    <flux:heading size="sm">{{ __('person.electoral_information') }}</flux:heading>

                    <div>
                        <flux:input
                            wire:model="polling_center"
                            label="{{ __('person.polling_center') }}"
                            placeholder="{{ __('person.enter_polling_center') }}"
                        />
                        @error('polling_center') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Selector de ubicación (Estado, Municipio, Parroquia) -->
                    <div>
                        <flux:heading size="xs">{{ __('person.location') }}</flux:heading>
                        <livewire:location-selector
                            wire:key="location-selector-create"
                            :estate-id="$estate_id"
                            :municipality-id="$municipality_id"
                            :parish-id="$parish_id"
                        />
                    </div>
                </div>

                <!-- Información Adicional -->
                <div class="space-y-4">
                    <flux:heading size="sm">{{ __('person.additional_information') }}</flux:heading>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <flux:select
                                wire:model="role"
                                label="{{ __('person.role') }}"
                            >
                                <flux:select.option value="Votante">{{ __('person.voter') }}</flux:select.option>
                                <flux:select.option value="Militante">{{ __('person.militant') }}</flux:select.option>
                            </flux:select>
                            @error('role') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <flux:select
                                wire:model="activity_status"
                                label="{{ __('person.status') }}"
                            >
                                <flux:select.option value="Activo">{{ __('person.active') }}</flux:select.option>
                                <flux:select.option value="Inactivo">{{ __('person.inactive') }}</flux:select.option>
                            </flux:select>
                            @error('activity_status') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div>
                        <flux:input
                            wire:model="party"
                            label="{{ __('person.party') }}"
                            placeholder="{{ __('person.enter_party') }}"
                        />
                        @error('party') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:textarea
                            wire:model="observations"
                            label="{{ __('person.observations') }}"
                            placeholder="{{ __('person.enter_observations') }}"
                            rows="2"
                        />
                        @error('observations') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <flux:button wire:click="$set('showCreatePersonModal', false)" variant="ghost">
                        {{ __('global.cancel') }}
                    </flux:button>
                    <flux:button type="submit" variant="primary">
                        {{ __('global.save') }}
                    </flux:button>
                </div>
            </form>
        </div>
    </flux:modal>

    <!-- Modal para editar persona -->
    <flux:modal wire:model="showEditPersonModal" variant="flyout">
        <div class="p-4 space-y-4">
            <flux:heading>{{ __('person.edit_person') }}</flux:heading>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ __('person.my_assigned_persons_description') }}
            </p>

            <form wire:submit="updatePerson" class="space-y-4">
                <!-- Información Personal -->
                <div class="space-y-4">
                    <flux:heading size="sm">{{ __('person.personal_information') }}</flux:heading>

                    <div>
                        <flux:input
                            wire:model="name"
                            label="{{ __('person.full_name') }}"
                            placeholder="{{ __('person.enter_full_name') }}"
                            required
                        />
                        @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input
                            wire:model="cedula"
                            label="{{ __('person.id_number') }}"
                            placeholder="{{ __('person.enter_id_number') }}"
                            type="number"
                            required
                        />
                        @error('cedula') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input
                            wire:model="phone"
                            label="{{ __('person.phone') }}"
                            placeholder="{{ __('person.enter_phone') }}"
                            required
                        />
                        @error('phone') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input
                            wire:model="email"
                            label="{{ __('person.email') }}"
                            placeholder="{{ __('person.enter_email') }}"
                            type="email"
                        />
                        @error('email') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:textarea
                            wire:model="address"
                            label="{{ __('person.address') }}"
                            placeholder="{{ __('person.enter_address') }}"
                            rows="2"
                        />
                        @error('address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <!-- Información Electoral -->
                <div class="space-y-4">
                    <flux:heading size="sm">{{ __('person.electoral_information') }}</flux:heading>

                    <div>
                        <flux:input
                            wire:model="polling_center"
                            label="{{ __('person.polling_center') }}"
                            placeholder="{{ __('person.enter_polling_center') }}"
                        />
                        @error('polling_center') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Selector de ubicación (Estado, Municipio, Parroquia) -->
                    <div>
                        <flux:heading size="xs">{{ __('person.location') }}</flux:heading>
                        <livewire:location-selector
                            wire:key="location-selector-edit"
                            :estate-id="$estate_id"
                            :municipality-id="$municipality_id"
                            :parish-id="$parish_id"
                        />
                    </div>
                </div>

                <!-- Información Adicional -->
                <div class="space-y-4">
                    <flux:heading size="sm">{{ __('person.additional_information') }}</flux:heading>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <flux:select
                                wire:model="role"
                                label="{{ __('person.role') }}"
                            >
                                <flux:select.option value="Votante">{{ __('person.voter') }}</flux:select.option>
                                <flux:select.option value="Militante">{{ __('person.militant') }}</flux:select.option>
                            </flux:select>
                            @error('role') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <flux:select
                                wire:model="activity_status"
                                label="{{ __('person.status') }}"
                            >
                                <flux:select.option value="Activo">{{ __('person.active') }}</flux:select.option>
                                <flux:select.option value="Inactivo">{{ __('person.inactive') }}</flux:select.option>
                            </flux:select>
                            @error('activity_status') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div>
                        <flux:input
                            wire:model="party"
                            label="{{ __('person.party') }}"
                            placeholder="{{ __('person.enter_party') }}"
                        />
                        @error('party') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:textarea
                            wire:model="observations"
                            label="{{ __('person.observations') }}"
                            placeholder="{{ __('person.enter_observations') }}"
                            rows="2"
                        />
                        @error('observations') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <flux:button wire:click="$set('showEditPersonModal', false)" variant="ghost">
                        {{ __('global.cancel') }}
                    </flux:button>
                    <flux:button type="submit" variant="primary">
                        {{ __('global.save') }}
                    </flux:button>
                </div>
            </form>
        </div>
    </flux:modal>

    <!-- Modal de confirmación para eliminar -->
    <flux:modal wire:model="showDeleteConfirmation">
        <div class="p-4 space-y-4">
            <flux:heading>{{ __('person.confirm_unassignment') }}</flux:heading>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ __('person.unassign_confirmation_message') }}
            </p>

            @if($personToDelete)
                <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <div class="font-medium">{{ $personToDelete->name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('person.id_number') }}: {{ $personToDelete->cedula }}</div>
                </div>
            @endif

            <div class="flex justify-end space-x-3 pt-4">
                <flux:button wire:click="$set('showDeleteConfirmation', false)" variant="ghost">
                    {{ __('global.cancel') }}
                </flux:button>
                <flux:button wire:click="deletePerson" variant="danger">
                    {{ __('person.unassign_person') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
