<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <div>
        <flux:select
            wire:model.live="estateId"
            label="Estado"
            placeholder="Seleccione un estado"
            hint="Seleccione el estado donde reside la persona"
        >
            <flux:select.option value="">Seleccione un estado</flux:select.option>
            @foreach($this->estates as $estate)
                <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
            @endforeach
        </flux:select>
        @error('estateId') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
    </div>

    <div>
        <flux:select
            wire:model.live="municipalityId"
            label="Municipio"
            placeholder="{{ $estateId ? 'Seleccione un municipio' : 'Primero seleccione un estado' }}"
            :disabled="$estateId == null"
            hint="{{ $estateId ? 'Seleccione el municipio' : 'Primero debe seleccionar un estado' }}"
        >
            <flux:select.option value="">{{ $estateId ? 'Seleccione un municipio' : 'Primero seleccione un estado' }}</flux:select.option>
            @foreach($this->municipalities as $municipality)
                <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
            @endforeach
        </flux:select>
        @error('municipalityId') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
    </div>

    <div>
        <flux:select
            wire:model.live="parishId"
            label="Parroquia"
            placeholder="{{ $municipalityId ? 'Seleccione una parroquia' : 'Primero seleccione un municipio' }}"
            :disabled="$municipalityId == null"
            hint="{{ $municipalityId ? 'Seleccione la parroquia' : 'Primero debe seleccionar un municipio' }}"
        >
            <flux:select.option value="">{{ $municipalityId ? 'Seleccione una parroquia' : 'Primero seleccione un municipio' }}</flux:select.option>
            @foreach($this->parishes as $parish)
                <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
            @endforeach
        </flux:select>
        @error('parishId') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
    </div>
</div>
