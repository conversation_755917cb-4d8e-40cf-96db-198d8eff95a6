@push('scripts')
    @include('partials.dashboard-scripts')
@endpush

<div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                <flux:icon.chart-bar class="h-6 w-6 text-primary-500" />
                Dashboard
            </h1>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Bienvenido a tu panel de control. Desde aquí puedes acceder a todas las funciones del sistema.
            </p>
        </div>

        <!-- Estadísticas en una sola fila con tarjetas Flux mejoradas -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <flux:card class="overflow-hidden transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-primary-100 dark:border-primary-900/30 bg-gradient-to-br from-primary-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:text class="text-gray-500 dark:text-gray-400">Personas Asignadas</flux:text>
                            <flux:heading size="xl" class="mt-1 tabular-nums">{{ $this->statistics['assigned_persons'] }}</flux:heading>
                            <flux:text class="text-xs text-gray-500 dark:text-gray-400">Personas bajo tu responsabilidad</flux:text>
                        </div>
                        <div class="rounded-full bg-primary-100 p-3 dark:bg-primary-900/50">
                            <flux:icon.user-group class="h-6 w-6 text-primary-600 dark:text-primary-400" />
                        </div>
                    </div>
                </div>
                <flux:chart class="-mx-4 -mb-4 h-[3rem]" :value="[8, 5, 7, 9, 6, 8, 10]">
                    <flux:chart.svg gutter="0">
                        <flux:chart.line class="text-primary-400 dark:text-primary-500" />
                        <flux:chart.area class="text-primary-100 dark:text-primary-500/20" />
                    </flux:chart.svg>
                </flux:chart>
            </flux:card>

            <flux:card class="overflow-hidden transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-green-100 dark:border-green-900/30 bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:text class="text-gray-500 dark:text-gray-400">Seguimientos Recientes</flux:text>
                            <flux:heading size="xl" class="mt-1 tabular-nums">{{ $this->statistics['recent_trackings'] }}</flux:heading>
                            <flux:text class="text-xs text-gray-500 dark:text-gray-400">En los últimos 7 días</flux:text>
                        </div>
                        <div class="rounded-full bg-green-100 p-3 dark:bg-green-900/50">
                            <flux:icon.chart-bar class="h-6 w-6 text-green-600 dark:text-green-400" />
                        </div>
                    </div>
                </div>
                <flux:chart class="-mx-4 -mb-4 h-[3rem]" :value="[3, 7, 5, 8, 6, 9, 4]">
                    <flux:chart.svg gutter="0">
                        <flux:chart.line class="text-green-400 dark:text-green-500" />
                        <flux:chart.area class="text-green-100 dark:text-green-500/20" />
                    </flux:chart.svg>
                </flux:chart>
            </flux:card>

            <flux:card class="overflow-hidden transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-amber-100 dark:border-amber-900/30 bg-gradient-to-br from-amber-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:text class="text-gray-500 dark:text-gray-400">Eventos Próximos</flux:text>
                            <flux:heading size="xl" class="mt-1 tabular-nums">{{ $this->statistics['upcoming_events'] }}</flux:heading>
                            <flux:text class="text-xs text-gray-500 dark:text-gray-400">En los próximos 30 días</flux:text>
                        </div>
                        <div class="rounded-full bg-amber-100 p-3 dark:bg-amber-900/50">
                            <flux:icon.calendar class="h-6 w-6 text-amber-600 dark:text-amber-400" />
                        </div>
                    </div>
                </div>
                <flux:chart class="-mx-4 -mb-4 h-[3rem]" :value="[2, 4, 3, 5, 7, 6, 8]">
                    <flux:chart.svg gutter="0">
                        <flux:chart.line class="text-amber-400 dark:text-amber-500" />
                        <flux:chart.area class="text-amber-100 dark:text-amber-500/20" />
                    </flux:chart.svg>
                </flux:chart>
            </flux:card>

            <flux:card class="overflow-hidden transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-red-100 dark:border-red-900/30 bg-gradient-to-br from-red-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:text class="text-gray-500 dark:text-gray-400">Movilizaciones</flux:text>
                            <flux:heading size="xl" class="mt-1 tabular-nums">{{ $this->statistics['mobilizations'] }}</flux:heading>
                            <flux:text class="text-xs text-gray-500 dark:text-gray-400">Programadas próximamente</flux:text>
                        </div>
                        <div class="rounded-full bg-red-100 p-3 dark:bg-red-900/50">
                            <flux:icon.truck class="h-6 w-6 text-red-600 dark:text-red-400" />
                        </div>
                    </div>
                </div>
                <flux:chart class="-mx-4 -mb-4 h-[3rem]" :value="[4, 6, 8, 5, 7, 9, 6]">
                    <flux:chart.svg gutter="0">
                        <flux:chart.line class="text-red-400 dark:text-red-500" />
                        <flux:chart.area class="text-red-100 dark:text-red-500/20" />
                    </flux:chart.svg>
                </flux:chart>
            </flux:card>
        </div>

        <!-- Progreso de Completado 1x10 -->
        <flux:card class="mb-8 bg-gradient-to-r from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 border border-blue-100 dark:border-blue-900/30 transition-all duration-300 hover:shadow-md">
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    <flux:heading size="sm">Progreso de Completado 1x10</flux:heading>
                    <flux:badge variant="{{ $this->statistics['completion_rate'] >= 100 ? 'success' : 'primary' }}" class="text-xs">
                        {{ $this->statistics['completion_rate'] }}% Completado
                    </flux:badge>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2">
                    <div class="bg-primary-600 dark:bg-primary-500 h-2.5 rounded-full transition-all duration-500"
                         style="width: {{ $this->statistics['completion_rate'] }}%"></div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Has registrado {{ $this->statistics['assigned_persons'] }} de 10 personas en tu grupo 1x10.
                    @if($this->statistics['completion_rate'] < 100)
                        Te faltan {{ 10 - $this->statistics['assigned_persons'] }} personas para completar tu grupo.
                    @else
                        ¡Felicidades! Has completado tu grupo 1x10.
                    @endif
                </p>
            </div>
        </flux:card>

        <!-- Gráficos Avanzados -->
        <flux:card class="mb-8 bg-gradient-to-br from-purple-50 to-white dark:from-gray-900 dark:to-gray-800 border border-purple-100 dark:border-purple-900/30 transition-all duration-300 hover:shadow-md">
            <div class="p-4">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                    <flux:heading size="sm">Análisis de Actividad</flux:heading>
                </div>

                <!-- Datos de Actividad -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div class="p-4 border border-purple-100 dark:border-purple-900/30 rounded-lg bg-gradient-to-br from-purple-50 to-white dark:from-gray-900 dark:to-gray-800">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-2">Seguimientos Recientes</h3>
                        <div class="flex items-center gap-2">
                            <div class="w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center">
                                <flux:icon.chart-bar class="h-8 w-8 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                                <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $this->statistics['recent_trackings'] }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">En los últimos 7 días</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border border-green-100 dark:border-green-900/30 rounded-lg bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-2">Personas Asignadas</h3>
                        <div class="flex items-center gap-2">
                            <div class="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center">
                                <flux:icon.user-group class="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                                <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $this->statistics['assigned_persons'] }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Total de personas</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </flux:card>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Personas Asignadas -->
            <flux:card class="col-span-1 transition-all duration-300 hover:shadow-md hover:scale-[1.01] border border-primary-100 dark:border-primary-900/30">
                <flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.user-group class="h-5 w-5 text-primary-500" />
                        <flux:text>Mis Personas Asignadas</flux:text>
                    </div>
                </flux:heading>
                <div>
                    @if($this->assignedPersons->count() > 0)
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($this->assignedPersons as $person)
                                <div class="p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150">
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $person->name }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">CI: {{ $person->cedula }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $person->phone }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                            No tienes personas asignadas aún.
                        </div>
                    @endif
                </div>
                <div>
                    <flux:button href="{{ route('1x10.manage') }}" wire:navigate variant="filled" class="w-full">
                        Ver Todas
                    </flux:button>
                </div>
            </flux:card>

            <!-- Eventos Próximos -->
            <flux:card class="col-span-1 transition-all duration-300 hover:shadow-md hover:scale-[1.01] border border-amber-100 dark:border-amber-900/30">
                <flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.calendar class="h-5 w-5 text-amber-500" />
                        <flux:text>Eventos Próximos</flux:text>
                    </div>
                </flux:heading>
                <div class="p-0">
                    @if($this->upcomingEvents->count() > 0)
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($this->upcomingEvents as $event)
                                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150">
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $event->name }}</p>
                                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <flux:icon.calendar-days class="h-4 w-4" />
                                        <span>{{ $event->start_date->format('d/m/Y H:i') }}</span>
                                    </div>
                                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <flux:icon.map-pin class="h-4 w-4" />
                                        <span>{{ $event->location }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                            No hay eventos próximos programados.
                        </div>
                    @endif
                </div>
            </flux:card>

            <!-- Accesos Rápidos -->
            <flux:card class="col-span-1 transition-all duration-300 hover:shadow-md hover:scale-[1.01] border border-blue-100 dark:border-blue-900/30">
                <flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.bolt class="h-5 w-5 text-blue-500" />
                        <flux:text>Accesos Rápidos</flux:text>
                    </div>
                </flux:heading>
                <div>
                    <div class="space-y-4">
                        <flux:button href="{{ route('1x10.manage') }}" wire:navigate variant="filled" class="w-full flex items-center justify-center gap-2">
                            <flux:icon.user-group class="size-5" />
                            Gestionar Personas Asignadas
                        </flux:button>

                        <flux:button href="{{ route('notifications') }}" wire:navigate variant="outline" class="w-full flex items-center justify-center gap-2">
                            <flux:icon.bell class="size-5" />
                            Ver Notificaciones
                        </flux:button>

                        <flux:button href="{{ route('settings.profile') }}" wire:navigate variant="outline" class="w-full flex items-center justify-center gap-2">
                            <flux:icon.user class="size-5" />
                            Editar Perfil
                        </flux:button>
                    </div>
                </div>
            </flux:card>
        </div>

        <!-- Seguimientos Recientes y Movilizaciones -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Seguimientos Recientes -->
            <flux:card class="transition-all duration-300 hover:shadow-md hover:scale-[1.01] border border-green-100 dark:border-green-900/30 bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800">
                <flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.chart-bar class="h-5 w-5 text-green-500" />
                        <flux:text>Seguimientos Recientes</flux:text>
                    </div>
                </flux:heading>
                <div>
                    @if($this->recentTrackings->count() > 0)
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($this->recentTrackings as $tracking)
                                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $tracking->person->name }}</p>
                                            <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                <flux:badge variant="{{ $tracking->tracking_type === 'contact' ? 'primary' : ($tracking->tracking_type === 'visit' ? 'success' : 'warning') }}" size="xs">
                                                    {{ $tracking->tracking_type === 'contact' ? 'Contacto' : ($tracking->tracking_type === 'visit' ? 'Visita' : 'Otro') }}
                                                </flux:badge>
                                                <span>{{ $tracking->created_at->diffForHumans() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    @if($tracking->notes)
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2 line-clamp-2">{{ $tracking->notes }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                            No hay seguimientos recientes.
                        </div>
                    @endif
                </div>
            </flux:card>

            <!-- Movilizaciones Próximas -->
            <flux:card class="transition-all duration-300 hover:shadow-md hover:scale-[1.01] border border-red-100 dark:border-red-900/30 bg-gradient-to-br from-red-50 to-white dark:from-gray-900 dark:to-gray-800">
                <flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.truck class="h-5 w-5 text-red-500" />
                        <flux:text>Movilizaciones Próximas</flux:text>
                    </div>
                </flux:heading>
                <div>
                    @if($this->upcomingMobilizations->count() > 0)
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($this->upcomingMobilizations as $mobilization)
                                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150">
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $mobilization->name }}</p>
                                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <flux:icon.calendar-days class="h-4 w-4" />
                                        <span>{{ $mobilization->mobilization_date->format('d/m/Y H:i') }}</span>
                                    </div>
                                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <flux:icon.map-pin class="h-4 w-4" />
                                        <span>{{ $mobilization->votingCenter->name }}</span>
                                    </div>
                                    <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <flux:icon.users class="h-4 w-4" />
                                        <span>{{ $mobilization->target_voters }} votantes objetivo</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                            No hay movilizaciones próximas programadas.
                        </div>
                    @endif
                </div>
            </flux:card>
        </div>

        <!-- Centros de Votación -->
        <flux:card class="mb-8 bg-gradient-to-br from-indigo-50 to-white dark:from-gray-900 dark:to-gray-800 border border-indigo-100 dark:border-indigo-900/30 transition-all duration-300 hover:shadow-md">
            <div class="p-4">
                <div class="flex items-center justify-between mb-4">
                    <flux:heading size="sm">Centros de Votación</flux:heading>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @if(count($this->statistics['map_data']) > 0)
                        @foreach(array_slice($this->statistics['map_data'], 0, 4) as $center)
                            <div class="p-4 border border-indigo-100 dark:border-indigo-900/30 rounded-lg hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors duration-150">
                                <p class="font-medium text-gray-900 dark:text-white">{{ $center['name'] }}</p>
                                <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    <flux:icon.map-pin class="h-4 w-4" />
                                    <span>{{ $center['address'] }}</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    <flux:icon.users class="h-4 w-4" />
                                    <span>{{ $center['voters_count'] }} votantes</span>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="col-span-2 p-4 text-center text-gray-500 dark:text-gray-400">
                            No hay centros de votación disponibles.
                        </div>
                    @endif
                </div>
            </div>
        </flux:card>

        <!-- Tarjetas de acceso rápido mejoradas -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Tarjeta para Mi Grupo 1x10 -->
            <flux:card class="transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border border-primary-100 dark:border-primary-900/30 bg-gradient-to-br from-primary-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="rounded-full bg-primary-100 p-3 dark:bg-primary-900/50">
                                <flux:icon.user-group class="h-6 w-6 text-primary-600 dark:text-primary-400" />
                            </div>
                            <h3 class="ml-4 text-lg font-medium text-gray-900 dark:text-white">Mis Personas Asignadas</h3>
                        </div>
                    </div>
                    <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                        Gestiona las personas asignadas a ti como líder 1x10, agrega nuevos miembros y actualiza su información.
                    </p>
                    <div class="mt-6">
                        <flux:button href="{{ route('1x10.manage') }}" wire:navigate variant="filled" class="w-full">
                            Gestionar Personas
                        </flux:button>
                    </div>
                </div>
            </flux:card>

            <!-- Tarjeta para Notificaciones -->
            <flux:card class="transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border border-blue-100 dark:border-blue-900/30 bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="rounded-full bg-blue-100 p-3 dark:bg-blue-900/50">
                                <flux:icon.bell class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <h3 class="ml-4 text-lg font-medium text-gray-900 dark:text-white">Notificaciones</h3>
                        </div>
                    </div>
                    <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                        Revisa tus notificaciones y mantente al día con las últimas actualizaciones del sistema.
                    </p>
                    <div class="mt-6">
                        <flux:button href="{{ route('notifications') }}" wire:navigate variant="filled" class="w-full">
                            Ver Notificaciones
                        </flux:button>
                    </div>
                </div>
            </flux:card>

            <!-- Tarjeta para Perfil -->
            <flux:card class="transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border border-green-100 dark:border-green-900/30 bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="rounded-full bg-green-100 p-3 dark:bg-green-900/50">
                                <flux:icon.user class="h-6 w-6 text-green-600 dark:text-green-400" />
                            </div>
                            <h3 class="ml-4 text-lg font-medium text-gray-900 dark:text-white">Mi Perfil</h3>
                        </div>
                    </div>
                    <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                        Actualiza tu información personal y configura tus preferencias de usuario en el sistema.
                    </p>
                    <div class="mt-6">
                        <flux:button href="{{ route('settings.profile') }}" wire:navigate variant="filled" class="w-full">
                            Editar Perfil
                        </flux:button>
                    </div>
                </div>
            </flux:card>
        </div>
    </div>
</div>
