<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-globe-alt class="size-6 text-primary-500"/>
                {{ __('locations.title') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('locations.subtitle') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('locations.home') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('locations.settings') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('locations.locations') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Estadísticas Mejoradas -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('locations.states') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($this->statistics['estates']) }}</div>
                    </div>
                    @if($this->statistics['top_estate'])
                        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <span class="font-medium">{{ __('locations.featured') }}:</span> {{ $this->statistics['top_estate']['name'] }}
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {{ $this->statistics['top_estate']['count'] }} municipios
                            </span>
                        </div>
                    @endif
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-600 dark:to-blue-800 shadow-md">
                    <flux:icon.globe-alt class="size-6 text-white" />
                </div>
            </div>
            <div class="mt-3 w-full bg-gray-100 dark:bg-gray-700 rounded-full h-1.5">
                <div class="bg-blue-600 h-1.5 rounded-full" style="width: 100%"></div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('locations.municipalities') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($this->statistics['municipalities']) }}</div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span class="font-medium">{{ __('locations.average_per_state') }}:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            {{ $this->statistics['avg_municipalities_per_estate'] }}
                        </span>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-green-400 to-green-600 dark:from-green-600 dark:to-green-800 shadow-md">
                    <flux:icon.building-office class="size-6 text-white" />
                </div>
            </div>
            <div class="mt-3 w-full bg-gray-100 dark:bg-gray-700 rounded-full h-1.5">
                <div class="bg-green-600 h-1.5 rounded-full" style="width: 75%"></div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('locations.parishes') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($this->statistics['parishes']) }}</div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span class="font-medium">{{ __('locations.average_per_municipality') }}:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                            {{ $this->statistics['avg_parishes_per_municipality'] }}
                        </span>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-600 dark:to-purple-800 shadow-md">
                    <flux:icon.map-pin class="size-6 text-white" />
                </div>
            </div>
            <div class="mt-3 w-full bg-gray-100 dark:bg-gray-700 rounded-full h-1.5">
                <div class="bg-purple-600 h-1.5 rounded-full" style="width: 50%"></div>
            </div>
        </flux:card>

        @if($this->statistics['top_municipality'])
        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('locations.featured_municipality') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-xl font-bold text-gray-900 dark:text-white">{{ $this->statistics['top_municipality']['name'] }}</div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span class="font-medium">{{ $this->statistics['top_municipality']['estate'] }}</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                            {{ $this->statistics['top_municipality']['count'] }} parroquias
                        </span>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-600 dark:to-amber-800 shadow-md">
                    <flux:icon.star class="size-6 text-white" />
                </div>
            </div>
            <div class="mt-3 w-full bg-gray-100 dark:bg-gray-700 rounded-full h-1.5">
                <div class="bg-amber-600 h-1.5 rounded-full" style="width: 85%"></div>
            </div>
        </flux:card>
        @endif
    </div>

    <!-- Gráficos de Visualización -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4">
            <flux:heading size="sm" class="mb-4">{{ __('locations.states_with_most_municipalities') }}</flux:heading>
            <x-charts.bar-chart
                :labels="$this->statistics['estates_chart']['labels']"
                :values="$this->statistics['estates_chart']['values']"
                :colors="['blue', 'primary', 'green', 'purple', 'amber']"
                height="h-64"
            />
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4">
            <flux:heading size="sm" class="mb-4">{{ __('locations.municipalities_with_most_parishes') }}</flux:heading>
            <x-charts.horizontal-bar-chart
                :labels="$this->statistics['municipalities_chart']['labels']"
                :values="$this->statistics['municipalities_chart']['values']"
                :colors="['green', 'primary', 'blue', 'purple', 'amber']"
                height="h-64"
            />
        </flux:card>
    </div>

    <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4 mb-6">
        <flux:heading size="sm" class="mb-4">{{ __('locations.location_distribution') }}</flux:heading>
        <div class="flex flex-col md:flex-row items-center justify-center gap-6">
            <x-charts.pie-chart
                :labels="$this->statistics['distribution_chart']['labels']"
                :values="$this->statistics['distribution_chart']['values']"
                :colors="['blue', 'green', 'purple']"
                height="h-64"
                class="w-full md:w-1/2"
            />
            <div class="w-full md:w-1/2">
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-medium mb-2">{{ __('locations.location_summary') }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('locations.location_summary_description') }}</p>
                    </div>
                    <div class="grid grid-cols-1 gap-2">
                        <div class="flex justify-between items-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <span class="font-medium">{{ __('locations.states') }}:</span>
                            <span class="text-blue-600 dark:text-blue-400 font-bold">{{ number_format($this->statistics['estates']) }}</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <span class="font-medium">{{ __('locations.municipalities') }}:</span>
                            <span class="text-green-600 dark:text-green-400 font-bold">{{ number_format($this->statistics['municipalities']) }}</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                            <span class="font-medium">{{ __('locations.parishes') }}:</span>
                            <span class="text-purple-600 dark:text-purple-400 font-bold">{{ number_format($this->statistics['parishes']) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </flux:card>

    <flux:card class="shadow-md hover:shadow-lg transition-all duration-300">
        <div class="p-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                <div class="flex flex-col md:flex-row items-start md:items-center gap-3 w-full md:w-auto">
                    <div class="relative w-full md:w-64">
                        <flux:input
                            wire:model.live.debounce.300ms="search"
                            placeholder="{{ __('locations.search_locations') }}"
                            icon="magnifying-glass"
                            class="w-full pr-10 focus:ring-2 focus:ring-primary-500"
                        />
                        @if($search)
                            <button wire:click="$set('search', '')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <flux:icon.x-mark class="size-4" />
                            </button>
                        @endif
                    </div>

                    @if($tab === 'municipalities')
                        <flux:select
                            wire:model.live="filterEstateId"
                            placeholder="Filtrar por estado"
                            class="w-full md:w-48 focus:ring-2 focus:ring-primary-500"
                        >
                            <flux:select.option value="">{{ __('locations.all_states') }}</flux:select.option>
                            @foreach($this->allEstates as $estate)
                                <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endif

                    @if($tab === 'parishes')
                        <div class="flex flex-col md:flex-row gap-3 w-full">
                            <flux:select
                                wire:model.live="filterEstateId"
                                placeholder="{{ __('locations.filter_by_state') }}"
                                class="w-full md:w-48 focus:ring-2 focus:ring-primary-500"
                            >
                                <flux:select.option value="">{{ __('locations.all_states') }}</flux:select.option>
                                @foreach($this->allEstates as $estate)
                                    <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                                @endforeach
                            </flux:select>

                            <flux:select
                                wire:model.live="filterMunicipalityId"
                                placeholder="{{ __('locations.filter_by_municipality') }}"
                                class="w-full md:w-48 focus:ring-2 focus:ring-primary-500"
                                :disabled="!$filterEstateId"
                            >
                                <flux:select.option value="">{{ __('locations.all_municipalities') }}</flux:select.option>
                                @foreach($this->municipalitiesForEstate as $municipality)
                                    <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </div>
                    @endif
                </div>
                <div class="flex items-center gap-3">
                    @if($tab === 'estates')
                        <flux:button wire:click="exportEstates" variant="filled" icon="arrow-down-tray" class="text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700">
                            {{ __('locations.export') }}
                        </flux:button>
                        <flux:button wire:click="createEstate" variant="filled" icon="plus" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white">
                            {{ __('locations.new_state') }}
                        </flux:button>
                    @elseif($tab === 'municipalities')
                        <flux:button wire:click="exportMunicipalities" variant="filled" icon="arrow-down-tray" class="text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700">
                            {{ __('locations.export') }}
                        </flux:button>
                        <flux:button wire:click="createMunicipality" variant="filled" icon="plus" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white">
                            {{ __('locations.new_municipality') }}
                        </flux:button>
                    @elseif($tab === 'parishes')
                        <flux:button wire:click="exportParishes" variant="filled" icon="arrow-down-tray" class="text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700">
                            {{ __('locations.export') }}
                        </flux:button>
                        <flux:button wire:click="createParish" variant="filled" icon="plus" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white">
                            {{ __('locations.new_parish') }}
                        </flux:button>
                    @endif
                </div>
            </div>

            <flux:tab.group wire:model="tab">
                <flux:tabs variant="segmented">
                    <flux:tab name="estates" icon="globe-alt">{{ __('locations.states') }}</flux:tab>
                    <flux:tab name="municipalities" icon="building-office">{{ __('locations.municipalities') }}</flux:tab>
                    <flux:tab name="parishes" icon="map-pin">{{ __('locations.parishes') }}</flux:tab>
                </flux:tabs>

                <flux:tab.panel name="estates">
                    <div class="overflow-x-auto">
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">{{ __('locations.name') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">{{ __('locations.code') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'municipalities_count'" :direction="$sortDirection" wire:click="sort('municipalities_count')">{{ __('locations.municipalities') }}</flux:table.column>
                                <flux:table.column>{{ __('locations.actions') }}</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                @forelse($this->estates as $estate)
                                    <flux:table.row wire:key="estate-row-{{ $estate->id }}">
                                        <flux:table.cell>{{ $estate->name }}</flux:table.cell>
                                        <flux:table.cell>{{ $estate->code }}</flux:table.cell>
                                        <flux:table.cell>{{ $estate->municipalities_count ?? 0 }}</flux:table.cell>
                                        <flux:table.cell>
                                            <div class="flex items-center justify-end gap-2">
                                                <flux:dropdown position="bottom-end">
                                                    <flux:button variant="filled" size="xs" class="px-2">
                                                        <flux:icon.ellipsis-horizontal class="size-5" />
                                                    </flux:button>
                                                    <flux:menu>
                                                        <flux:menu.item wire:click="editEstate({{ $estate->id }})" icon="pencil-square">
                                                            {{ __('locations.edit') }}
                                                        </flux:menu.item>
                                                        <flux:menu.item wire:click="confirmDeleteEstate({{ $estate->id }})" icon="trash" class="text-red-600 dark:text-red-400">
                                                            {{ __('locations.delete') }}
                                                        </flux:menu.item>
                                                    </flux:menu>
                                                </flux:dropdown>
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @empty
                                    <flux:table.row>
                                        <flux:table.cell colspan="4" class="text-center py-4">
                                            <div class="text-gray-500 dark:text-gray-400">
                                                {{ __('locations.no_states_found') }}
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @endforelse
                            </flux:table.rows>
                        </flux:table>
                    </div>

                    <div class="mt-4">
                        {{ $this->estates->links() }}
                    </div>
                </flux:tab.panel>

                <flux:tab.panel name="municipalities">
                    <div class="overflow-x-auto">
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">{{ __('locations.name') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">{{ __('locations.code') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'estate_id'" :direction="$sortDirection" wire:click="sort('estate_id')">{{ __('locations.state') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'parishes_count'" :direction="$sortDirection" wire:click="sort('parishes_count')">{{ __('locations.parishes') }}</flux:table.column>
                                <flux:table.column>{{ __('locations.actions') }}</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                @forelse($this->municipalities as $municipality)
                                    <flux:table.row wire:key="municipality-row-{{ $municipality->id }}">
                                        <flux:table.cell>{{ $municipality->name }}</flux:table.cell>
                                        <flux:table.cell>{{ $municipality->code }}</flux:table.cell>
                                        <flux:table.cell>{{ $municipality->estate->name }}</flux:table.cell>
                                        <flux:table.cell>{{ $municipality->parishes_count ?? 0 }}</flux:table.cell>
                                        <flux:table.cell>
                                            <div class="flex items-center justify-end gap-2">
                                                <flux:dropdown position="bottom-end">
                                                    <flux:button variant="filled" size="xs" class="px-2">
                                                        <flux:icon.ellipsis-horizontal class="size-5" />
                                                    </flux:button>
                                                    <flux:menu>
                                                        <flux:menu.item wire:click="editMunicipality({{ $municipality->id }})" icon="pencil-square">
                                                            {{ __('locations.edit') }}
                                                        </flux:menu.item>
                                                        <flux:menu.item wire:click="confirmDeleteMunicipality({{ $municipality->id }})" icon="trash" class="text-red-600 dark:text-red-400">
                                                            {{ __('locations.delete') }}
                                                        </flux:menu.item>
                                                    </flux:menu>
                                                </flux:dropdown>
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @empty
                                    <flux:table.row>
                                        <flux:table.cell colspan="5" class="text-center py-4">
                                            <div class="text-gray-500 dark:text-gray-400">
                                                {{ __('locations.no_municipalities_found') }}
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @endforelse
                            </flux:table.rows>
                        </flux:table>
                    </div>

                    <div class="mt-4">
                        {{ $this->municipalities->links() }}
                    </div>
                </flux:tab.panel>

                <flux:tab.panel name="parishes">
                    <div class="overflow-x-auto">
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">{{ __('locations.name') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">{{ __('locations.code') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'municipality_id'" :direction="$sortDirection" wire:click="sort('municipality_id')">{{ __('locations.municipality') }}</flux:table.column>
                                <flux:table.column>{{ __('locations.state') }}</flux:table.column>
                                <flux:table.column>{{ __('locations.actions') }}</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                @forelse($this->parishes as $parish)
                                    <flux:table.row wire:key="parish-row-{{ $parish->id }}">
                                        <flux:table.cell>{{ $parish->name }}</flux:table.cell>
                                        <flux:table.cell>{{ $parish->code }}</flux:table.cell>
                                        <flux:table.cell>{{ $parish->municipality->name }}</flux:table.cell>
                                        <flux:table.cell>{{ $parish->municipality->estate->name }}</flux:table.cell>
                                        <flux:table.cell>
                                            <div class="flex items-center justify-end gap-2">
                                                <flux:dropdown position="bottom-end">
                                                    <flux:button variant="filled" size="xs" class="px-2">
                                                        <flux:icon.ellipsis-horizontal class="size-5" />
                                                    </flux:button>
                                                    <flux:menu>
                                                        <flux:menu.item wire:click="editParish({{ $parish->id }})" icon="pencil-square">
                                                            {{ __('locations.edit') }}
                                                        </flux:menu.item>
                                                        <flux:menu.item wire:click="confirmDeleteParish({{ $parish->id }})" icon="trash" class="text-red-600 dark:text-red-400">
                                                            {{ __('locations.delete') }}
                                                        </flux:menu.item>
                                                    </flux:menu>
                                                </flux:dropdown>
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @empty
                                    <flux:table.row>
                                        <flux:table.cell colspan="5" class="text-center py-4">
                                            <div class="text-gray-500 dark:text-gray-400">
                                                {{ __('locations.no_parishes_found') }}
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @endforelse
                            </flux:table.rows>
                        </flux:table>
                    </div>

                    <div class="mt-4">
                        {{ $this->parishes->links() }}
                    </div>
                </flux:tab.panel>
            </flux:tab.group>
        </div>
    </flux:card>

    <!-- Modal para crear/editar estado -->
    <flux:modal wire:model="showEstateModal" title="{{ $estateId ? __('locations.edit_state') : __('locations.create_state') }}" size="md">
        <div class="space-y-5">
            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mb-4">
                <div class="flex items-center">
                    <flux:icon.information-circle class="size-5 text-blue-500 dark:text-blue-400 mr-2" />
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        {{ $estateId ? __('locations.update_state_info') : __('locations.create_state_info') }}
                    </p>
                </div>
            </div>

            <flux:input
                wire:model="estateName"
                label="{{ __('locations.name') }}"
                placeholder="{{ __('locations.state_name') }}"
                required
                class="focus:ring-2 focus:ring-blue-500"
                icon="document-text"
            />

            <flux:input
                wire:model="estateCode"
                label="{{ __('locations.code') }}"
                placeholder="{{ __('locations.state_code') }}"
                required
                class="focus:ring-2 focus:ring-blue-500"
                icon="hashtag"
                help="{{ __('locations.code_unique_hint') }}"
            />

            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:button wire:click="$set('showEstateModal', false)" variant="filled" class="hover:bg-gray-200 dark:hover:bg-gray-700">
                    {{ __('locations.cancel') }}
                </flux:button>
                <flux:button
                    wire:click="saveEstate"
                    variant="filled"
                    class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                    wire:loading.attr="disabled"
                >
                    <div class="flex items-center gap-1">
                        <span wire:loading.remove wire:target="saveEstate">{{ __('locations.save') }}</span>
                        <span wire:loading wire:target="saveEstate">{{ __('locations.saving') }}</span>
                        <flux:icon.loader-circle class="animate-spin size-4" wire:loading wire:target="saveEstate" />
                    </div>
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Modal para crear/editar municipio -->
    <flux:modal wire:model="showMunicipalityModal" title="{{ $municipalityId ? __('locations.edit_municipality') : __('locations.create_municipality') }}" size="md">
        <div class="space-y-5">
            <div class="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg mb-4">
                <div class="flex items-center">
                    <flux:icon.information-circle class="size-5 text-green-500 dark:text-green-400 mr-2" />
                    <p class="text-sm text-green-700 dark:text-green-300">
                        {{ $municipalityId ? __('locations.update_municipality_info') : __('locations.create_municipality_info') }}
                    </p>
                </div>
            </div>

            <flux:select
                wire:model.live="municipalityEstateId"
                label="{{ __('locations.state') }}"
                required
                class="focus:ring-2 focus:ring-green-500"
                icon="globe-alt"
            >
                <flux:select.option value="">{{ __('locations.select_state') }}</flux:select.option>
                @foreach($this->allEstates as $estate)
                    <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <flux:input
                wire:model="municipalityName"
                label="{{ __('locations.name') }}"
                placeholder="{{ __('locations.municipality_name') }}"
                required
                class="focus:ring-2 focus:ring-green-500"
                icon="document-text"
            />

            <flux:input
                wire:model="municipalityCode"
                label="{{ __('locations.code') }}"
                placeholder="{{ __('locations.municipality_code') }}"
                required
                class="focus:ring-2 focus:ring-green-500"
                icon="hashtag"
                help="{{ __('locations.code_unique_hint') }}"
            />

            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:button wire:click="$set('showMunicipalityModal', false)" variant="filled" class="hover:bg-gray-200 dark:hover:bg-gray-700">
                    {{ __('locations.cancel') }}
                </flux:button>
                <flux:button
                    wire:click="saveMunicipality"
                    variant="filled"
                    class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                    wire:loading.attr="disabled"
                >
                    <div class="flex items-center gap-1">
                        <span wire:loading.remove wire:target="saveMunicipality">{{ __('locations.save') }}</span>
                        <span wire:loading wire:target="saveMunicipality">{{ __('locations.saving') }}</span>
                        <flux:icon.loader-circle class="animate-spin size-4" wire:loading wire:target="saveMunicipality" />
                    </div>
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Modal para crear/editar parroquia -->
    <flux:modal wire:model="showParishModal" title="{{ $parishId ? __('locations.edit_parish') : __('locations.create_parish') }}" size="md">
        <div class="space-y-5">
            <div class="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg mb-4">
                <div class="flex items-center">
                    <flux:icon.information-circle class="size-5 text-purple-500 dark:text-purple-400 mr-2" />
                    <p class="text-sm text-purple-700 dark:text-purple-300">
                        {{ $parishId ? __('locations.update_parish_info') : __('locations.create_parish_info') }}
                    </p>
                </div>
            </div>

            <flux:select
                wire:model.live="municipalityEstateId"
                label="{{ __('locations.state') }}"
                required
                class="focus:ring-2 focus:ring-purple-500"
                icon="globe-alt"
            >
                <flux:select.option value="">{{ __('locations.select_state') }}</flux:select.option>
                @foreach($this->allEstates as $estate)
                    <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <flux:select
                wire:model.live="parishMunicipalityId"
                label="{{ __('locations.municipality') }}"
                required
                :disabled="!$municipalityEstateId"
                class="focus:ring-2 focus:ring-purple-500"
                icon="building-office"
            >
                <flux:select.option value="">{{ __('locations.select_municipality') }}</flux:select.option>
                @foreach($this->municipalitiesForEstate as $municipality)
                    <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <flux:input
                wire:model="parishName"
                label="{{ __('locations.name') }}"
                placeholder="{{ __('locations.parish_name') }}"
                required
                class="focus:ring-2 focus:ring-purple-500"
                icon="document-text"
            />

            <flux:input
                wire:model="parishCode"
                label="{{ __('locations.code') }}"
                placeholder="{{ __('locations.parish_code') }}"
                required
                class="focus:ring-2 focus:ring-purple-500"
                icon="hashtag"
                help="{{ __('locations.code_unique_hint') }}"
            />

            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:button wire:click="$set('showParishModal', false)" variant="filled" class="hover:bg-gray-200 dark:hover:bg-gray-700">
                    {{ __('locations.cancel') }}
                </flux:button>
                <flux:button
                    wire:click="saveParish"
                    variant="filled"
                    class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
                    wire:loading.attr="disabled"
                >
                    <div class="flex items-center gap-1">
                        <span wire:loading.remove wire:target="saveParish">{{ __('locations.save') }}</span>
                        <span wire:loading wire:target="saveParish">{{ __('locations.saving') }}</span>
                        <flux:icon.loader-circle class="animate-spin size-4" wire:loading wire:target="saveParish" />
                    </div>
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
