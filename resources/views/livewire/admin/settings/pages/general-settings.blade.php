<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-cog class="size-6 text-primary-500"/>
                {{ __('Configuración General') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Configuración general del sistema') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Configuración') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('General') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card>
        <div class="p-6 space-y-6">
            <flux:heading size="md">{{ __('Información Básica') }}</flux:heading>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <flux:input
                    wire:model="settings.app_name"
                    label="{{ __('Nombre de la Aplicación') }}"
                    placeholder="{{ __('Nombre de la aplicación') }}"
                />
                
                <flux:input
                    wire:model="settings.app_description"
                    label="{{ __('Descripción') }}"
                    placeholder="{{ __('Breve descripción de la aplicación') }}"
                />
                
                <flux:input
                    wire:model="settings.contact_email"
                    label="{{ __('Email de Contacto') }}"
                    placeholder="{{ __('<EMAIL>') }}"
                    type="email"
                />
                
                <flux:input
                    wire:model="settings.contact_phone"
                    label="{{ __('Teléfono de Contacto') }}"
                    placeholder="{{ __('+58 ************') }}"
                />
            </div>
            
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:heading size="md">{{ __('Apariencia') }}</flux:heading>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <flux:input
                        wire:model="settings.primary_color"
                        label="{{ __('Color Primario') }}"
                        type="color"
                    />
                    
                    <flux:input
                        wire:model="settings.secondary_color"
                        label="{{ __('Color Secundario') }}"
                        type="color"
                    />
                    
                    <flux:textarea
                        wire:model="settings.footer_text"
                        label="{{ __('Texto del Pie de Página') }}"
                        placeholder="{{ __('Texto que aparecerá en el pie de página') }}"
                        rows="3"
                    />
                </div>
            </div>
            
            <div class="flex justify-end pt-4">
                <flux:button wire:click="saveSettings" variant="primary">
                    {{ __('Guardar Configuración') }}
                </flux:button>
            </div>
        </div>
    </flux:card>
</div>
