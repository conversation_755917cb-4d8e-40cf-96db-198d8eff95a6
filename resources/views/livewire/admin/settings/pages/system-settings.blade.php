<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-wrench class="size-6 text-primary-500"/>
                {{ __('Configuración del Sistema') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Configuración avanzada del sistema') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Configuración') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Sistema') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card>
        <div class="p-6 space-y-6">
            <flux:heading size="md">{{ __('Configuración de Rendimiento') }}</flux:heading>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <flux:input
                    wire:model="settings.cache_ttl"
                    label="{{ __('Tiempo de Vida de Caché (minutos)') }}"
                    type="number"
                    min="1"
                    max="1440"
                />

                <flux:input
                    wire:model="settings.items_per_page"
                    label="{{ __('Elementos por Página') }}"
                    type="number"
                    min="5"
                    max="100"
                />

                <flux:toggle
                    wire:model="settings.enable_debug_mode"
                    label="{{ __('Modo de Depuración') }}"
                    hint="{{ __('Habilitar mensajes de error detallados (no recomendado en producción)') }}"
                />
            </div>

            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:heading size="md">{{ __('Configuración de Seguridad') }}</flux:heading>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <flux:toggle
                        wire:model="settings.enable_user_registration"
                        label="{{ __('Registro de Usuarios') }}"
                        hint="{{ __('Permitir que los usuarios se registren en el sistema') }}"
                    />

                    <flux:toggle
                        wire:model="settings.enable_email_verification"
                        label="{{ __('Verificación de Email') }}"
                        hint="{{ __('Requerir verificación de email para nuevos usuarios') }}"
                    />

                    <flux:toggle
                        wire:model="settings.enable_two_factor_auth"
                        label="{{ __('Autenticación de Dos Factores') }}"
                        hint="{{ __('Habilitar la autenticación de dos factores para mayor seguridad') }}"
                    />
                </div>
            </div>

            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:heading size="md">{{ __('Mantenimiento del Sistema') }}</flux:heading>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <flux:toggle
                            wire:model="settings.enable_maintenance_mode"
                            label="{{ __('Modo de Mantenimiento') }}"
                            hint="{{ __('Poner el sitio en modo de mantenimiento') }}"
                            disabled
                        />

                        <div class="mt-2">
                            <flux:button wire:click="toggleMaintenanceMode" variant="primary" size="sm">
                                {{ $settings['enable_maintenance_mode'] ? __('Desactivar Modo de Mantenimiento') : __('Activar Modo de Mantenimiento') }}
                            </flux:button>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <flux:button wire:click="clearCache" variant="primary" size="sm" class="w-full">
                            {{ __('Limpiar Caché') }}
                        </flux:button>

                        <flux:button wire:click="optimizeSystem" variant="primary" size="sm" class="w-full">
                            {{ __('Optimizar Sistema') }}
                        </flux:button>
                    </div>
                </div>
            </div>

            <div class="flex justify-end pt-4">
                <flux:button wire:click="saveSettings" variant="primary">
                    {{ __('Guardar Configuración') }}
                </flux:button>
            </div>
        </div>
    </flux:card>
</div>
