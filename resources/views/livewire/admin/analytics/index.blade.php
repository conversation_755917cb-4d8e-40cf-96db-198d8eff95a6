<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-chart-bar class="size-6 text-primary-500"/>
                {{ __('Analytics') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Análisis de datos y métricas del sistema') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" wire:navigate icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Analytics') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Estadísticas Principales -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Personas') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($statistics['total_persons']) }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-600 dark:to-blue-800 shadow-md">
                    <flux:icon.users class="size-6 text-white" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Líderes 1x10') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($statistics['total_leaders']) }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-green-400 to-green-600 dark:from-green-600 dark:to-green-800 shadow-md">
                    <flux:icon.user-group class="size-6 text-white" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Centros de Votación') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($statistics['total_voting_centers']) }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-600 dark:to-purple-800 shadow-md">
                    <flux:icon.building-office class="size-6 text-white" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Votos Registrados') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($statistics['total_votes']) }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-600 dark:to-amber-800 shadow-md">
                    <flux:icon.check-circle class="size-6 text-white" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between p-1">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Usuarios') }}</div>
                    <div class="mt-2 flex items-baseline">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ number_format($statistics['total_users']) }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-gradient-to-br from-red-400 to-red-600 dark:from-red-600 dark:to-red-800 shadow-md">
                    <flux:icon.user class="size-6 text-white" />
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Cobertura de Líderes 1x10 -->
    <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4 mb-6">
        <flux:heading size="sm" class="mb-4">Cobertura de Líderes 1x10</flux:heading>
        <div class="flex flex-col md:flex-row items-center justify-center gap-6">
            <div class="w-full md:w-1/2">
                <div class="relative pt-1">
                    <div class="flex mb-2 items-center justify-between">
                        <div>
                            <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-primary-600 bg-primary-200">
                                Progreso
                            </span>
                        </div>
                        <div class="text-right">
                            <span class="text-xs font-semibold inline-block text-primary-600">
                                {{ $statistics['coverage'] }}%
                            </span>
                        </div>
                    </div>
                    <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-primary-200">
                        <div style="width:{{ $statistics['coverage'] }}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-primary-500"></div>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-4">
                    <div class="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <span class="font-medium">Personas con responsable:</span>
                        <span class="text-green-600 dark:text-green-400 font-bold">{{ number_format($statistics['persons_with_responsible']) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <span class="font-medium">Personas sin responsable:</span>
                        <span class="text-red-600 dark:text-red-400 font-bold">{{ number_format($statistics['persons_without_responsible']) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <span class="font-medium">Líderes 1x10:</span>
                        <span class="text-blue-600 dark:text-blue-400 font-bold">{{ number_format($statistics['leaders_count']) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <span class="font-medium">Promedio por líder:</span>
                        <span class="text-purple-600 dark:text-purple-400 font-bold">{{ $statistics['average_persons_per_leader'] }}</span>
                    </div>
                </div>
            </div>

            <div class="w-full md:w-1/2">
                <x-charts.pie-chart
                    :labels="['Con responsable', 'Sin responsable']"
                    :values="[$statistics['persons_with_responsible'], $statistics['persons_without_responsible']]"
                    :colors="['green', 'red']"
                    height="h-64"
                    title="Distribución de Personas"
                />
            </div>
        </div>
    </flux:card>

    <!-- Gráficos de Análisis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4">
            <flux:heading size="sm" class="mb-4">Distribución por Género</flux:heading>
            <x-charts.pie-chart
                :labels="$personsByGender['labels']"
                :values="$personsByGender['values']"
                :colors="['blue', 'pink', 'gray']"
                height="h-64"
            />
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4">
            <flux:heading size="sm" class="mb-4">Distribución por Edad</flux:heading>
            <x-charts.bar-chart
                :labels="$personsByAge['labels']"
                :values="$personsByAge['values']"
                :colors="['primary', 'blue', 'green', 'purple', 'amber', 'gray']"
                height="h-64"
            />
        </flux:card>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4">
            <flux:heading size="sm" class="mb-4">Distribución por Estado</flux:heading>
            <x-charts.horizontal-bar-chart
                :labels="$personsByLocation['labels']"
                :values="$personsByLocation['values']"
                :colors="['blue', 'primary', 'green', 'purple', 'amber']"
                height="h-64"
            />
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4">
            <flux:heading size="sm" class="mb-4">Actividad de Líderes 1x10</flux:heading>
            <x-charts.pie-chart
                :labels="$groupsActivity['labels']"
                :values="$groupsActivity['values']"
                :colors="['green', 'blue', 'amber', 'red']"
                height="h-64"
            />
        </flux:card>
    </div>

    <!-- Tendencias de Votación -->
    <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4 mb-6">
        <flux:heading size="sm" class="mb-4">Tendencias de Votación</flux:heading>
        <div class="h-80">
            <!-- Simulación de gráfico de líneas -->
            <div class="w-full h-full flex flex-col">
                <div class="flex justify-between mb-2">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                        <span class="text-sm">Votantes Registrados</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span class="text-sm">Votantes Efectivos</span>
                    </div>
                </div>

                <div class="flex-1 relative">
                    <!-- Eje Y -->
                    <div class="absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500">
                        @foreach(range(5, 1) as $i)
                            <div>{{ $i * 400 }}</div>
                        @endforeach
                        <div>0</div>
                    </div>

                    <!-- Área del gráfico -->
                    <div class="absolute left-12 right-0 top-0 bottom-0 border-l border-b border-gray-300">
                        <!-- Líneas horizontales de referencia -->
                        @foreach(range(1, 5) as $i)
                            <div class="absolute left-0 right-0 border-t border-gray-200" style="top: {{ (6-$i) * 16.66 }}%"></div>
                        @endforeach

                        <!-- Datos del gráfico (simulados) -->
                        <div class="absolute inset-0 flex items-end">
                            @foreach($votingTrends['labels'] as $index => $month)
                                <div class="flex-1 flex flex-col items-center justify-end h-full">
                                    <!-- Barra de votantes registrados -->
                                    <div class="w-4 bg-blue-500 rounded-t" style="height: {{ ($votingTrends['datasets'][0]['values'][$index] / 2000) * 100 }}%"></div>

                                    <!-- Barra de votantes efectivos -->
                                    <div class="w-4 bg-green-500 rounded-t mt-1" style="height: {{ ($votingTrends['datasets'][1]['values'][$index] / 2000) * 100 }}%"></div>

                                    <!-- Etiqueta del mes -->
                                    <div class="text-xs mt-1">{{ $month }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </flux:card>

    <!-- Acciones Rápidas -->
    <flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 p-4 mb-6">
        <flux:heading size="sm" class="mb-4">Acciones Rápidas</flux:heading>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <flux:button href="{{ route('admin.persons.index') }}" wire:navigate variant="filled" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white">
                <div class="flex flex-col items-center py-2">
                    <flux:icon.users class="size-8 mb-2" />
                    <span>Gestionar Personas</span>
                </div>
            </flux:button>

            <flux:button href="{{ route('admin.persons.index') }}?filter[is_1x10]=1" wire:navigate variant="filled" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white">
                <div class="flex flex-col items-center py-2">
                    <flux:icon.user-group class="size-8 mb-2" />
                    <span>Gestionar Líderes 1x10</span>
                </div>
            </flux:button>

            <flux:button href="{{ route('admin.persons.assign') }}" wire:navigate variant="filled" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white">
                <div class="flex flex-col items-center py-2">
                    <flux:icon.user-plus class="size-8 mb-2" />
                    <span>Asignar 1x10</span>
                </div>
            </flux:button>
        </div>
    </flux:card>
</div>
