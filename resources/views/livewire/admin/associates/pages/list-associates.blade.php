<section class="w-full space-y-6">
    <!-- Statistics Cards -->
    <div class="flex gap-4">
        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('associates.total_associates') }}</flux:text>
                <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['total']) }}</flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.users variant="micro" class="text-blue-600 dark:text-blue-500" />
                    <span class="text-sm font-medium text-blue-600 dark:text-blue-500">Total registrados</span>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('associates.active_associates') }}</flux:text>
                <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['active']) }}</flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm font-medium text-green-600 dark:text-green-500">
                        {{ number_format(($this->statistics['active'] / max($this->statistics['total'], 1)) * 100, 1) }}% activos
                    </span>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('associates.inactive_associates') }}</flux:text>
                <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['inactive']) }}</flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.arrow-trending-down variant="micro" class="text-red-600 dark:text-red-500" />
                    <span class="text-sm font-medium text-red-600 dark:text-red-500">
                        {{ number_format(($this->statistics['inactive'] / max($this->statistics['total'], 1)) * 100, 1) }}% inactivos
                    </span>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('associates.new_associates') }}</flux:text>
                <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['new'] ?? 0) }}</flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.sparkles variant="micro" class="text-purple-600 dark:text-purple-500" />
                    <span class="text-sm font-medium text-purple-600 dark:text-purple-500">Último mes</span>
                </div>
            </div>
        </flux:card>
    </div>
    <flux:card class="flex-1">
    <!-- Search and Filters -->
    <div class="flex flex-col md:flex-row items-center gap-4 md:justify-between">
        <div class="flex items-center gap-2 w-full md:w-auto">
            <flux:input
                wire:model.live.debounce.300ms="search"
                placeholder="{{ __('associates.search_placeholder') }}"
                icon="magnifying-glass"
                class="w-full md:w-64"
            />
        </div>

        <div class="flex items-center gap-2 w-full md:w-auto">
            <flux:select wire:model.live="perPage" class="w-24">
                @foreach($perPageOptions as $value)
                    <flux:select.option value="{{ $value }}">{{ $value }}</flux:select.option>
                @endforeach
            </flux:select>

            <flux:select wire:model.live="status" class="w-32">
                <flux:select.option value="">{{ __('associates.all') }}</flux:select.option>
                <flux:select.option value="active">{{ __('associates.active') }}</flux:select.option>
                <flux:select.option value="inactive">{{ __('associates.inactive') }}</flux:select.option>
            </flux:select>

            <flux:dropdown position="bottom" align="end">
                <flux:button variant="outline" icon="arrow-down-tray">
                    {{ __('global.export') }}
                </flux:button>

                <flux:menu>
                    <flux:menu.item wire:click="exportToExcel" icon="document-text">
                        {{ __('global.export_excel') }}
                    </flux:menu.item>
                    <flux:menu.item wire:click="exportToPdf" icon="document">
                        {{ __('global.export_pdf') }}
                    </flux:menu.item>
                </flux:menu>
            </flux:dropdown>
        </div>
    </div>

    <!-- Data Table -->
    <flux:table :paginate="$this->associates">
        <flux:table.columns>
            <flux:table.column sortable :sorted="$sortBy === 'idasociado'" :direction="$sortDirection" wire:click="sort('idasociado')">
                {{ __('associates.id') }}
            </flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'cedula_soc'" :direction="$sortDirection" wire:click="sort('cedula_soc')">
                {{ __('associates.document') }}
            </flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'nombres'" :direction="$sortDirection" wire:click="sort('nombres')">
                {{ __('associates.name') }}
            </flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'email'" :direction="$sortDirection" wire:click="sort('email')">
                {{ __('associates.email') }}
            </flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'numero_telefpers'" :direction="$sortDirection" wire:click="sort('numero_telefpers')">
                {{ __('associates.phone') }}
            </flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'condicion_soc'" :direction="$sortDirection" wire:click="sort('condicion_soc')">
                {{ __('associates.status') }}
            </flux:table.column>
            <flux:table.column>{{ __('associates.actions') }}</flux:table.column>
        </flux:table.columns>

        <flux:table.rows>
            @forelse ($this->associates as $associate)
                <flux:table.row wire:key="associate-{{ $associate->idasociado }}">
                    <flux:table.cell>{{ $associate->idasociado }}</flux:table.cell>
                    <flux:table.cell>{{ $associate->cedula_soc }}</flux:table.cell>
                    <flux:table.cell class="font-medium">{{ $associate->nombres }} {{ $associate->apellidos }}</flux:table.cell>
                    <flux:table.cell>{{ $associate->email }}</flux:table.cell>
                    <flux:table.cell>{{ $associate->numero_telefpers }}</flux:table.cell>
                    <flux:table.cell>
                        <flux:badge
                            size="sm"
                            :color="$associate->condicion_soc ? 'success' : 'danger'"
                            class="whitespace-nowrap"
                        >
                            {{ $associate->condicion_soc ? __('associates.active') : __('associates.inactive') }}
                        </flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>
                        <flux:dropdown>
                            <flux:button size="sm" variant="ghost" icon:trailing="chevron-down">
                                {{ __('associates.actions') }}
                            </flux:button>
                            <flux:menu>
                                @can('view associates')
                                    <flux:menu.item wire:navigate href="{{ route('admin.associates.show', $associate) }}" icon="eye">
                                        {{ __('associates.view') }}
                                    </flux:menu.item>
                                @endcan

                                @can('edit associates')
                                    <flux:menu.item wire:navigate href="{{ route('admin.associates.edit', $associate) }}" icon="pencil">
                                        {{ __('associates.edit') }}
                                    </flux:menu.item>
                                @endcan

                                <flux:menu.separator />

                                <flux:menu.submenu heading="{{ __('associates.status') }}">
                                    <flux:menu.radio.group wire:model.live="associateStatus.{{ $associate->idasociado }}">
                                        <flux:menu.radio value="active" :checked="$associate->condicion_soc">
                                            {{ __('associates.active') }}
                                        </flux:menu.radio>
                                        <flux:menu.radio value="inactive" :checked="!$associate->condicion_soc">
                                            {{ __('associates.inactive') }}
                                        </flux:menu.radio>
                                    </flux:menu.radio.group>
                                </flux:menu.submenu>

                                <flux:menu.submenu heading="{{ __('global.quick_actions') }}">
                                    <flux:menu.checkbox wire:model.live="selectedAssociates" value="{{ $associate->idasociado }}">
                                        {{ __('global.select') }}
                                    </flux:menu.checkbox>
                                    <flux:menu.checkbox wire:model.live="markAsImportant" value="{{ $associate->idasociado }}">
                                        {{ __('global.mark_important') }}
                                    </flux:menu.checkbox>
                                </flux:menu.submenu>

                                <flux:menu.separator />

                                <flux:menu.item
                                    variant="danger"
                                    icon="trash"
                                    wire:click="$dispatch('deleteAssociate', { associate: {{ $associate->idasociado }} })"
                                >
                                    {{ __('global.delete') }}
                                </flux:menu.item>
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
            @empty
                <flux:table.row>
                    <flux:table.cell colspan="7" class="text-center py-8">
                        <div class="flex flex-col items-center justify-center gap-2">
                            <flux:icon name="user-group" class="w-12 h-12 text-gray-400" />
                            <span class="text-gray-500">{{ __('associates.no_associates_found') }}</span>
                        </div>
                    </flux:table.cell>
                </flux:table.row>
            @endforelse
        </flux:table.rows>
    </flux:table>
    </flux:card>

    <livewire:admin.associates.pages.destroy-associate />
</section>
