<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            Editar Asociado
        </x-slot:title>
        <x-slot:subtitle>
            Actualiza la información del asociado {{ $associate->nombres }} {{ $associate->apellidos }}
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card>
        <flux:tab.group>
            <flux:tabs wire:model="tab">
                <flux:tab name="personal">
                    <x-heroicon-m-user class="size-5"/>
                    Datos Personales
                </flux:tab>
                <flux:tab name="laboral">
                    <x-heroicon-m-briefcase class="size-5"/>
                    Datos Laborales
                </flux:tab>
                <flux:tab name="bancario">
                    <x-heroicon-m-banknotes class="size-5"/>
                    Datos Bancarios
                </flux:tab>
                <flux:tab name="familiar">
                    <x-heroicon-m-users class="size-5"/>
                    Grupo Familiar
                </flux:tab>
            </flux:tabs>

            <flux:tab.panel name="personal">
                <livewire:admin.associates.tabs.personal-data :associate="$associate" />
            </flux:tab.panel>

            <flux:tab.panel name="laboral">
                <livewire:admin.associates.tabs.work-data :associate="$associate" />
            </flux:tab.panel>

            <flux:tab.panel name="bancario">
                <livewire:admin.associates.tabs.bank-data :associate="$associate" />
            </flux:tab.panel>

            <flux:tab.panel name="familiar">
                <livewire:admin.associates.tabs.family-data :associate="$associate" />
            </flux:tab.panel>
        </flux:tab.group>
    </flux:card>
</section>
