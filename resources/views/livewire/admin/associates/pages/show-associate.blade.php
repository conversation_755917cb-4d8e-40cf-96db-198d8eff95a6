<div class="max-w-7xl mx-auto py-8 space-y-8">
    <!-- Header Section -->
    <div class="relative px-8 py-12 overflow-hidden">
        <!-- Decorative background elements -->
        <div class="absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent dark:from-accent/10"></div>
        <div class="absolute inset-0 bg-[radial-gradient(600px_circle_at_0%_0%,theme(colors.accent/0.1),transparent)]"></div>

        <div class="relative flex flex-col md:flex-row md:items-center md:justify-between gap-10">
            <div class="flex flex-col md:flex-row items-start md:items-center gap-8">
                <!-- Avatar section with animated gradient -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-accent to-accent/60 rounded-[2.5rem] blur-xl opacity-50 group-hover:opacity-70 transition-opacity"></div>
                    <div class="relative size-28 rounded-[2rem] bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center shadow-2xl ring-1 ring-black/5 dark:ring-white/10 transform transition-transform group-hover:scale-105">
                        <span class="text-5xl font-bold text-white">{{ substr($associate->nombres, 0, 1) }}</span>
                    </div>
                    <div class="absolute -bottom-3 -right-3">
                        <flux:badge
                            size="lg"
                            :color="$associate->condicion_soc ? 'success' : 'danger'"
                            class="rounded-full shadow-lg border border-white/10 dark:border-black/10"
                        >
                            {{ $associate->condicion_soc ? 'Activo' : 'Inactivo' }}
                        </flux:badge>
                    </div>
                </div>

                <!-- Info section -->
                <div class="space-y-4">
                    <h1 class="text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                        {{ $associate->nombres }} {{ $associate->apellidos }}
                    </h1>
                    <div class="flex flex-wrap items-center gap-3">
                        <div class="flex items-center gap-2 px-4 py-2 bg-white/50 dark:bg-zinc-800/50 backdrop-blur-sm rounded-full border border-gray-200/50 dark:border-zinc-700/50 shadow-sm">
                            <x-heroicon-m-identification class="size-5 text-accent"/>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-200">{{ $associate->cedula_soc }}</span>
                        </div>
                        <div class="flex items-center gap-2 px-4 py-2 bg-white/50 dark:bg-zinc-800/50 backdrop-blur-sm rounded-full border border-gray-200/50 dark:border-zinc-700/50 shadow-sm">
                            <x-heroicon-m-hashtag class="size-5 text-accent"/>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-200">{{ $associate->idasociado }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action buttons -->
            <div class="flex items-center gap-3">
                <flux:button
                    wire:click="$dispatch('openModal', { component: 'admin.associates.modals.edit-associate', arguments: { associate: {{ $associate->idasociado }} }})"
                    icon="pencil-square"
                    class="w-full md:w-auto bg-accent/90 hover:bg-accent text-white shadow-lg shadow-accent/20 border-none"
                >
                    Editar Asociado
                </flux:button>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 border-t border-gray-200 dark:border-zinc-700 bg-white/50 dark:bg-zinc-800/50">
        <div class="p-8 flex flex-col gap-2 border-b md:border-b-0 md:border-r border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                <x-heroicon-m-banknotes class="size-5"/>
                Sueldo Base
            </div>
            <p class="text-3xl font-bold tracking-tight">{{ number_format($associate->sueldo_base, 2) }}</p>
        </div>
        <div class="p-8 flex flex-col gap-2 border-b md:border-b-0 md:border-r border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                <x-heroicon-m-user class="size-5"/>
                Aporte Personal
            </div>
            <p class="text-3xl font-bold tracking-tight">{{ number_format($associate->porcen_apopers, 2) }}%</p>
        </div>
        <div class="p-8 flex flex-col gap-2">
            <div class="flex items-center gap-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                <x-heroicon-m-building-office class="size-5"/>
                Aporte Patronal
            </div>
            <p class="text-3xl font-bold tracking-tight">{{ number_format($associate->porcen_apopatr, 2) }}%</p>
        </div>
    </div>

    <!-- Main Content Card -->
    <flux:card class="overflow-hidden">
        <!-- Personal Information -->
        <div class="border-b border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-3 p-6 border-b border-gray-200 dark:border-zinc-700">
                <x-heroicon-s-user-circle class="size-6 text-gray-400"/>
                <flux:heading class="!mb-0">Información Personal</flux:heading>
            </div>
            <div class="p-6">
                <dl class="grid gap-6">
                    <div class="flex items-center justify-between">
                        <dt class="text-sm font-medium text-gray-500">Fecha de Nacimiento</dt>
                        <dd class="text-sm font-semibold">{{ $associate->fecha_nac ? $associate->fecha_nac : 'No especificada' }}</dd>
                    </div>
                    <div class="flex items-center justify-between">
                        <dt class="text-sm font-medium text-gray-500">Estado Civil</dt>
                        <dd class="text-sm font-semibold">{{ $associate->estado_civil ? 'Casado(a)' : 'Soltero(a)' }}</dd>
                    </div>
                    <div class="flex items-center justify-between">
                        <dt class="text-sm font-medium text-gray-500">Sexo</dt>
                        <dd class="text-sm font-semibold">{{ $associate->sexo ? 'Masculino' : 'Femenino' }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="border-b border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-3 p-6 border-b border-gray-200 dark:border-zinc-700">
                <x-heroicon-s-phone class="size-6 text-gray-400"/>
                <flux:heading class="!mb-0">Información de Contacto</flux:heading>
            </div>
            <div class="p-6">
                <dl class="grid gap-6">
                    <div class="grid gap-2">
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="text-sm font-semibold flex items-center gap-2">
                            <x-heroicon-m-envelope class="size-4 text-gray-400"/>
                            {{ $associate->email ?: 'No especificado' }}
                        </dd>
                    </div>
                    <div class="grid gap-2">
                        <dt class="text-sm font-medium text-gray-500">Teléfonos</dt>
                        <dd class="grid gap-3">
                            <div class="flex items-center gap-2 text-sm">
                                <x-heroicon-m-device-phone-mobile class="size-4 text-gray-400"/>
                                <span class="font-semibold">{{ $associate->numero_telefpers ?: 'No especificado' }}</span>
                                <span class="text-xs text-gray-500">(Personal)</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <x-heroicon-m-building-office class="size-4 text-gray-400"/>
                                <span class="font-semibold">{{ $associate->numero_telefofi ?: 'No especificado' }}</span>
                                <span class="text-xs text-gray-500">(Oficina)</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <x-heroicon-m-phone class="size-4 text-gray-400"/>
                                <span class="font-semibold">{{ $associate->numero_telefcont ?: 'No especificado' }}</span>
                                <span class="text-xs text-gray-500">(Contacto)</span>
                            </div>
                        </dd>
                    </div>
                    <div class="grid gap-2">
                        <dt class="text-sm font-medium text-gray-500">Dirección</dt>
                        <dd class="text-sm font-semibold flex items-start gap-2">
                            <x-heroicon-m-map-pin class="size-4 text-gray-400 mt-0.5"/>
                            <span>{{ $associate->direccion ?: 'No especificada' }}</span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="border-b border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-3 p-6 border-b border-gray-200 dark:border-zinc-700">
                <x-heroicon-s-banknotes class="size-6 text-gray-400"/>
                <flux:heading class="!mb-0">Información Financiera</flux:heading>
            </div>
            <div class="p-6">
                <dl class="grid gap-6">
                    <div class="flex items-center justify-between">
                        <dt class="text-sm font-medium text-gray-500">Ingresos Adicionales</dt>
                        <dd class="text-sm font-semibold">{{ number_format($associate->ingresos_adic, 2) }}</dd>
                    </div>
                    <div class="grid gap-2">
                        <dt class="text-sm font-medium text-gray-500">Información Bancaria</dt>
                        <dd class="flex items-center gap-4 text-sm">
                            <div class="flex items-center gap-2">
                                <x-heroicon-m-building-library class="size-4 text-gray-400"/>
                                <span class="font-semibold">{{ $associate->codigo_banco ?: 'No especificado' }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <x-heroicon-m-credit-card class="size-4 text-gray-400"/>
                                <span class="font-semibold">{{ $associate->numero_cuenta ?: 'No especificado' }}</span>
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Work Information -->
        <div class="border-b border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-3 p-6 border-b border-gray-200 dark:border-zinc-700">
                <x-heroicon-s-briefcase class="size-6 text-gray-400"/>
                <flux:heading class="!mb-0">Información Laboral</flux:heading>
            </div>
            <div class="p-6">
                <dl class="grid gap-6">
                    <div class="grid grid-cols-2 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Número de Expediente</dt>
                            <dd class="text-sm font-semibold">{{ $associate->numero_exp ?: 'No especificado' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Código de Nómina</dt>
                            <dd class="text-sm font-semibold">{{ $associate->codigo_nomina ?: 'No especificado' }}</dd>
                        </div>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 mb-1">Cargo</dt>
                        <dd class="text-sm font-semibold">{{ $associate->cargo_delsocio ?: 'No especificado' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 mb-1">Profesión</dt>
                        <dd class="text-sm font-semibold">{{ $associate->profesion ?: 'No especificada' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 mb-1">Fecha de Ingreso</dt>
                        <dd class="text-sm font-semibold">{{ $associate->fecha_ingreso ? $associate->fecha_ingreso : 'No especificada' }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Contributions Information -->
        <div class="border-b border-gray-200 dark:border-zinc-700">
            <div class="flex items-center gap-3 p-6 border-b border-gray-200 dark:border-zinc-700">
                <x-heroicon-s-chart-bar class="size-6 text-gray-400"/>
                <flux:heading class="!mb-0">Información de Aportes</flux:heading>
            </div>
            <div class="p-6">
                <dl class="grid gap-6">
                    <div class="flex items-center justify-between">
                        <dt class="text-sm font-medium text-gray-500">Aporte Extraordinario</dt>
                        <dd class="text-sm font-semibold">{{ number_format($associate->porcen_apoextrd, 2) }}%</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Observations -->
        <div>
            <div class="flex items-center gap-3 p-6 border-b border-gray-200 dark:border-zinc-700">
                <x-heroicon-s-clipboard-document-list class="size-6 text-gray-400"/>
                <flux:heading class="!mb-0">Observaciones</flux:heading>
            </div>
            <div class="p-6">
                <div class="prose prose-sm max-w-none dark:prose-invert">
                    <p class="text-sm">{{ $associate->observaciones ?: 'Sin observaciones' }}</p>
                </div>
            </div>
        </div>
    </flux:card>
</div>
