<x-form wire:submit="saveLaboralData" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <flux:input
            wire:model="numero_exp"
            label="Número de Expediente"
            placeholder="Ingrese el número"
        />
        <flux:input
            wire:model="codigo_nomina"
            label="Código de Nómina"
            placeholder="Ingrese el código"
        />
        <flux:input
            type="date"
            wire:model="fecha_ingreso"
            label="Fecha de Ingreso"
        />
        <flux:input
            type="number"
            wire:model="sueldo_base"
            label="Sueldo Base"
            placeholder="0.00"
        />
        <flux:input
            type="number"
            wire:model="ingresos_adic"
            label="Ingresos Adicionales"
            placeholder="0.00"
        />
    <flux:select
            wire:model="tipo_pers"
            label="Tipo de Persona"
            placeholder="Seleccione el tipo de persona"
        >
            @foreach($person_types as $type)
                <flux:select.option value="{{ $type->codigo_v }}">{{ $type->descri_v }}</flux:select.option>
            @endforeach
        </flux:select>

        <flux:select
            wire:model="codigo_dep"
            label="Departamento"
            placeholder="Seleccione el departamento"
        >
            @foreach($departments as $department)
                <flux:select.option value="{{ $department->codigo_v }}">{{ $department->descri_v }}</flux:select.option>
            @endforeach
        </flux:select>
    </div>
    <div class="flex justify-end">
        <flux:button type="submit" variant="primary">
            Guardar
        </flux:button>
    </div>
</x-form>
