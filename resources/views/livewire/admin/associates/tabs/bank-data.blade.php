<x-form wire:submit="saveBankData" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <flux:select
            wire:model="codigo_banco"
            label="Banco"
            placeholder="Seleccione el banco"
        >
            @foreach($banks as $bank)
                <flux:select.option value="{{ $bank->codigo_banco }}">{{ $bank->nombre_banco }}</flux:select.option>
            @endforeach
        </flux:select>

        <flux:input
            wire:model="numero_cuenta"
            label="Número de Cuenta"
            placeholder="Ingrese el número"
        />
    </div>
    <div class="flex justify-end">
        <flux:button type="submit" variant="primary">
            Guardar
        </flux:button>
    </div>
</x-form>
