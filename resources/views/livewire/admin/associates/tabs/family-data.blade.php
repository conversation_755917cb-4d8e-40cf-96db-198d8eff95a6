<div class="space-y-6">
    <div class="flex justify-end">
        <flux:modal.trigger name="add-family-member">
            <flux:button
                variant="primary"
            >
                {{ __('associates.add_family_member') }}
            </flux:button>
        </flux:modal.trigger>
    </div>

    <flux:table :paginate="$this->familyMembers">
        <flux:table.columns>
            <flux:table.column sortable :sorted="$sortBy === 'cedula'" :direction="$sortDirection" wire:click="sort('cedula')">Cédula</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'nombre_completo'" :direction="$sortDirection" wire:click="sort('nombre_completo')">Nombre</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'parentesco'" :direction="$sortDirection" wire:click="sort('parentesco')">Parentesco</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'genero'" :direction="$sortDirection" wire:click="sort('genero')">Genero</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">Tipo</flux:table.column>
            <flux:table.column>Acciones</flux:table.column>
        </flux:table.columns>

        <flux:table.rows>
            @forelse($this->familyMembers as $member)
                <flux:table.row :key="$member->id">
                    <flux:table.cell>{{ $member->family->document_number }}</flux:table.cell>
                    <flux:table.cell>{{ $member->family->first_name }} {{ $member->family->last_name }}</flux:table.cell>
                    <flux:table.cell>{{ $member->relationship->description  }}</flux:table.cell>
                    <flux:table.cell>{{ $member->family->familyGender->descri_v }}</flux:table.cell>
                    <flux:table.cell>{{ $member->familyType->description }}</flux:table.cell>
                    <flux:table.cell>
                        <flux:dropdown position="bottom" align="end">
                            <flux:button variant="primary" size="sm">
                                {{ __('global.actions') }}
                                <x-heroicon-m-chevron-down class="ml-2 -mr-1 h-4 w-4" />
                            </flux:button>

                            <flux:menu>
                                <flux:modal.trigger wire:click="editFamilyMember({{ $member->id }})" >
                                    <flux:menu.item
                                        icon="pencil-square"
                                    >
                                        {{ __('associates.edit') }}
                                    </flux:menu.item>
                                </flux:modal.trigger>
                                <flux:menu.separator />

                                <flux:menu.item
                                    wire:click="confirmDeleteFamilyMember({{ $member->id }})"
                                    icon="trash"
                                    class="text-red-600 dark:text-red-400"
                                >
                                    {{ __('associates.delete') }}
                                </flux:menu.item>
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
            @empty
                <flux:table.row>
                    <flux:table.cell colspan="5" class="text-center">
                        No hay familiares registrados
                    </flux:table.cell>
                </flux:table.row>
            @endforelse
        </flux:table.rows>
    </flux:table>

    <livewire:admin.associates.components.modals.create-family-modal :associate="$associate" />
    <livewire:admin.associates.components.modals.edit-family-modal :associate="$associate"/>
    <livewire:admin.associates.components.modals.delete-family-modal />
</div>
