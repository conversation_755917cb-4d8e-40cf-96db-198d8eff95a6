<div>

    <flux:modal name="edit-family-member">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Editar Familiar</flux:heading>
                <flux:text class="mt-2">Modifique los datos del familiar.</flux:text>
            </div>

            <form wire:submit="save" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex gap-2">
                        <flux:select
                            wire:model="form.document_type"
                            label="Tipo"
                            class="w-24"
                        >
                            <flux:select.option value="V">V</flux:select.option>
                            <flux:select.option value="E">E</flux:select.option>
                        </flux:select>

                        <flux:input
                            wire:model="form.document_number"
                            label="Número de Documento"
                            placeholder="Ingrese el documento"
                        />
                    </div>

                    <flux:input
                        wire:model="form.first_name"
                        label="Nombres"
                        placeholder="Ingrese los nombres"
                    />

                    <flux:input
                        wire:model="form.last_name"
                        label="Apellidos"
                        placeholder="Ingrese los apellidos"
                    />

                    <flux:select
                        wire:model="form.gender_code"
                        label="Género"
                        placeholder="Seleccione el género"
                    >
                        @foreach($genders as $gender)
                            <flux:select.option value="{{ $gender->codigo_v }}">
                                {{ $gender->descri_v }}
                            </flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:input
                        wire:model="form.birth_date"
                        type="date"
                        label="Fecha de Nacimiento"
                    />

                    <flux:select
                        wire:model="form.relationship_id"
                        label="Parentesco"
                        placeholder="Seleccione el parentesco"
                    >
                        @foreach($relationships as $relationship)
                            <flux:select.option value="{{ $relationship->id }}">
                                {{ $relationship->description }}
                            </flux:select.option>
                        @endforeach
                    </flux:select>

                    <div class="col-span-2">
                        <flux:textarea
                            wire:model="form.address"
                            label="Dirección"
                            placeholder="Ingrese la dirección"
                        />
                    </div>

                    <flux:input
                        wire:model="form.home_phone"
                        label="Teléfono de Casa"
                        placeholder="Ingrese el teléfono"
                    />

                    <flux:input
                        wire:model="form.mobile_phone"
                        label="Teléfono Móvil"
                        placeholder="Ingrese el celular"
                    />

                    <flux:input
                        wire:model="form.occupation"
                        label="Ocupación"
                        placeholder="Ingrese la ocupación"
                    />

                    <flux:select
                        wire:model="form.marital_status"
                        label="Estado Civil"
                        placeholder="Seleccione el estado civil"
                    >
                        @foreach($marital_status as $status)
                            <flux:select.option value="{{ $status->codigo_v }}">
                                {{ $status->descri_v }}
                            </flux:select.option>
                        @endforeach
                    </flux:select>
                </div>

                <div class="flex justify-end space-x-3">
                    <flux:button wire:click="$dispatch('closeModal')" type="button">
                        Cancelar
                    </flux:button>
                    <flux:button type="submit" variant="primary">
                        Actualizar Familiar
                    </flux:button>
                </div>
            </form>
        </div>
    </flux:modal>
</div>
