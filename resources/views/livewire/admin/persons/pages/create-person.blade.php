<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-user-plus class="size-6 text-primary-500"/>
                {{ __('Crear Persona') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Registra una nueva persona en el sistema') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.persons.index') }}">{{ __('Personas') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Crear') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card>
        <x-form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Información Personal -->
                <div class="space-y-4">
                    <flux:heading size="lg">Información Personal</flux:heading>

                    <!-- Componente de búsqueda por cédula -->
                    <livewire:components.cedula-search />

                    <div class="border-t border-gray-200 dark:border-gray-700 my-4 pt-4">
                        <flux:input
                            wire:model="name"
                            label="Nombre Completo"
                            placeholder="Ingrese el nombre completo"
                            required
                        />

                        <flux:input
                            wire:model="cedula"
                            label="Cédula"
                            placeholder="Ej. 12345678"
                            type="number"
                            required
                            class="mt-4"
                        />
                    </div>

                    <flux:input
                        wire:model="phone"
                        label="Teléfono"
                        placeholder="Ej. 04141234567"
                        required
                    />

                    <flux:input
                        wire:model="email"
                        label="Correo Electrónico"
                        placeholder="<EMAIL>"
                        type="email"
                    />

                    <flux:textarea
                        wire:model="address"
                        label="Dirección"
                        placeholder="Ingrese la dirección completa"
                        rows="3"
                    />
                </div>

                <!-- Información Electoral -->
                <div class="space-y-4">
                    <flux:heading size="lg">Información Electoral</flux:heading>

                    <flux:input
                        wire:model="polling_center"
                        label="Centro de Votación"
                        placeholder="Ingrese el centro de votación"
                    />

                    <!-- Selector de ubicación (Estado, Municipio, Parroquia) -->
                    <div class="mt-4">
                        <flux:heading size="sm">Ubicación</flux:heading>
                        <livewire:location-selector
                            wire:key="location-selector"
                            :estate-id="$estate_id"
                            :municipality-id="$municipality_id"
                            :parish-id="$parish_id"
                        />
                    </div>

                    <!-- Los campos de texto para municipio y estado han sido eliminados -->
                    <!-- Ahora solo usamos las relaciones con las tablas correspondientes -->

                    <flux:select
                        wire:model="role"
                        label="Rol"
                        required
                    >
                        <flux:select.option value="Votante">Votante</flux:select.option>
                        <flux:select.option value="Militante">Militante</flux:select.option>
                    </flux:select>

                    <flux:input
                        wire:model="party"
                        label="Partido"
                        placeholder="Ingrese el partido político"
                        required
                    />

                    <flux:select
                        wire:model="activity_status"
                        label="Estado de Actividad"
                        required
                    >
                        <flux:select.option value="Activo">Activo</flux:select.option>
                        <flux:select.option value="Inactivo">Inactivo</flux:select.option>
                    </flux:select>
                </div>
            </div>

            <!-- Información 1x10 -->
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:heading size="lg" class="mb-4">Información 1x10</flux:heading>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:select
                            wire:model="responsible_id"
                            label="Responsable 1x10"
                            placeholder="Seleccione un responsable (opcional)"
                        >
                            <flux:select.option value="">Sin responsable</flux:select.option>
                            @foreach($responsibles as $responsible)
                                <flux:select.option value="{{ $responsible->id }}">{{ $responsible->name }} ({{ $responsible->cedula }})</flux:select.option>
                            @endforeach
                        </flux:select>
                    </div>

                    <div>
                        @if($leaderRole)
                            <flux:checkbox
                                wire:model="assign_1x10_leader_role"
                                label="Asignar rol de Líder 1x10"
                                hint="Esta persona será marcada como líder 1x10 y se le asignará el rol correspondiente"
                            />
                        @endif
                    </div>
                </div>

                <div class="mt-4">
                    <flux:textarea
                        wire:model="observations"
                        label="Observaciones"
                        placeholder="Ingrese observaciones adicionales"
                        rows="3"
                    />
                </div>
            </div>

            <div class="flex justify-end space-x-3 pt-6">
                <flux:button href="{{ route('admin.persons.index') }}" variant="primary">
                    Cancelar
                </flux:button>
                <flux:button type="submit" icon="ellipsis-horizontal" variant="primary">
                    Guardar
                </flux:button>
            </div>
        </x-form>
    </flux:card>
</section>
