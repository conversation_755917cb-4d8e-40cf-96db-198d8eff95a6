<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.users class="size-6 text-primary-500"/>
                {{ __('Gestión de Personas') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Administra el registro de personas, militantes y votantes') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" wire:navigate icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Personas') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button
                href="{{ route('admin.persons.create') }}"
                wire:navigate
                icon="plus"
                variant="primary"
            >
                {{ __('Agregar Persona') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-5 overflow-x-auto pb-2">
        <!-- Total de Personas -->
        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-blue-100 dark:border-blue-900/30">
            <flux:text>Total de Personas</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ number_format($this->statistics['total']) }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['total_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-blue-200 dark:text-blue-400" />
                    <flux:chart.area class="text-blue-100 dark:text-blue-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <!-- Militantes -->
        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-green-100 dark:border-green-900/30">
            <flux:text>Militantes</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ number_format($this->statistics['militants']) }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['militants_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-green-200 dark:text-green-400" />
                    <flux:chart.area class="text-green-100 dark:text-green-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <!-- Líderes 1x10 -->
        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-purple-100 dark:border-purple-900/30">
            <flux:text>Líderes 1x10</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ number_format($this->statistics['1x10']) }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['1x10_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-purple-200 dark:text-purple-400" />
                    <flux:chart.area class="text-purple-100 dark:text-purple-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <!-- Votantes -->
        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-yellow-100 dark:border-yellow-900/30">
            <flux:text>Votantes</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ number_format($this->statistics['voters']) }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['voters_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-yellow-200 dark:text-yellow-400" />
                    <flux:chart.area class="text-yellow-100 dark:text-yellow-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <!-- Activos -->
        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-indigo-100 dark:border-indigo-900/30">
            <flux:text>Activos</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ number_format($this->statistics['active']) }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['active_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-indigo-200 dark:text-indigo-400" />
                    <flux:chart.area class="text-indigo-100 dark:text-indigo-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>
    </div>

    <flux:card>
        <!-- Filtros y Búsqueda -->
        <div class="flex flex-col md:flex-row items-center justify-between w-full mb-6 gap-4">
            <div class="w-full md:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por nombre, cédula, email...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            @if(count($selectedItems) > 0)
            <div class="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 p-2 rounded-lg border border-gray-200 dark:border-gray-700">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ count($selectedItems) }} personas seleccionadas</span>

                <flux:button
                    wire:click="resetSelection"
                    variant="primary"
                    size="xs"
                    icon="x-mark"
                >
                    Limpiar
                </flux:button>

                <flux:select wire:model.live="selectedResponsible" class="w-full md:w-auto">
                    <flux:select.option value="">Seleccionar responsable...</flux:select.option>
                    @foreach($responsibles as $responsible)
                        <flux:select.option value="{{ $responsible->id }}">{{ $responsible->name }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:button
                    wire:click="assignResponsible"
                    variant="primary"
                    size="xs"
                    icon="user-plus"
                    :disabled="!$selectedResponsible || empty($selectedPersons)"
                >
                    Asignar
                </flux:button>

                <flux:button
                    wire:click="removeResponsible"
                    variant="danger"
                    size="xs"
                    icon="user-minus"
                    :disabled="empty($selectedItems)"
                >
                    Quitar
                </flux:button>
            </div>
            @endif

            <div class="flex flex-wrap items-center gap-2 w-full md:w-auto">
                <flux:select wire:model.live="role" class="w-full md:w-auto">
                    <flux:select.option value="">Todos los roles</flux:select.option>
                    <flux:select.option value="militant">Militantes</flux:select.option>
                    <flux:select.option value="voter">Votantes</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="status" class="w-full md:w-auto">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    <flux:select.option value="active">Activos</flux:select.option>
                    <flux:select.option value="inactive">Inactivos</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="is1x10" class="w-full md:w-auto">
                    <flux:select.option value="">Todos</flux:select.option>
                    <flux:select.option value="yes">Líderes 1x10</flux:select.option>
                    <flux:select.option value="no">No 1x10</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="perPage" class="w-full md:w-auto">
                    <flux:select.option value="10">10 por página</flux:select.option>
                    <flux:select.option value="25">25 por página</flux:select.option>
                    <flux:select.option value="50">50 por página</flux:select.option>
                    <flux:select.option value="100">100 por página</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Filtros de ubicación -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <flux:select wire:model.live="estate_id">
                <flux:select.option value="">Todos los estados</flux:select.option>
                @foreach($this->estates as $estate)
                    <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                @endforeach
            </flux:select>

            @if($this->estate_id)
                <flux:select wire:model.live="municipality_id">
                    <flux:select.option value="">Todos los municipios</flux:select.option>
                    @foreach($this->municipalities as $municipality)
                        <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            @endif

            @if($this->municipality_id)
                <flux:select wire:model.live="parish_id">
                    <flux:select.option value="">Todas las parroquias</flux:select.option>
                    @foreach($this->parishes as $parish)
                        <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            @endif
        </div>



        <!-- Tabs para separar militantes y votantes -->
        <flux:tab.group>
            <flux:tabs wire:model.live="tab">
                <flux:tab name="all">Todos</flux:tab>
                <flux:tab name="militants">Militantes</flux:tab>
                <flux:tab name="voters">Votantes</flux:tab>
            </flux:tabs>

            <!-- Panel para Todos -->
            <flux:tab.panel name="all">
                <div class="overflow-x-auto">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column class="w-10">
                                <div class="flex items-center">
                                    <input type="checkbox" wire:model.live="selectAll" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                                </div>
                            </flux:table.column>
                            <flux:table.column>{{ __('Nombre') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'cedula' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('cedula')">{{ __('Cédula') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'phone' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('phone')">{{ __('Teléfono') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'role' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('role')">{{ __('Rol') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'activity_status' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('activity_status')">{{ __('Estado') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'is_1x10' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('is_1x10')">{{ __('1x10') }}</flux:table.column>
                            <flux:table.column>{{ __('Responsable') }}</flux:table.column>
                            <flux:table.column>{{ __('Acciones') }}</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            @forelse($this->persons as $person)
                                <flux:table.row wire:key="person-row-{{ $person->id }}" >
                                    <flux:table.cell>
                                        <input type="checkbox" wire:model.live="selectedItems" value="{{ $person->id }}" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                                    </flux:table.cell>
                                    <flux:table.cell class="flex items-center gap-3">
                                        <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300">
                                            {{ substr($person->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $person->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email ?: 'Sin email' }}</div>
                                        </div>
                                    </flux:table.cell>
                                    <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                                    <flux:table.cell>{{ $person->phone ?: 'Sin teléfono' }}</flux:table.cell>
                                    <flux:table.cell>
                                        <flux:badge size="sm" color="{{ $person->role === 'Militant' ? 'green' : 'blue' }}" inset="top bottom">{{ $person->role }}</flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:badge size="sm" color="{{ $person->activity_status === 'Active' ? 'green' : 'red' }}" inset="top bottom">{{ $person->activity_status }}</flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($person->is_1x10)
                                            <flux:badge size="sm" color="purple" inset="top bottom">1x10</flux:badge>
                                        @else
                                            <flux:badge size="sm" color="gray" inset="top bottom">No</flux:badge>
                                        @endif
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($person->responsible)
                                            <div class="flex items-center gap-2">
                                                <div class="h-6 w-6 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-xs">
                                                    {{ substr($person->responsible->name, 0, 1) }}
                                                </div>
                                                <span class="text-sm">{{ $person->responsible->name }}</span>
                                            </div>
                                        @else
                                            <span class="text-sm text-gray-500 dark:text-gray-400">Sin asignar</span>
                                        @endif
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:dropdown position="bottom" align="end">
                                            <flux:button icon="ellipsis-horizontal" variant="primary" size="sm" />
                                            <flux:menu>
                                                <flux:menu.item href="{{ route('admin.persons.show', $person) }}" wire:navigate icon="eye">Ver</flux:menu.item>
                                                <flux:menu.item href="{{ route('admin.persons.edit', $person) }}" wire:navigate icon="pencil-square">Editar</flux:menu.item>
                                            </flux:menu>
                                        </flux:dropdown>
                                    </flux:table.cell>
                                </flux:table.row>
                            @empty
                                <flux:table.row>
                                    <flux:table.cell colspan="9" class="text-center py-8">
                                        <div class="flex flex-col items-center justify-center py-6">
                                            <flux:icon.users class="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
                                            <div class="text-lg font-medium text-gray-500 dark:text-gray-400">No se encontraron personas</div>
                                            <div class="text-sm text-gray-400 dark:text-gray-500 mt-1">Intenta con otros filtros de búsqueda</div>
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforelse
                        </flux:table.rows>
                    </flux:table>
                </div>
            </flux:tab.panel>

            <!-- Panel para Militantes -->
            <flux:tab.panel name="militants">
                <div class="overflow-x-auto">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column class="w-10">
                                <div class="flex items-center">
                                    <input type="checkbox" wire:model.live="selectAll" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                                </div>
                            </flux:table.column>
                            <flux:table.column>{{ __('Nombre') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'cedula' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('cedula')">{{ __('Cédula') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'phone' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('phone')">{{ __('Teléfono') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'activity_status' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('activity_status')">{{ __('Estado') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'is_1x10' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('is_1x10')">{{ __('1x10') }}</flux:table.column>
                            <flux:table.column>{{ __('Responsable') }}</flux:table.column>
                            <flux:table.column>{{ __('Acciones') }}</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            @forelse($this->persons as $person)
                                <flux:table.row wire:key="militant-row-{{ $person->id }}" >
                                    <flux:table.cell>
                                        <input type="checkbox" wire:model.live="selectedPersons" value="{{ $person->id }}" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                                    </flux:table.cell>
                                    <flux:table.cell class="flex items-center gap-3">
                                        <div class="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-700 dark:text-green-300">
                                            {{ substr($person->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $person->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email ?: 'Sin email' }}</div>
                                        </div>
                                    </flux:table.cell>
                                    <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                                    <flux:table.cell>{{ $person->phone ?: 'Sin teléfono' }}</flux:table.cell>
                                    <flux:table.cell>
                                        <flux:badge size="sm" color="{{ $person->activity_status === 'Active' ? 'green' : 'red' }}" inset="top bottom">{{ $person->activity_status }}</flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($person->is_1x10)
                                            <flux:badge size="sm" color="purple" inset="top bottom">1x10</flux:badge>
                                        @else
                                            <flux:badge size="sm" color="gray" inset="top bottom">No</flux:badge>
                                        @endif
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($person->responsible)
                                            <div class="flex items-center gap-2">
                                                <div class="h-6 w-6 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-xs">
                                                    {{ substr($person->responsible->name, 0, 1) }}
                                                </div>
                                                <span class="text-sm">{{ $person->responsible->name }}</span>
                                            </div>
                                        @else
                                            <span class="text-sm text-gray-500 dark:text-gray-400">Sin asignar</span>
                                        @endif
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:dropdown position="bottom" align="end">
                                            <flux:button icon="ellipsis-horizontal" variant="primary" size="sm" />
                                            <flux:menu>
                                                <flux:menu.item href="{{ route('admin.persons.show', $person) }}" wire:navigate icon="eye">Ver</flux:menu.item>
                                                <flux:menu.item href="{{ route('admin.persons.edit', $person) }}" wire:navigate icon="pencil-square">Editar</flux:menu.item>
                                            </flux:menu>
                                        </flux:dropdown>
                                    </flux:table.cell>
                                </flux:table.row>
                            @empty
                                <flux:table.row>
                                    <flux:table.cell colspan="8" class="text-center py-8">
                                        <div class="flex flex-col items-center justify-center py-6">
                                            <flux:icon.users class="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
                                            <div class="text-lg font-medium text-gray-500 dark:text-gray-400">No se encontraron militantes</div>
                                            <div class="text-sm text-gray-400 dark:text-gray-500 mt-1">Intenta con otros filtros de búsqueda</div>
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforelse
                        </flux:table.rows>
                    </flux:table>
                </div>
            </flux:tab.panel>

            <!-- Panel para Votantes -->
            <flux:tab.panel name="voters">
                <div class="overflow-x-auto">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column class="w-10">
                                <div class="flex items-center">
                                    <input type="checkbox" wire:model.live="selectAll" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                                </div>
                            </flux:table.column>
                            <flux:table.column>{{ __('Nombre') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'cedula' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('cedula')">{{ __('Cédula') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'phone' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('phone')">{{ __('Teléfono') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'activity_status' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('activity_status')">{{ __('Estado') }}</flux:table.column>
                            <flux:table.column sortable sorted="{{ $sortField === 'is_1x10' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('is_1x10')">{{ __('1x10') }}</flux:table.column>
                            <flux:table.column>{{ __('Responsable') }}</flux:table.column>
                            <flux:table.column>{{ __('Acciones') }}</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            @forelse($this->persons as $person)
                                <flux:table.row wire:key="voter-row-{{ $person->id }}" >
                                    <flux:table.cell>
                                        <input type="checkbox" wire:model.live="selectedPersons" value="{{ $person->id }}" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                                    </flux:table.cell>
                                    <flux:table.cell class="flex items-center gap-3">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-700 dark:text-blue-300">
                                            {{ substr($person->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $person->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email ?: 'Sin email' }}</div>
                                        </div>
                                    </flux:table.cell>
                                    <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                                    <flux:table.cell>{{ $person->phone ?: 'Sin teléfono' }}</flux:table.cell>
                                    <flux:table.cell>
                                        <flux:badge size="sm" color="{{ $person->activity_status === 'Active' ? 'green' : 'red' }}" inset="top bottom">{{ $person->activity_status }}</flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($person->is_1x10)
                                            <flux:badge size="sm" color="purple" inset="top bottom">1x10</flux:badge>
                                        @else
                                            <flux:badge size="sm" color="gray" inset="top bottom">No</flux:badge>
                                        @endif
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        @if($person->responsible)
                                            <div class="flex items-center gap-2">
                                                <div class="h-6 w-6 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-xs">
                                                    {{ substr($person->responsible->name, 0, 1) }}
                                                </div>
                                                <span class="text-sm">{{ $person->responsible->name }}</span>
                                            </div>
                                        @else
                                            <span class="text-sm text-gray-500 dark:text-gray-400">Sin asignar</span>
                                        @endif
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:dropdown position="bottom" align="end">
                                            <flux:button icon="ellipsis-horizontal" variant="primary" size="sm" />
                                            <flux:menu>
                                                <flux:menu.item href="{{ route('admin.persons.show', $person) }}" wire:navigate icon="eye">Ver</flux:menu.item>
                                                <flux:menu.item href="{{ route('admin.persons.edit', $person) }}" wire:navigate icon="pencil-square">Editar</flux:menu.item>
                                            </flux:menu>
                                        </flux:dropdown>
                                    </flux:table.cell>
                                </flux:table.row>
                            @empty
                                <flux:table.row>
                                    <flux:table.cell colspan="8" class="text-center py-8">
                                        <div class="flex flex-col items-center justify-center py-6">
                                            <flux:icon.users class="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
                                            <div class="text-lg font-medium text-gray-500 dark:text-gray-400">No se encontraron votantes</div>
                                            <div class="text-sm text-gray-400 dark:text-gray-500 mt-1">Intenta con otros filtros de búsqueda</div>
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforelse
                        </flux:table.rows>
                    </flux:table>
                </div>
            </flux:tab.panel>
        </flux:tab.group>

        <div class="mt-6">
            {{ $this->persons->links() }}
        </div>
    </flux:card>
</div>
