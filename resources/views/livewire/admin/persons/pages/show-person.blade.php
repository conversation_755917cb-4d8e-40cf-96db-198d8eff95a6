<section class="w-full animate-fade-in">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-user class="size-6 text-primary-500"/>
                {{ $person->name }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Detalles de la persona') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" wire:navigate icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.persons.index') }}" wire:navigate>{{ __('Personas') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ $person->name }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    @if($showCreateUserPrompt)
    <div class="mb-6">
        <flux:callout icon="user-plus" variant="info">
            <flux:callout.heading>{{ $person->name }} es un líder 1x10 sin cuenta de usuario</flux:callout.heading>
            <flux:callout.text>
                Para que esta persona pueda acceder al sistema como líder 1x10, necesita una cuenta de usuario.
                <div class="mt-2">
                    <flux:button wire:click="openCreateUserModal" variant="filled" size="sm">Crear Usuario Ahora</flux:button>
                </div>
            </flux:callout.text>
        </flux:callout>
    </div>
    @endif

    <div class="flex flex-col md:flex-row gap-6 transition-all duration-500 ease-in-out transform hover:translate-y-[-5px]">
        <!-- Información Personal -->
        <flux:card class="flex-1 border border-primary-100 dark:border-primary-900/30 shadow-sm transition-all duration-300 ease-in-out hover:shadow-md bg-gradient-to-br from-primary-50 to-white dark:from-gray-900 dark:to-gray-800">
            <div class="flex items-center justify-between mb-6">
                <flux:heading size="lg">Información Personal</flux:heading>
                <flux:dropdown position="bottom" align="end">
                    <flux:button icon="ellipsis-horizontal" variant="ghost" size="sm" />
                    <flux:menu>
                        <flux:menu.item href="{{ route('admin.persons.edit', $person) }}" wire:navigate icon="pencil-square">Editar</flux:menu.item>
                    </flux:menu>
                </flux:dropdown>
            </div>

            <div class="flex flex-col items-center mb-6">
                <div class="h-24 w-24 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-3xl font-bold mb-4">
                    {{ substr($person->name, 0, 1) }}
                </div>
                <flux:heading size="xl">{{ $person->name }}</flux:heading>
                <div class="flex items-center gap-2 mt-2">
                    <flux:badge variant="{{ $person->role === 'Militant' ? 'success' : 'info' }}">
                        {{ $person->role }}
                    </flux:badge>
                    <flux:badge variant="{{ $person->activity_status === 'Active' ? 'success' : 'danger' }}">
                        {{ $person->activity_status }}
                    </flux:badge>
                    @if($person->is_1x10)
                        <flux:badge variant="purple">1x10</flux:badge>
                    @endif
                </div>
            </div>

            <div class="space-y-4">
                <div class="flex flex-col">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Cédula</span>
                    <span class="font-medium">{{ $person->cedula }}</span>
                </div>

                <div class="flex flex-col">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Teléfono</span>
                    <span class="font-medium">{{ $person->phone }}</span>
                </div>

                <div class="flex flex-col">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Correo Electrónico</span>
                    <span class="font-medium">{{ $person->email ?: 'No registrado' }}</span>
                </div>

                <div class="flex flex-col">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Dirección</span>
                    <span class="font-medium">{{ $person->address ?: 'No registrada' }}</span>
                </div>

                <div class="flex flex-col">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Fecha de Registro</span>
                    <span class="font-medium">{{ $person->registration_date->format('d/m/Y H:i') }}</span>
                </div>
            </div>
        </flux:card>

        <!-- Información Electoral y 1x10 -->
        <div class="flex-1 space-y-6">
            <flux:card class="border border-blue-100 dark:border-blue-900/30 shadow-sm transition-all duration-300 ease-in-out hover:shadow-md bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
                <flux:heading size="lg" class="mb-6">Información Electoral</flux:heading>

                <div class="space-y-4">
                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Centro de Votación</span>
                        <span class="font-medium">{{ $person->polling_center ?: 'No registrado' }}</span>
                    </div>

                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Estado</span>
                        <span class="font-medium">
                            @if($person->estate_id && is_object($person->estate))
                                {{ $person->estate->name }}
                            @else
                                No registrado
                            @endif
                        </span>
                    </div>

                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Municipio</span>
                        <span class="font-medium">
                            @if($person->municipality_id && is_object($person->municipality))
                                {{ $person->municipality->name }}
                            @else
                                No registrado
                            @endif
                        </span>
                    </div>

                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Parroquia</span>
                        <span class="font-medium">
                            @if($person->parish_id && is_object($person->parish))
                                {{ $person->parish->name }}
                            @else
                                No registrada
                            @endif
                        </span>
                    </div>

                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Partido</span>
                        <span class="font-medium">{{ $person->party }}</span>
                    </div>
                </div>
            </flux:card>

            <flux:card class="border border-purple-100 dark:border-purple-900/30 shadow-sm transition-all duration-300 ease-in-out hover:shadow-md bg-gradient-to-br from-purple-50 to-white dark:from-gray-900 dark:to-gray-800">
                <div class="flex items-center justify-between mb-6">
                    <flux:heading size="lg">Información 1x10</flux:heading>
                    @if($person->is_1x10)
                        <div class="flex gap-2">
                            @if($person->user)
                                <flux:dropdown position="bottom" align="end">
                                    <flux:button icon="user" variant="primary" size="sm">Gestionar Usuario</flux:button>
                                    <flux:menu>
                                        <flux:menu.item wire:click="openEditUserModal" icon="pencil-square">Editar Usuario</flux:menu.item>
                                        <flux:menu.item wire:click="openChangePasswordModal" icon="key">Cambiar Contraseña</flux:menu.item>
                                    </flux:menu>
                                </flux:dropdown>
                            @else
                                <flux:button wire:click="openCreateUserModal" icon="user-plus" variant="primary" size="sm">Crear Usuario</flux:button>
                            @endif
                        </div>
                    @endif
                </div>

                <div class="space-y-4">
                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Es líder 1x10</span>
                        <span class="font-medium">{{ $person->is_1x10 ? 'Sí' : 'No' }}</span>
                    </div>

                    @if($person->user)
                    <div class="flex flex-col">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Rol de Líder 1x10</span>
                        <span class="font-medium">
                            @if($person->user->hasRole('1x10_leader'))
                                <div class="flex items-center gap-2">
                                    <span>Asignado</span>
                                    <flux:badge variant="success">Activo</flux:badge>
                                </div>
                            @else
                                <div class="flex items-center gap-2">
                                    <span>No asignado</span>
                                    <flux:badge variant="warning">Inactivo</flux:badge>
                                </div>
                            @endif
                        </span>
                    </div>
                    @endif

                    @if($person->is_1x10)
                        <div class="flex flex-col">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Cuenta de Usuario</span>
                            @if($person->user)
                                <div class="flex items-center gap-2">
                                    <span class="font-medium">{{ $person->user->username }}</span>
                                    <flux:badge variant="success">Activo</flux:badge>
                                </div>
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ $person->user->email }}</span>
                            @else
                                <div class="flex items-center gap-2">
                                    <span class="font-medium">No tiene</span>
                                    <flux:badge variant="danger">Sin acceso</flux:badge>
                                </div>
                            @endif
                        </div>
                    @endif

                    @if($person->responsible)
                        <div class="flex flex-col">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Responsable 1x10</span>
                            <a href="{{ route('admin.persons.show', $person->responsible) }}" wire:navigate class="font-medium text-primary-600 hover:text-primary-700 dark:text-primary-500 dark:hover:text-primary-400">
                                {{ $person->responsible->name }} ({{ $person->responsible->cedula }})
                            </a>
                        </div>
                    @endif

                    @if($person->is_1x10)
                        <div class="flex flex-col">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Personas a cargo</span>
                            <span class="font-medium">{{ $person->responsibleFor->count() }}</span>
                        </div>
                    @endif

                    @if($person->observations)
                        <div class="flex flex-col">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Observaciones</span>
                            <span class="font-medium">{{ $person->observations }}</span>
                        </div>
                    @endif
                </div>
            </flux:card>
        </div>
    </div>

    @if($person->is_1x10)
        <div class="mt-8">
            <flux:card>
                <div class="flex items-center justify-between mb-6">
                    <flux:heading size="lg" class="flex items-center gap-2">
                        <flux:icon.users class="size-6 text-purple-500" />
                        Patrullados 1x10
                    </flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:badge variant="purple" size="md">
                            {{ $person->responsibleFor->count() }} Personas
                        </flux:badge>
                        <flux:button wire:click="openRegisterPatrulladosModal" icon="user-group" variant="primary" size="sm">Registrar Patrullados</flux:button>
                    </div>
                </div>

                @if($person->responsibleFor->count() > 0)
                    <div class="overflow-x-auto">
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column>Nombre</flux:table.column>
                                <flux:table.column>Cédula</flux:table.column>
                                <flux:table.column>Teléfono</flux:table.column>
                                <flux:table.column>Correo</flux:table.column>
                                <flux:table.column>Rol</flux:table.column>
                                <flux:table.column>Estado</flux:table.column>
                                <flux:table.column>Acciones</flux:table.column>
                            </flux:table.columns>
                            <flux:table.rows>
                                @foreach($person->responsibleFor as $patrullado)
                                    <flux:table.row :key="$patrullado->id">
                                        <flux:table.cell class="font-medium">{{ $patrullado->name }}</flux:table.cell>
                                        <flux:table.cell>{{ $patrullado->cedula }}</flux:table.cell>
                                        <flux:table.cell>{{ $patrullado->phone }}</flux:table.cell>
                                        <flux:table.cell>{{ $patrullado->email ?: 'No registrado' }}</flux:table.cell>
                                        <flux:table.cell>
                                            <flux:badge size="sm" color="{{ $patrullado->role === 'Militante' ? 'green' : 'blue' }}" inset="top bottom">
                                                {{ $patrullado->role }}
                                            </flux:badge>
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:badge size="sm" color="{{ $patrullado->activity_status === 'Activo' ? 'green' : 'red' }}" inset="top bottom">
                                                {{ $patrullado->activity_status }}
                                            </flux:badge>
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <div class="flex items-center gap-2">
                                                <flux:button href="{{ route('admin.persons.show', $patrullado) }}" wire:navigate variant="ghost" size="sm" icon="eye" inset="top bottom"></flux:button>
                                                <flux:button wire:click="removePatrullado({{ $patrullado->id }})" variant="ghost" size="sm" icon="user-minus" inset="top bottom"></flux:button>
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @endforeach
                            </flux:table.rows>
                        </flux:table>
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <flux:icon.users class="mx-auto size-12 text-gray-400 dark:text-gray-600 mb-3" />
                        <p class="text-lg font-medium">No hay patrullados registrados</p>
                        <p class="mt-1">Utilice el botón "Registrar Patrullados" para comenzar a agregar personas.</p>
                    </div>
                @endif
            </flux:card>
        </div>
    @elseif($person->responsible)
        <div class="mt-8">
            <flux:card>
                <div class="flex items-center justify-between mb-6">
                    <flux:heading size="lg" class="flex items-center gap-2">
                        <flux:icon.users class="size-6 text-purple-500" />
                        Compañeros 1x10
                    </flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:badge variant="purple" size="md">
                            {{ $this->teammates->count() + 2 }} Personas
                        </flux:badge>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column>Nombre</flux:table.column>
                            <flux:table.column>Cédula</flux:table.column>
                            <flux:table.column>Teléfono</flux:table.column>
                            <flux:table.column>Correo</flux:table.column>
                            <flux:table.column>Rol</flux:table.column>
                            <flux:table.column>Estado</flux:table.column>
                            <flux:table.column>Acciones</flux:table.column>
                        </flux:table.columns>
                        <flux:table.rows>
                            <!-- Mostrar primero al responsable -->
                            <flux:table.row :key="$person->responsible->id">
                                <flux:table.cell class="font-medium">
                                    {{ $person->responsible->name }}
                                    <flux:badge variant="purple" size="xs" class="ml-2">Responsable</flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>{{ $person->responsible->cedula }}</flux:table.cell>
                                <flux:table.cell>{{ $person->responsible->phone }}</flux:table.cell>
                                <flux:table.cell>{{ $person->responsible->email ?: 'No registrado' }}</flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" color="{{ $person->responsible->role === 'Militante' ? 'green' : 'blue' }}" inset="top bottom">
                                        {{ $person->responsible->role }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" color="{{ $person->responsible->activity_status === 'Activo' ? 'green' : 'red' }}" inset="top bottom">
                                        {{ $person->responsible->activity_status }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <div class="flex items-center gap-2">
                                        <flux:button href="{{ route('admin.persons.show', $person->responsible) }}" wire:navigate variant="ghost" size="sm" icon="eye" inset="top bottom"></flux:button>
                                    </div>
                                </flux:table.cell>
                            </flux:table.row>

                            <!-- Mostrar a la persona actual -->
                            <flux:table.row :key="$person->id">
                                <flux:table.cell class="font-medium">
                                    {{ $person->name }}
                                    <flux:badge variant="success" size="xs" class="ml-2">Tú</flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                                <flux:table.cell>{{ $person->phone }}</flux:table.cell>
                                <flux:table.cell>{{ $person->email ?: 'No registrado' }}</flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" color="{{ $person->role === 'Militante' ? 'green' : 'blue' }}" inset="top bottom">
                                        {{ $person->role }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" color="{{ $person->activity_status === 'Activo' ? 'green' : 'red' }}" inset="top bottom">
                                        {{ $person->activity_status }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <div class="flex items-center gap-2">
                                        <flux:button href="{{ route('admin.persons.edit', $person) }}" wire:navigate variant="ghost" size="sm" icon="pencil-square" inset="top bottom"></flux:button>
                                    </div>
                                </flux:table.cell>
                            </flux:table.row>

                            <!-- Mostrar a los compañeros -->
                            @foreach($this->teammates as $teammate)
                                <flux:table.row :key="$teammate->id">
                                    <flux:table.cell class="font-medium">
                                        {{ $teammate->name }}
                                        <flux:badge variant="info" size="xs" class="ml-2">Compañero</flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>{{ $teammate->cedula }}</flux:table.cell>
                                    <flux:table.cell>{{ $teammate->phone }}</flux:table.cell>
                                    <flux:table.cell>{{ $teammate->email ?: 'No registrado' }}</flux:table.cell>
                                    <flux:table.cell>
                                        <flux:badge size="sm" color="{{ $teammate->role === 'Militante' ? 'green' : 'blue' }}" inset="top bottom">
                                            {{ $teammate->role }}
                                        </flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:badge size="sm" color="{{ $teammate->activity_status === 'Activo' ? 'green' : 'red' }}" inset="top bottom">
                                            {{ $teammate->activity_status }}
                                        </flux:badge>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <div class="flex items-center gap-2">
                                            <flux:button href="{{ route('admin.persons.show', $teammate) }}" wire:navigate variant="ghost" size="sm" icon="eye" inset="top bottom"></flux:button>
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforeach
                        </flux:table.rows>
                    </flux:table>
                </div>
            </flux:card>
        </div>
    @endif
    <!-- Modales para gestión de usuarios -->
    @if($person->is_1x10)
        <!-- Modal para crear usuario -->
        <flux:modal wire:model="showCreateUserModal" title="Crear Usuario" max-width="md">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Crear Usuario para {{ $person->name }}</flux:heading>
                    <flux:text class="mt-2">Crea una cuenta de usuario para este líder 1x10.</flux:text>
                </div>

                <flux:input
                    wire:model="name"
                    label="Nombre"
                    placeholder="Nombre completo"
                    required
                />

                <flux:input
                    wire:model="username"
                    label="Nombre de usuario"
                    placeholder="Nombre de usuario único"
                    required
                />

                <flux:input
                    wire:model="email"
                    label="Correo electrónico"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                />

                <flux:input
                    wire:model="password"
                    label="Contraseña"
                    type="password"
                    placeholder="Mínimo 8 caracteres"
                    required
                />

                <flux:input
                    wire:model="password_confirmation"
                    label="Confirmar contraseña"
                    type="password"
                    placeholder="Confirmar contraseña"
                    required
                />

                <div class="flex justify-end gap-2 mt-4">
                    <flux:button wire:click="$set('showCreateUserModal', false)" variant="primary">
                        Cancelar
                    </flux:button>
                    <flux:button wire:click="createUser" variant="primary">
                        Crear Usuario
                    </flux:button>
                </div>
            </div>
        </flux:modal>

        <!-- Modal para editar usuario -->
        <flux:modal wire:model="showEditUserModal" title="Editar Usuario" max-width="md">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Editar Usuario de {{ $person->name }}</flux:heading>
                    <flux:text class="mt-2">Actualiza los datos del usuario.</flux:text>
                </div>

                <flux:input
                    wire:model="name"
                    label="Nombre"
                    placeholder="Nombre completo"
                    required
                />

                <flux:input
                    wire:model="username"
                    label="Nombre de usuario"
                    placeholder="Nombre de usuario único"
                    required
                />

                <flux:input
                    wire:model="email"
                    label="Correo electrónico"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                />

                <div class="flex justify-end gap-2 mt-4">
                    <flux:button wire:click="$set('showEditUserModal', false)" variant="primary">
                        Cancelar
                    </flux:button>
                    <flux:button wire:click="updateUser" variant="primary">
                        Guardar Cambios
                    </flux:button>
                </div>
            </div>
        </flux:modal>

        <!-- Modal para cambiar contraseña -->
        <flux:modal wire:model="showChangePasswordModal" title="Cambiar Contraseña" max-width="md">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Cambiar Contraseña de {{ $person->name }}</flux:heading>
                    <flux:text class="mt-2">Establece una nueva contraseña para este usuario.</flux:text>
                </div>

                <flux:input
                    wire:model="new_password"
                    label="Nueva contraseña"
                    type="password"
                    placeholder="Mínimo 8 caracteres"
                    required
                />

                <flux:input
                    wire:model="new_password_confirmation"
                    label="Confirmar nueva contraseña"
                    type="password"
                    placeholder="Confirmar nueva contraseña"
                    required
                />

                <div class="flex justify-end gap-2 mt-4">
                    <flux:button wire:click="$set('showChangePasswordModal', false)" variant="primary">
                        Cancelar
                    </flux:button>
                    <flux:button wire:click="changePassword" variant="primary">
                        Cambiar Contraseña
                    </flux:button>
                </div>
            </div>
        </flux:modal>

        <!-- Modal para registrar patrullados 1x10 -->
        <flux:modal wire:model="showRegisterPatrulladosModal" title="Registrar Patrullados 1x10" max-width="md">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Registrar Patrullados para {{ $person->name }}</flux:heading>
                    <flux:text class="mt-2">Busca y asigna personas a este líder 1x10.</flux:text>
                </div>

                <div class="space-y-4">
                    <flux:input
                        wire:model.live.debounce.300ms="searchPatrullado"
                        placeholder="Buscar por nombre o cédula"
                        icon="magnifying-glass"
                    />

                    @if($searchPatrullado && $this->potentialPatrullados->count() > 0)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                            <div class="max-h-60 overflow-y-auto">
                                @foreach($this->potentialPatrullados as $potentialPatrullado)
                                    <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800">
                                        <div>
                                            <div class="font-medium">{{ $potentialPatrullado->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $potentialPatrullado->cedula }}</div>
                                        </div>
                                        <flux:button
                                            wire:click="assignPatrullado({{ $potentialPatrullado->id }})"
                                            variant="priamry"
                                            size="xs"
                                            icon="user-plus"
                                        >
                                            Asignar
                                        </flux:button>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @elseif($searchPatrullado)
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            No se encontraron resultados para "{{ $searchPatrullado }}"
                            <div class="mt-2">
                                <flux:button
                                    wire:click="openCreatePersonModal"
                                    variant="primary"
                                    size="xs"
                                    icon="plus"
                                >
                                    Crear Nueva Persona
                                </flux:button>
                            </div>
                        </div>
                    @endif

                    @if($person->responsibleFor->count() > 0)
                        <div class="mt-6">
                            <flux:heading size="md" class="mb-2">Personas Asignadas ({{ $person->responsibleFor->count() }})</flux:heading>
                            <div class="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                                <div class="max-h-60 overflow-y-auto">
                                    @foreach($person->responsibleFor as $patrullado)
                                        <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800">
                                            <div>
                                                <div class="font-medium">{{ $patrullado->name }}</div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ $patrullado->cedula }}</div>
                                            </div>
                                            <flux:button
                                                wire:click="removePatrullado({{ $patrullado->id }})"
                                                variant="danger"
                                                size="xs"
                                                icon="user-minus"
                                            >
                                                Quitar
                                            </flux:button>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="flex justify-end gap-2 mt-4">
                    <flux:button wire:click="$set('showRegisterPatrulladosModal', false)" variant="primary">
                        Cerrar
                    </flux:button>
                </div>
            </div>
        </flux:modal>

        <!-- Modal para crear nueva persona -->
        <flux:modal wire:model="showCreatePersonModal" title="Crear Nueva Persona" max-width="md">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Crear Nueva Persona</flux:heading>
                    <flux:text class="mt-2">Registra una nueva persona y asígnala a este líder 1x10.</flux:text>
                </div>

                <flux:input
                    wire:model="newPerson.name"
                    label="Nombre"
                    placeholder="Nombre completo"
                    required
                />

                <flux:input
                    wire:model="newPerson.cedula"
                    label="Cédula"
                    placeholder="Número de cédula"
                    type="number"
                    required
                />

                <flux:input
                    wire:model="newPerson.phone"
                    label="Teléfono"
                    placeholder="Número de teléfono"
                    required
                />

                <flux:input
                    wire:model="newPerson.email"
                    label="Correo electrónico"
                    type="email"
                    placeholder="<EMAIL>"
                />

                <flux:select
                    wire:model="newPerson.role"
                    label="Rol"
                    required
                >
                    <flux:select.option value="Votante">Votante</flux:select.option>
                    <flux:select.option value="Militante">Militante</flux:select.option>
                </flux:select>

                <div class="flex justify-end gap-2 mt-4">
                    <flux:button wire:click="$set('showCreatePersonModal', false)" variant="primary">
                        Cancelar
                    </flux:button>
                    <flux:button wire:click="createAndAssignPerson" variant="primary">
                        Crear y Asignar
                    </flux:button>
                </div>
            </div>
        </flux:modal>
    @endif
</section>
