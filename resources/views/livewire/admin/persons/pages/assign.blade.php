<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-user-group class="size-6 text-primary-500"/>
                {{ __('Asignar 1x10') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Asigna responsables 1x10 a las personas') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.persons.index') }}">{{ __('Personas') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Asignar 1x10') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button wire:click="openCreatePersonModal" variant="primary" icon="user-plus">
                {{ __('Crear Persona') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Mensaje informativo -->
    <flux:card>
        <div class="flex items-start gap-4">
            <div class="flex-shrink-0">
                <flux:icon.information-circle class="h-6 w-6 text-primary-500" />
            </div>
            <div>
                <flux:heading size="sm">Cómo asignar personas a un responsable 1x10</flux:heading>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Para asignar personas a un responsable 1x10, sigue estos pasos:
                </p>
                <ol class="mt-2 text-sm text-gray-600 dark:text-gray-400 list-decimal list-inside space-y-1">
                    <li>Selecciona un responsable 1x10 de la lista de la izquierda</li>
                    <li>Marca las casillas de las personas que deseas asignar a ese responsable</li>
                    <li>Haz clic en el botón <span class="font-semibold text-primary-600 dark:text-primary-400">"Asignar a Responsable 1x10"</span> que aparece en la parte superior de la tabla</li>
                </ol>
            </div>
        </div>
    </flux:card>

    <!-- Estadísticas Mejoradas -->
    <flux:card>
        <div class="flex justify-between items-center mb-4">
            <flux:heading >Estadísticas de Asignación 1x10</flux:heading>
            <flux:button wire:click="refreshStatistics" variant="ghost" size="xs" icon="arrow-path" class="text-xs">
                Actualizar
            </flux:button>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <!-- Total Personas -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs font-medium text-blue-600 dark:text-blue-400">Total Personas</div>
                        <div class="mt-1 text-2xl font-bold text-blue-700 dark:text-blue-300">{{ number_format($this->statistics['total_persons']) }}</div>
                    </div>
                    <div class="bg-blue-100 dark:bg-blue-800/50 p-2 rounded-lg">
                        <flux:icon.users class="h-6 w-6 text-blue-500 dark:text-blue-400" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-blue-600 dark:text-blue-400">En el sistema</div>
                <div class="mt-2 h-1 w-full bg-blue-200 dark:bg-blue-700 rounded-full overflow-hidden">
                    <div class="h-full bg-blue-500 rounded-full" style="width: 100%"></div>
                </div>
            </div>

            <!-- Con Responsable -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-800 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs font-medium text-green-600 dark:text-green-400">Con Responsable</div>
                        <div class="mt-1 text-2xl font-bold text-green-700 dark:text-green-300">{{ number_format($this->statistics['with_responsible']) }}</div>
                    </div>
                    <div class="bg-green-100 dark:bg-green-800/50 p-2 rounded-lg">
                        <flux:icon.user-circle class="h-6 w-6 text-green-500 dark:text-green-400" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-green-600 dark:text-green-400">
                    {{ round(($this->statistics['with_responsible'] / max(1, $this->statistics['total_persons'])) * 100) }}% del total
                </div>
                <div class="mt-2 h-1 w-full bg-green-200 dark:bg-green-700 rounded-full overflow-hidden">
                    <div class="h-full bg-green-500 rounded-full" style="width: {{ round(($this->statistics['with_responsible'] / max(1, $this->statistics['total_persons'])) * 100) }}%"></div>
                </div>
            </div>

            <!-- Sin Responsable -->
            <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-800 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs font-medium text-red-600 dark:text-red-400">Sin Responsable</div>
                        <div class="mt-1 text-2xl font-bold text-red-700 dark:text-red-300">{{ number_format($this->statistics['without_responsible']) }}</div>
                    </div>
                    <div class="bg-red-100 dark:bg-red-800/50 p-2 rounded-lg">
                        <flux:icon.user-minus class="h-6 w-6 text-red-500 dark:text-red-400" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-red-600 dark:text-red-400">
                    {{ round(($this->statistics['without_responsible'] / max(1, $this->statistics['total_persons'])) * 100) }}% del total
                </div>
                <div class="mt-2 h-1 w-full bg-red-200 dark:bg-red-700 rounded-full overflow-hidden">
                    <div class="h-full bg-red-500 rounded-full" style="width: {{ round(($this->statistics['without_responsible'] / max(1, $this->statistics['total_persons'])) * 100) }}%"></div>
                </div>
            </div>

            <!-- Líderes 1x10 -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs font-medium text-purple-600 dark:text-purple-400">Líderes 1x10</div>
                        <div class="mt-1 text-2xl font-bold text-purple-700 dark:text-purple-300">{{ number_format($this->statistics['total_1x10']) }}</div>
                    </div>
                    <div class="bg-purple-100 dark:bg-purple-800/50 p-2 rounded-lg">
                        <flux:icon.user-group class="h-6 w-6 text-purple-500 dark:text-purple-400" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-purple-600 dark:text-purple-400">
                    {{ round(($this->statistics['total_1x10'] / max(1, $this->statistics['total_persons'])) * 100) }}% del total
                </div>
                <div class="mt-2 h-1 w-full bg-purple-200 dark:bg-purple-700 rounded-full overflow-hidden">
                    <div class="h-full bg-purple-500 rounded-full" style="width: {{ round(($this->statistics['total_1x10'] / max(1, $this->statistics['total_persons'])) * 100) }}%"></div>
                </div>
            </div>

            <!-- Promedio de Asignaciones -->
            <div class="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs font-medium text-amber-600 dark:text-amber-400">Promedio por Líder</div>
                        <div class="mt-1 text-2xl font-bold text-amber-700 dark:text-amber-300">
                            {{ number_format($this->statistics['total_1x10'] > 0 ? $this->statistics['with_responsible'] / $this->statistics['total_1x10'] : 0, 1) }}
                        </div>
                    </div>
                    <div class="bg-amber-100 dark:bg-amber-800/50 p-2 rounded-lg">
                        <flux:icon.chart-bar class="h-6 w-6 text-amber-500 dark:text-amber-400" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-amber-600 dark:text-amber-400">
                    Personas por líder 1x10
                </div>
                <div class="mt-2 h-1 w-full bg-amber-200 dark:bg-amber-700 rounded-full overflow-hidden">
                    <div class="h-full bg-amber-500 rounded-full" style="width: {{ min(100, ($this->statistics['total_1x10'] > 0 ? $this->statistics['with_responsible'] / $this->statistics['total_1x10'] : 0) * 10) }}%"></div>
                </div>
            </div>

            <!-- Cobertura -->
            <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="text-xs font-medium text-emerald-600 dark:text-emerald-400">Cobertura</div>
                        <div class="mt-1 text-2xl font-bold text-emerald-700 dark:text-emerald-300">
                            {{ round(($this->statistics['with_responsible'] / max(1, $this->statistics['total_persons'])) * 100) }}%
                        </div>
                    </div>
                    <div class="bg-emerald-100 dark:bg-emerald-800/50 p-2 rounded-lg">
                        <flux:icon.check-circle class="h-6 w-6 text-emerald-500 dark:text-emerald-400" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-emerald-600 dark:text-emerald-400">
                    Personas con responsable asignado
                </div>
                <div class="mt-2 h-1 w-full bg-emerald-200 dark:bg-emerald-700 rounded-full overflow-hidden">
                    <div class="h-full bg-emerald-500 rounded-full" style="width: {{ round(($this->statistics['with_responsible'] / max(1, $this->statistics['total_persons'])) * 100) }}%"></div>
                </div>
            </div>
        </div>
    </flux:card>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        <!-- Selección de Responsable -->
        <flux:card class="sm:col-span-1 lg:col-span-1 xl:col-span-1 h-full">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <flux:heading >Líderes 1x10</flux:heading>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Selecciona un líder para asignarle personas</p>
                </div>
                <flux:badge  color="purple" inset="left right" class="animate-pulse">
                    {{ count($this->responsibles) }} Líderes
                </flux:badge>
            </div>

            <div class="mb-4">
                <div class="relative">
                    <flux:input
                        wire:model.live.debounce.300ms="searchResponsible"
                        placeholder="Buscar por nombre o cédula..."
                        icon="magnifying-glass"
                        class="w-full"
                    />
                    @if($searchResponsible)
                        <flux:button
                            wire:click="$set('searchResponsible', '')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                            <flux:icon.x-mark class="h-5 w-5" />
                        </flux:button>
                    @endif
                </div>

                <div class="mt-3">
                    <flux:label>Filtrar responsables por</flux:label>
                    <flux:select
                        wire:model.live="searchResponsible"
                        class="w-full shadow-sm border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"
                    >
                        <flux:select.option value="">Todos los responsables</flux:select.option>
                        <flux:select.option value="sin_asignaciones_especial">Sin asignaciones</flux:select.option>
                        <flux:select.option value="mas_asignaciones_especial">Más asignaciones</flux:select.option>
                        <flux:select.option value="recientes_especial">Agregados recientemente</flux:select.option>
                    </flux:select>
                </div>

                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        {{ count($this->responsibles) }} líderes encontrados
                    </div>
                    <flux:button
                        wire:click="openCreateResponsibleModal"
                        size="xs"
                        variant="primary"
                        icon="plus"
                        class="text-xs"
                    >
                        Nuevo líder
                    </flux:button>
                </div>
            </div>

            <div class="space-y-3 max-h-[calc(100vh-35rem)] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                @forelse($this->responsibles as $responsible)
                    <div
                        wire:key="responsible-{{ $responsible->id }}"
                        wire:click="selectResponsible({{ $responsible->id }})"
                        class="p-3 rounded-lg border cursor-pointer transition-all duration-200 {{ $selectedResponsible == $responsible->id ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 shadow-md transform scale-[1.02]' : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-sm' }}"
                    >
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12 relative">
                                    <div class="h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-lg font-medium shadow-sm {{ $selectedResponsible == $responsible->id ? 'ring-2 ring-primary-500' : '' }}">
                                        {{ substr($responsible->name, 0, 1) }}
                                    </div>
                                    @if($selectedResponsible == $responsible->id)
                                        <div class="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-primary-500 flex items-center justify-center text-white shadow-sm">
                                            <flux:icon.check class="h-3 w-3" />
                                        </div>
                                    @endif
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center gap-1">
                                        {{ $responsible->name }}
                                        @if($selectedResponsible == $responsible->id)
                                            <span class="text-xs text-primary-600 dark:text-primary-400 font-normal">(Seleccionado)</span>
                                        @endif
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                        {{ $responsible->cedula }}
                                    </div>
                                    @if($responsible->phone)
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-0.5 flex items-center">
                                            <flux:icon.phone class="h-3 w-3 mr-1" />
                                            {{ $responsible->phone }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="flex flex-col items-end">
                                <flux:badge size="sm" color="{{ $responsible->responsibleFor->count() > 0 ? 'indigo' : 'gray' }}" inset="left right">
                                    <div class="flex items-center gap-1">
                                        <flux:icon.users class="h-3 w-3" />
                                        <span>{{ $responsible->responsibleFor->count() }}</span>
                                    </div>
                                </flux:badge>
                                @if($responsible->responsibleFor->count() > 0)
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Personas asignadas
                                    </div>
                                @else
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Sin asignaciones
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8 px-4">
                        <flux:icon.user-group class="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <div class="text-gray-500 dark:text-gray-400 text-sm font-medium">
                            No se encontraron líderes 1x10
                        </div>
                        <div class="text-gray-400 dark:text-gray-500 text-xs mt-1">
                            Intenta con otros filtros o crea un nuevo líder
                        </div>
                        <div class="mt-4">
                            <flux:button
                                wire:click="openCreateResponsibleModal"
                                variant="primary"
                                size="sm"
                                icon="plus"
                            >
                                Crear Líder 1x10
                            </flux:button>
                        </div>
                    </div>
                @endforelse
            </div>

            @if($this->selectedResponsiblePerson)
                <div class="mt-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-200 dark:border-primary-800">
                    <div class="flex justify-between items-center mb-4">
                        <flux:heading size="sm">Responsable Seleccionado</flux:heading>
                        <flux:badge  color="purple" inset="left right">
                            {{ $this->selectedResponsiblePerson->responsibleFor->count() }} personas asignadas
                        </flux:badge>
                    </div>

                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 h-16 w-16">
                            <div class="h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-2xl font-bold shadow-md">
                                {{ substr($this->selectedResponsiblePerson->name, 0, 1) }}
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                {{ $this->selectedResponsiblePerson->name }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $this->selectedResponsiblePerson->cedula }}
                            </div>
                            <div class="flex items-center gap-2 mt-1">
                                @if($this->selectedResponsiblePerson->phone)
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                        <flux:icon.phone class="h-4 w-4 mr-1" /> {{ $this->selectedResponsiblePerson->phone }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Personas ya asignadas a este responsable -->
                    @if($this->selectedResponsiblePerson->responsibleFor->count() > 0)
                        <div class="mt-4">
                            <flux:heading size="xs" class="mb-2">Personas ya asignadas</flux:heading>
                            <div class="max-h-60 overflow-y-auto pr-2 space-y-2">
                                @foreach($this->selectedResponsiblePerson->responsibleFor->take(10) as $person)
                                    <div class="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8">
                                                <div class="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-sm">
                                                    {{ substr($person->name, 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $person->name }}
                                                </div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ $person->cedula }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                                @if($this->selectedResponsiblePerson->responsibleFor->count() > 10)
                                    <div class="text-center py-2 text-sm text-gray-500 dark:text-gray-400">
                                        + {{ $this->selectedResponsiblePerson->responsibleFor->count() - 10 }} personas más
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            @endif
        </flux:card>

        <!-- Selección de Personas -->
        <flux:card class="sm:col-span-2 lg:col-span-2 xl:col-span-3 h-full">
            <div class="flex justify-between items-center mb-4">
                <flux:heading >Seleccionar Personas</flux:heading>
                <flux:badge  color="blue" inset="left right">{{ $persons->total() }} resultados</flux:badge>
            </div>

            <!-- Filtros Avanzados -->
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <flux:heading size="sm">Filtros Avanzados</flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:badge  color="blue" inset="left right">{{ $persons->total() }} resultados</flux:badge>
                        <flux:button wire:click="resetFilters" variant="subtle" size="xs" icon="x-mark">Limpiar</flux:button>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <flux:label>Buscar</flux:label>
                        <div class="relative">
                            <flux:input
                                wire:model.live.debounce.300ms="search"
                                placeholder="Nombre, cédula, teléfono..."
                                icon="magnifying-glass"
                                class="w-full"
                            />
                        </div>
                    </div>

                    <div>
                        <flux:label>Estado de asignación</flux:label>
                        <flux:select
                            wire:model.live="filterResponsible"
                            class="w-full shadow-sm border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"
                        >
                            <flux:select.option value="all">Todos</flux:select.option>
                            <flux:select.option value="without">Sin asignar</flux:select.option>
                            <flux:select.option value="with">Asignados</flux:select.option>
                        </flux:select>
                    </div>

                    <div>
                        <flux:label>Tipo de persona</flux:label>
                        <flux:select
                            wire:model.live="filter1x10"
                            class="w-full shadow-sm border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"
                        >
                            <flux:select.option value="all">Todos</flux:select.option>
                            <flux:select.option value="no">Votantes</flux:select.option>
                            <flux:select.option value="yes">Líderes 1x10</flux:select.option>
                        </flux:select>
                    </div>

                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <flux:label>Rol</flux:label>
                            <flux:select wire:model.live="filterRole">
                                <flux:select.option value="all">Todos los roles</flux:select.option>
                                @foreach($this->availableRoles as $role)
                                    <flux:select.option value="{{ $role }}">{{ $role }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </div>
                        <div>
                            <flux:label>Por página</flux:label>
                            <flux:select wire:model.live="perPage">
                                <flux:select.option value="10">10</flux:select.option>
                                <flux:select.option value="25">25</flux:select.option>
                                <flux:select.option value="50">50</flux:select.option>
                                <flux:select.option value="100">100</flux:select.option>
                            </flux:select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4 shadow-sm">
                <div class="flex flex-col md:flex-row justify-between gap-4">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                        <div class="flex items-center gap-2 bg-gray-50 dark:bg-gray-900 px-3 py-2 rounded-lg">
                            <flux:icon.user-group class="h-5 w-5 text-primary-500" />
                            <div>
                                <span class="text-sm font-medium">Seleccionados:</span>
                                <span class="ml-1 text-lg font-bold {{ count($selectedPersons) > 0 ? 'text-primary-600 dark:text-primary-400' : 'text-gray-400 dark:text-gray-600' }}">
                                    {{ count($selectedPersons) }}
                                </span>
                            </div>
                        </div>

                        @if(count($selectedPersons) > 0)
                            <div class="flex flex-wrap gap-2">
                                <flux:button
                                    wire:click="resetSelection"
                                    variant="ghost"
                                    size="sm"
                                    icon="x-mark"
                                >
                                    Limpiar selección
                                </flux:button>

                                <flux:button
                                    wire:click="selectAllInPage"
                                    variant="ghost"
                                    size="sm"
                                    icon="check-circle"
                                >
                                    Seleccionar página
                                </flux:button>

                                <flux:button
                                    wire:click="openExportModal"
                                    variant="ghost"
                                    size="sm"
                                    icon="arrow-down-tray"
                                >
                                    Exportar
                                </flux:button>
                            </div>
                        @endif
                    </div>

                    <div class="flex flex-wrap gap-3">
                        <flux:button
                            wire:click="confirmRemoveResponsible"
                            variant="danger"

                            :disabled="empty($selectedPersons)"
                            class="{{ empty($selectedPersons) ? 'opacity-50 cursor-not-allowed' : 'shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200' }}"
                        >
                            <div class="flex items-center gap-2">
                                <flux:icon.user-minus class="h-5 w-5" />
                                <span class="font-medium">Quitar Responsable</span>
                                @if(!empty($selectedPersons))
                                    <span class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-white text-red-600 text-xs font-bold ml-1 shadow-sm">{{ count($selectedPersons) }}</span>
                                @endif
                            </div>
                        </flux:button>

                        <flux:button
                            wire:click="confirmAssignResponsible"
                            variant="primary"

                            :disabled="!$selectedResponsible || empty($selectedPersons)"
                            class="{{ (!$selectedResponsible || empty($selectedPersons)) ? 'opacity-50 cursor-not-allowed' : 'shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200' }}"
                        >
                            <div class="flex items-center gap-2">
                                <flux:icon.user-plus class="h-5 w-5" />
                                <span class="font-medium">Asignar a Responsable 1x10</span>
                                @if($selectedResponsible && !empty($selectedPersons))
                                    <span class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-white text-primary-600 text-xs font-bold ml-1 shadow-sm">{{ count($selectedPersons) }}</span>
                                @endif
                            </div>
                        </flux:button>
                    </div>
                </div>
            </div>

            <!-- Botones flotantes para acciones rápidas -->
            <div class="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
                <!-- Botón flotante para quitar responsable -->
                @if(count($selectedPersons) > 0)
                <div class="transform transition-all duration-300 hover:scale-105">
                    <flux:button
                        wire:click="confirmRemoveResponsible"
                        variant="danger"

                        class="shadow-xl rounded-full relative group"
                    >
                        <div class="flex items-center gap-2">
                            <flux:icon.user-minus class="h-6 w-6" />
                            <span class="font-medium hidden md:inline-block">Quitar Responsable</span>
                            <span class="absolute -top-2 -right-2 md:relative md:top-auto md:right-auto md:ml-1 inline-flex items-center justify-center h-6 w-6 rounded-full bg-white text-red-600 text-sm font-bold shadow-md border border-red-300">
                                {{ count($selectedPersons) }}
                            </span>
                        </div>
                    </flux:button>
                    <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full mr-3 bg-gray-900 text-white text-sm py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none md:hidden">
                        Quitar Responsable
                    </div>
                </div>
                @endif

                <!-- Botón flotante para asignar responsable -->
                @if(count($selectedPersons) > 0 && $selectedResponsible)
                <div class="animate-bounce transform transition-all duration-300 hover:scale-105">
                    <flux:button
                        wire:click="confirmAssignResponsible"
                        variant="primary"

                        class="shadow-xl rounded-full relative group"
                    >
                        <div class="flex items-center gap-2">
                            <flux:icon.user-plus class="h-6 w-6" />
                            <span class="font-medium hidden md:inline-block">Asignar a Responsable</span>
                            <span class="absolute -top-2 -right-2 md:relative md:top-auto md:right-auto md:ml-1 inline-flex items-center justify-center h-6 w-6 rounded-full bg-white text-primary-600 text-sm font-bold shadow-md border border-primary-300">
                                {{ count($selectedPersons) }}
                            </span>
                        </div>
                    </flux:button>
                    <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full mr-3 bg-gray-900 text-white text-sm py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none md:hidden">
                        Asignar a Responsable
                    </div>
                </div>
                @endif
            </div>

            <div class="overflow-x-auto">
                <flux:table class="min-w-full">
                    <flux:table.columns>
                        <flux:table.column class="w-10">
                            <input type="checkbox" wire:model.live="selectAll" id="select-all-checkbox" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                        </flux:table.column>
                        <flux:table.column class="min-w-[200px]">Nombre</flux:table.column>
                        <flux:table.column class="min-w-[120px]">Identificación</flux:table.column>
                        <flux:table.column class="min-w-[120px]">Teléfono</flux:table.column>
                        <flux:table.column class="min-w-[100px]">Rol</flux:table.column>
                        <flux:table.column class="min-w-[180px]">Responsable Actual</flux:table.column>
                        <flux:table.column class="min-w-[100px]">Estado</flux:table.column>
                    </flux:table.columns>

                <flux:table.rows>
                    @forelse($persons as $person)
                        <flux:table.row
                            wire:key="person-row-{{ $person->id }}"
                            class="{{ in_array((string) $person->id, $selectedPersons) ? 'bg-primary-50 dark:bg-primary-900/20' : '' }} hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                        >
                            <flux:table.cell>
                                @if($person->id != $selectedResponsible)
                                    <input
                                        type="checkbox"
                                        wire:model.live="selectedPersons"
                                        value="{{ $person->id }}"
                                        id="checkbox-{{ $person->id }}"
                                        class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                                    />
                                @else
                                    <div class="h-5 w-5 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 text-xs">
                                        <flux:icon.star class="h-3 w-3" />
                                    </div>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell class="flex items-center gap-3">
                                <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 font-medium shadow-sm {{ $person->is_1x10 ? 'ring-2 ring-primary-500' : '' }}">
                                    {{ substr($person->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium flex items-center gap-1">
                                        {{ $person->name }}
                                        @if($person->id == $selectedResponsible)
                                            <flux:badge size="xs" color="primary" inset="left right">Seleccionado</flux:badge>
                                        @endif
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email ?: 'Sin email' }}</div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div>{{ $person->cedula }}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    <flux:icon.calendar class="h-3 w-3 inline-block mr-1" /> {{ $person->registration_date ? $person->registration_date->format('d/m/Y') : 'Sin fecha' }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($person->phone)
                                    <div class="flex items-center gap-1">
                                        <flux:icon.phone class="h-4 w-4 text-gray-400" />
                                        <span>{{ $person->phone }}</span>
                                    </div>
                                @else
                                    <span class="text-gray-400 dark:text-gray-500">Sin teléfono</span>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" color="{{ $person->role === 'Militante' ? 'green' : 'blue' }}" inset="top bottom">{{ $person->role }}</flux:badge>
                                @if($person->is_1x10)
                                    <div class="mt-1">
                                        <flux:badge size="sm" color="purple" inset="top bottom">
                                            <div class="flex items-center gap-1">
                                                <flux:icon.user-group class="h-3 w-3" />
                                                <span>Líder 1x10</span>
                                            </div>
                                        </flux:badge>
                                    </div>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($person->responsible)
                                    <div class="flex items-center gap-2">
                                        <div class="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-700 dark:text-purple-300 shadow-sm">
                                            {{ substr($person->responsible->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium">{{ $person->responsible->name }}</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $person->responsible->cedula }}</div>
                                        </div>
                                    </div>
                                @else
                                    <flux:badge size="sm" color="yellow" inset="top bottom">
                                        <div class="flex items-center gap-1">
                                            <flux:icon.exclamation-triangle class="h-3 w-3" />
                                            <span>Sin responsable</span>
                                        </div>
                                    </flux:badge>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" color="{{ $person->activity_status === 'Activo' ? 'green' : 'gray' }}" inset="left right">
                                    <div class="flex items-center gap-1">
                                        <flux:icon.{{ $person->activity_status === 'Activo' ? 'check-circle' : 'x-circle' }} class="h-3 w-3" />
                                        <span>{{ $person->activity_status ?? 'Sin estado' }}</span>
                                    </div>
                                </flux:badge>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="7" class="text-center py-8">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <flux:icon.user-group class="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
                                    <div class="text-lg font-medium text-gray-500 dark:text-gray-400">No se encontraron personas</div>
                                    <div class="text-sm text-gray-400 dark:text-gray-500 mt-1">Intenta con otros filtros de búsqueda</div>
                                    <div class="mt-4">
                                        <flux:button
                                            wire:click="resetFilters"
                                            variant="primary"
                                            size="sm"
                                            icon="arrow-path"
                                        >
                                            Limpiar filtros y mostrar todos
                                        </flux:button>
                                    </div>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
                </flux:table>
            </div>

            <div class="mt-6 flex justify-center sm:justify-end">
                {{ $persons->links() }}
            </div>
        </flux:card>
    </div>

    <!-- Se elimina el modal de creación de grupos para evitar redundancia -->

    <!-- Modal para crear persona -->
    <flux:modal name="create-person-modal" wire:model="showCreatePersonModal" class="md:max-w-xl">
        <div class="space-y-6">
            <div>
                <flux:heading >Crear Persona</flux:heading>
                <flux:text class="mt-2">Registra una nueva persona en el sistema.</flux:text>
            </div>

            <div class="space-y-4">
                <div>
                    <flux:input label="Nombre Completo" id="personName" wire:model="personName" placeholder="Nombre completo" />
                    @error('personName') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <flux:input label="Cédula" id="personCedula" wire:model="personCedula" placeholder="Número de cédula" type="number" />
                    @error('personCedula') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <flux:input label="Teléfono" id="personPhone" wire:model="personPhone" placeholder="Número de teléfono" />
                        @error('personPhone') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input label="Correo Electrónico" id="personEmail" wire:model="personEmail" placeholder="Correo electrónico" />
                        @error('personEmail') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <div>
                    <flux:select label="Rol" id="personRole" wire:model="personRole">
                        <flux:select.option value="Votante">Votante</flux:select.option>
                        <flux:select.option value="Militante">Militante</flux:select.option>
                        <flux:select.option value="Coordinador">Coordinador</flux:select.option>
                    </flux:select>
                    @error('personRole') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                @if($selectedResponsible)
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                        <div class="flex items-center gap-2 mb-2">
                            <x-heroicon-o-information-circle class="w-5 h-5 text-green-500" />
                            <span class="font-medium">Asignación Automática</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-300">
                            Esta persona será asignada automáticamente a <strong>{{ $this->selectedResponsiblePerson->name ?? '' }}</strong> como su responsable 1x10.
                        </p>
                    </div>
                @endif
            </div>

            <div class="flex justify-end gap-3">
                <flux:button wire:click="$set('showCreatePersonModal', false)" variant="ghost">
                    Cancelar
                </flux:button>
                <flux:button wire:click="createPerson" variant="primary">
                    Crear Persona
                </flux:button>
            </div>
        </div>
    </flux:modal>

<!-- Modal para exportar datos -->
<flux:modal name="export-modal" wire:model="showExportModal" class="md:max-w-lg">
    <div class="space-y-6">
        <flux:heading >Exportar Datos</flux:heading>

        <div class="space-y-4">
            <div>
                <flux:label for="export_type">Tipo de Exportación</flux:label>
                <flux:select wire:model="exportType" class="w-full">
                    <flux:select.option value="all">Todas las personas</flux:select.option>
                    <flux:select.option value="with_responsible">Personas con responsable</flux:select.option>
                    <flux:select.option value="without_responsible">Personas sin responsable</flux:select.option>
                    <flux:select.option value="leaders">Líderes 1x10</flux:select.option>
                    <flux:select.option value="selected">Personas seleccionadas</flux:select.option>
                </flux:select>
            </div>

            <div>
                <flux:label for="export_format">Formato</flux:label>
                <flux:select wire:model="exportFormat" class="w-full">
                    <flux:select.option value="excel">Excel (.xlsx)</flux:select.option>
                    <flux:select.option value="csv">CSV (.csv)</flux:select.option>
                    <flux:select.option value="pdf">PDF (.pdf)</flux:select.option>
                </flux:select>
            </div>

            <div>
                <flux:label for="export_columns">Columnas a Exportar</flux:label>
                <div class="grid grid-cols-2 gap-2 mt-2">
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="name" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Nombre</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="cedula" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Cédula</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="phone" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Teléfono</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="email" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Email</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="role" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Rol</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="responsible" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Responsable</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="is_1x10" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Es 1x10</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" wire:model="exportColumns" value="status" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <span class="text-sm">Estado</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-2">
            <flux:button wire:click="$set('showExportModal', false)" variant="ghost">Cancelar</flux:button>
            <flux:button wire:click="exportData" variant="filled" icon="arrow-down-tray">Exportar</flux:button>
        </div>
    </div>
</flux:modal>
</div>
