<section class="w-full">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-magnifying-glass class="size-6 text-primary-500"/>
                {{ __('Búsqueda Avanzada') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Busca personas con filtros avanzados') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.persons.index') }}">{{ __('Personas') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Búsqueda') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card class="mb-6">
        <flux:heading size="lg" class="mb-4">Filtros de Búsqueda</flux:heading>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <flux:input
                wire:model.live.debounce.300ms="search"
                placeholder="Nombre, cédula, email..."
                icon="magnifying-glass"
            />

            <flux:select wire:model.live="role">
                <flux:select.option value="">Todos los roles</flux:select.option>
                <flux:select.option value="militant">Militantes</flux:select.option>
                <flux:select.option value="voter">Votantes</flux:select.option>
            </flux:select>

            <flux:select wire:model.live="status">
                <flux:select.option value="">Todos los estados</flux:select.option>
                <flux:select.option value="active">Activos</flux:select.option>
                <flux:select.option value="inactive">Inactivos</flux:select.option>
            </flux:select>

            <flux:select wire:model.live="is1x10">
                <flux:select.option value="">1x10 (Todos)</flux:select.option>
                <flux:select.option value="yes">Es 1x10</flux:select.option>
                <flux:select.option value="no">No es 1x10</flux:select.option>
            </flux:select>

            <flux:select wire:model.live="estate_id">
                <flux:select.option value="">Todos los estados</flux:select.option>
                @foreach($this->states as $estate)
                    <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <flux:select wire:model.live="municipality_id">
                <flux:select.option value="">Todos los municipios</flux:select.option>
                @foreach($this->municipalities as $municipality)
                    <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                @endforeach
            </flux:select>

            <flux:select wire:model.live="parish_id">
                <flux:select.option value="">Todas las parroquias</flux:select.option>
                @foreach($this->parishes as $parish)
                    <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
                @endforeach
            </flux:select>
        </div>

        <div class="flex justify-between items-center mt-6">
            <flux:select wire:model.live="perPage" class="w-24">
                <flux:select.option value="10">10</flux:select.option>
                <flux:select.option value="25">25</flux:select.option>
                <flux:select.option value="50">50</flux:select.option>
                <flux:select.option value="100">100</flux:select.option>
            </flux:select>

            <flux:button wire:click="$refresh" icon="ellipsis-horizontal" variant="primary">
                Actualizar
            </flux:button>
        </div>
    </flux:card>

    <flux:card>
        <flux:heading size="lg" class="mb-4">Resultados ({{ $this->persons->total() }})</flux:heading>

        @if($this->persons->count() > 0)
            <flux:table>
                <flux:table.columns>
                    <flux:table.column>Nombre</flux:table.column>
                    <flux:table.column>Cédula</flux:table.column>
                    <flux:table.column>Teléfono</flux:table.column>
                    <flux:table.column>Rol</flux:table.column>
                    <flux:table.column>Estado</flux:table.column>
                    <flux:table.column>1x10</flux:table.column>
                    <flux:table.column>Municipio</flux:table.column>
                    <flux:table.column>Estado</flux:table.column>
                    <flux:table.column>Responsable</flux:table.column>
                    <flux:table.column>Acciones</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @foreach($this->persons as $person)
                        <flux:table.row wire:key="person-row-{{ $person->id }}">
                            <flux:table.cell class="flex items-center gap-3">
                                <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300">
                                    {{ substr($person->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium">{{ $person->name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email ?: 'Sin email' }}</div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                            <flux:table.cell>{{ $person->phone }}</flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" color="{{ $person->role === 'Militant' ? 'green' : 'blue' }}" inset="top bottom">{{ $person->role }}</flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" color="{{ $person->activity_status === 'Active' ? 'green' : 'red' }}" inset="top bottom">{{ $person->activity_status }}</flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($person->is_1x10)
                                    <flux:badge size="sm" color="purple" inset="top bottom">Sí</flux:badge>
                                @else
                                    <flux:badge size="sm" color="gray" inset="top bottom">No</flux:badge>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($person->municipality_id && $person->municipality)
                                    {{ $person->municipality->name }}
                                @else
                                    <span class="text-gray-400 dark:text-gray-500">No registrado</span>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($person->estate_id && $person->estate)
                                    {{ $person->estate->name }}
                                @else
                                    <span class="text-gray-400 dark:text-gray-500">No registrado</span>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($person->is_1x10)
                                    <flux:badge variant="success">Líder 1x10</flux:badge>
                                @else
                                    @if($person->responsible_id && $person->responsible)
                                        <div class="flex items-center gap-1">
                                            <span class="text-xs text-gray-500 dark:text-gray-400">Resp:</span>
                                            <span class="text-sm font-medium">{{ $person->responsible->name }}</span>
                                        </div>
                                    @else
                                        <span class="text-gray-400 dark:text-gray-500">Sin asignar</span>
                                    @endif
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:dropdown position="bottom" align="end">
                                    <flux:button icon="ellipsis-horizontal" variant="ghost" size="sm" inset="top bottom" />
                                    <flux:menu>
                                        <flux:menu.item href="{{ route('admin.persons.show', $person) }}" icon="eye">Ver</flux:menu.item>
                                        <flux:menu.item href="{{ route('admin.persons.edit', $person) }}" icon="pencil-square">Editar</flux:menu.item>
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
            <div class="mt-4">
                {{ $this->persons->links() }}
            </div>

        @else
            <div class="flex flex-col items-center justify-center py-12">
                <x-heroicon-o-magnifying-glass class="w-16 h-16 text-gray-400 mb-4" />
                <p class="text-lg font-medium text-gray-500">No se encontraron resultados</p>
                <p class="text-sm text-gray-400">Intenta ajustar los filtros de búsqueda</p>
            </div>
        @endif
    </flux:card>
</section>
