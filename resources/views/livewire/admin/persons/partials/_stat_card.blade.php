@props([
    'title',
    'value',
    'description',
    'icon',
    'color'
])

@php
    $bgColorClass = "bg-{$color}-100 dark:bg-{$color}-900/30";
    $textColorClass = "text-{$color}-500";
@endphp

<flux:card class="bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300">
    <div class="flex items-center justify-between p-4">
        <div class="flex-1">
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $title }}</div>
            <div class="mt-2 flex items-baseline">
                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $value }}</div>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $description }}</div>
        </div>
        <div class="p-3 rounded-full {{ $bgColorClass }}">
            @switch($icon)
                @case('users')
                    <flux:icon.users class="size-5 {{ $textColorClass }}" />
                    @break
                @case('user-circle')
                    <flux:icon.user-circle class="size-5 {{ $textColorClass }}" />
                    @break
                @case('user-minus')
                    <flux:icon.user-minus class="size-5 {{ $textColorClass }}" />
                    @break
                @case('user-group')
                    <flux:icon.user-group class="size-5 {{ $textColorClass }}" />
                    @break
                @case('squares-2x2')
                    <flux:icon.squares-2x2 class="size-5 {{ $textColorClass }}" />
                    @break
                @case('check-circle')
                    <flux:icon.check-circle class="size-5 {{ $textColorClass }}" />
                    @break
                @default
                    <flux:icon.information-circle class="size-5 {{ $textColorClass }}" />
            @endswitch
        </div>
    </div>
</flux:card>
