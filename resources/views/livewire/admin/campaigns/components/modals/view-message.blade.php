<flux:modal name="view-message" class="max:w-96">
    <div class="space-y-6">
        <div>
            <flux:heading size="lg">{{__('campaigns.message_details')}}</flux:heading>
        </div>

        @if($message)
            <dl class="grid grid-cols-1 gap-4">
                <div class="space-y-1">
                    <dt class="text-sm font-medium text-gray-500">{{ __('campaigns.phone_number') }}</dt>
                    <dd class="text-sm created_at" style="white-space: pre-wrap;">{{ $message->phone_number }}</dd>
                </div>

                <div class="space-y-1">
                    <dt class="text-sm font-medium text-gray-500">{{ __('campaigns.message') }}</dt>
                    <dd class="">{{ $message->message }}</dd>
                </div>

                <div class="space-y-1">
                    <dt class="text-sm font-medium text-gray-500">{{ __('global.status') }}</dt>
                    <dd>
                        <flux:badge
                            variant="solid"
                            color="{{$this->getStatusColor($message->status)}}"
                        >
                            {{ $this->getStatusOptions()[$message->status] ?? $message->status }}
                        </flux:badge>
                    </dd>
                </div>

                <div class="space-y-1">
                    <dt class="text-sm font-medium text-gray-500">{{ __('global.created_at') }}</dt>
                    <dd class="text-sm created_at">{{ $message->created_at->format('d/m/Y H:i:s') }}</dd>
                </div>

                @if($message->sent_at)
                    <div class="space-y-1">
                        <dt class="text-sm font-medium text-gray-500">{{ __('campaigns.sent_at') }}</dt>
                        <dd class="text-sm created_at">{{ $message->sent_at->format('d/m/Y H:i:s') }}</dd>
                    </div>
                @endif


{{--                @if($message->error_message)--}}
{{--                    <div class="space-y-1">--}}
{{--                        <dt class="text-sm font-medium text-gray-500">{{ __('campaigns.error_message') }}</dt>--}}
{{--                        <dd class="text-sm text-red-600">{{ $message->error_message }}</dd>--}}
{{--                    </div>--}}
{{--                @endif--}}
            </dl>
        @endif
    </div>
</flux:modal>
