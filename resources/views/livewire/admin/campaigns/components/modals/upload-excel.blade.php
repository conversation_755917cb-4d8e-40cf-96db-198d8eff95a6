<flux:modal name="upload-excel" class="max:w-96">
    <div class="space-y-6">
        <div>
            <flux:heading size="lg">{{ __('campaigns.import_messages') }}</flux:heading>
            <flux:text class="mt-2">
                {{ __('campaigns.upload_excel_description') }}
                {{ __('campaigns.excel_columns_description') }}
            </flux:text>

            <dl class="mt-4 space-y-2 text-sm">
                <div>
                    <dt class="font-medium">{{ __('campaigns.excel_column_phone') }}</dt>
                    <dd class="text-gray-600">{{ __('campaigns.excel_column_phone_description') }}</dd>
                </div>
                <div>
                    <dt class="font-medium">{{ __('campaigns.excel_column_message') }}</dt>
                    <dd class="text-gray-600">{{ __('campaigns.excel_column_message_description') }}</dd>
                </div>
            </dl>
        </div>

        <x-form wire:submit="uploadExcel" class="space-y-4">
            <flux:input
                type="file"
                wire:model="excelFile"
                label="{{ __('campaigns.excel_file') }}"
                hint="{{ __('campaigns.allowed_formats') }}"
                accept=".xlsx,.xls,.csv"
            />

            @error('excelFile')
                <flux:error>{{ $message }}</flux:error>
            @enderror

            <div wire:loading wire:target="excelFile" class="mt-2">
                <flux:text class="mt-1 text-sm">{{ __('campaigns.uploading_file') }}</flux:text>
            </div>

            <div class="flex justify-end gap-3 pt-4">
                <flux:button
                    type="submit"
                    variant="primary"
                    icon="arrow-up-tray"
                    wire:loading.attr="disabled"
                    wire:target="uploadExcel"
                >
                    <span wire:loading.remove wire:target="uploadExcel">
                        {{ __('campaigns.import_messages') }}
                    </span>
                    <span wire:loading wire:target="uploadExcel">
                        {{ __('campaigns.processing_file') }}
                    </span>
                </flux:button>
            </div>
        </x-form>

        <div class="mt-4 border-t pt-4">
            <flux:text class="text-sm font-medium">{{ __('global.need_template') }}</flux:text>
            <flux:button
                variant="outline"
                size="sm"
                icon="document-arrow-down"
                wire:click="downloadTemplate"
                class="mt-1"
            >
                {{ __('campaigns.download_template') }}
            </flux:button>
        </div>
    </div>
</flux:modal>
