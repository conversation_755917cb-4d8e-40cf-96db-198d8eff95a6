
<flux:modal name="create-campaign" class="lg:max-w-6xl">
    <div class="space-y-6">
        <div>
            <flux:heading size="lg">{{ __('campaigns.create_new_campaign') }}</flux:heading>
            <flux:text class="mt-2">{{ __('campaigns.complete_campaign_data') }}</flux:text>
        </div>

        <x-form wire:submit="save">
            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-1">
                    <flux:input wire:model="form.name" label="{{ __('campaigns.name') }}" placeholder="{{ __('campaigns.name') }}" />
                </div>

                <div class="space-y-1">
                    <flux:input wire:model="form.description" label="{{ __('campaigns.description') }}" placeholder="{{ __('campaigns.description') }}" />
                </div>

                <div class="space-y-1">
                    <flux:input wire:model="form.start_date" type="date" label="{{ __('campaigns.start_date') }}" />
                </div>

                <div class="space-y-1">
                    <flux:input wire:model="form.end_date" type="date" label="{{ __('campaigns.end_date') }}" />
                </div>

                <div class="space-y-1">
                    <flux:select wire:model="form.status" label="{{ __('campaigns.status') }}" placeholder="{{ __('global.select_option') }}">
                        <option value="active">{{ __('campaigns.campaign_status.active') }}</option>
                        <option value="inactive">{{ __('campaigns.campaign_status.inactive') }}</option>
                    </flux:select>
                </div>
            </div>

            <div class="flex justify-end mt-6">
                <flux:button type="submit" variant="primary">{{ __('campaigns.create_campaign') }}</flux:button>
            </div>
        </x-form>
    </div>
</flux:modal>
