<section class="w-full">
    <!-- Statistics Grid -->
    <div class="flex items-center gap-4 mb-6">
        <flux:card class="flex-1">
            <div class="flex items-center justify-between">
                <div>
                    <flux:text class="text-gray-600 dark:text-gray-400">{{ __('campaigns.total_campaigns') }}</flux:text>
                    <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['total']) }}</flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.megaphone variant="micro" class="text-blue-600 dark:text-blue-500" />
                        <span class="text-sm font-medium text-blue-600 dark:text-blue-500">{{ __('global.total_registered') }}</span>
                    </div>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1">
            <div class="flex items-center justify-between">
                <div>
                    <flux:text class="text-gray-600 dark:text-gray-400">{{ __('campaigns.active_campaigns') }}</flux:text>
                    <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['active']) }}</flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.arrow-trending-up variant="micro" class="text-green-600 dark:text-green-500" />
                        <span class="text-sm font-medium text-green-600 dark:text-green-500">
                            {{ number_format(($this->statistics['active'] / max($this->statistics['total'], 1)) * 100, 1) }}% {{ __('global.active') }}
                        </span>
                    </div>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1">
            <div class="flex items-center justify-between">
                <div>
                    <flux:text class="text-gray-600 dark:text-gray-400">{{ __('global.scheduled_campaigns') }}</flux:text>
                    <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['scheduled']) }}</flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.clock variant="micro" class="text-orange-600 dark:text-orange-500" />
                        <span class="text-sm font-medium text-orange-600 dark:text-orange-500">
                            {{ number_format(($this->statistics['scheduled'] / max($this->statistics['total'], 1)) * 100, 1) }}% {{ __('global.scheduled') }}
                        </span>
                    </div>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1">
            <div class="flex items-center justify-between">
                <div>
                    <flux:text class="text-gray-600 dark:text-gray-400">{{ __('campaigns.completed_campaigns') }}</flux:text>
                    <flux:heading size="xl" class="mb-1">{{ number_format($this->statistics['completed']) }}</flux:heading>
                    <div class="flex items-center gap-2">
                        <flux:icon.check-circle variant="micro" class="text-indigo-600 dark:text-indigo-500" />
                        <span class="text-sm font-medium text-indigo-600 dark:text-indigo-500">
                            {{ number_format(($this->statistics['completed'] / max($this->statistics['total'], 1)) * 100, 1) }}% {{ __('global.completed') }}
                        </span>
                    </div>
                </div>
            </div>
        </flux:card>
    </div>
    <flux:card class="flex-1">
    <div class="flex items-center justify-between w-full mb-6 gap-2">
        <flux:input
            wire:model.live.debounce.300ms="search"
            placeholder="{{ __('campaigns.search_placeholder') }}"
            icon="magnifying-glass"
            class="!w-64"
        />

        <flux:select
            wire:model.live="searchField"
            class="!w-auto"
        >
            @foreach($this->getSearchableFields() as $value => $label)
                <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
            @endforeach
        </flux:select>

        <flux:spacer/>

        <flux:select wire:model.live="perPage" class="!w-auto">
            @foreach($perPageOptions as $value)
                <flux:select.option value="{{ $value }}">{{ $value }}</flux:select.option>
            @endforeach
        </flux:select>

        <flux:select wire:model.live="status" class="!w-auto">
            @foreach($this->getStatusOptions() as $value => $label)
                <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
            @endforeach
        </flux:select>

        <flux:button
            wire:click="exportCampaigns"
            icon="arrow-down-tray"
        >
            {{ __('global.export') }}
        </flux:button>

        <flux:modal.trigger name="create-campaign">
            <flux:button icon="plus">
                {{ __('campaigns.create_campaign') }}
            </flux:button>
        </flux:modal.trigger>
    </div>

    <flux:table :paginate="$this->campaigns">
        <flux:table.columns>
            <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">ID</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">{{ __('campaigns.name') }}</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'description'" :direction="$sortDirection" wire:click="sort('description')">{{ __('campaigns.description') }}</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'start_date'" :direction="$sortDirection" wire:click="sort('start_date')">{{ __('campaigns.start_date') }}</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'end_date'" :direction="$sortDirection" wire:click="sort('end_date')">{{ __('campaigns.end_date') }}</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">{{ __('campaigns.status') }}</flux:table.column>
            <flux:table.column>{{ __('campaigns.message') }}</flux:table.column>
            <flux:table.column>{{ __('global.actions') }}</flux:table.column>
        </flux:table.columns>

        <flux:table.rows>
            @forelse($this->campaigns as $campaign)
                <flux:table.row wire:key="campaign-{{ $campaign->id }}">
                    <flux:table.cell>{{ substr($campaign->id, 0, 8) }}</flux:table.cell>
                    <flux:table.cell>{{ $campaign->name }}</flux:table.cell>
                    <flux:table.cell>{{ Str::limit($campaign->description, 50) }}</flux:table.cell>
                    <flux:table.cell>{{ $campaign->start_date->format('d/m/Y') }}</flux:table.cell>
                    <flux:table.cell>{{ $campaign->end_date->format('d/m/Y') }}</flux:table.cell>
                    <flux:table.cell>
                        <flux:badge
                            size="sm"
                            :color="$this->getStatusColor($campaign->status)"
                            inset="top bottom"
                        >
                            {{ $this->getStatusOptions()[$campaign->status] ?? $campaign->status }}
                        </flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>
                        <flux:badge
                            size="sm"
                            color="secondary"
                            inset="top bottom"
                        >
                            {{ $campaign->details_count ?? 0 }} {{ __('campaigns.message') }}
                        </flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>
                        <flux:dropdown position="bottom" align="end">
                            <flux:button variant="primary" size="sm">
                                {{ __('global.actions') }}
                                <x-heroicon-m-chevron-down class="ml-2 -mr-1 h-4 w-4" />
                            </flux:button>

                            <flux:menu>
                                <flux:menu.item
                                    wire:navigate
                                    href="{{ route('admin.campaigns.show', $campaign) }}"
                                    icon="eye"
                                >
                                    {{ __('campaigns.view') }}
                                </flux:menu.item>

                                    <flux:menu.item
                                        wire:click="editCampaign('{{ $campaign->id }}')"
                                        icon="pencil-square"
                                    >
                                        {{ __('campaigns.edit') }}
                                    </flux:menu.item>


                                <flux:menu.separator />

                                <flux:menu.item
                                    wire:click="confirmDelete({{ $campaign->id }})"
                                    icon="trash"
                                    class="text-red-600 dark:text-red-400"
                                >
                                    {{ __('campaigns.delete') }}
                                </flux:menu.item>
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
            @empty
                <flux:table.row>
                    <flux:table.cell colspan="8" class="text-center py-8">
                        <div class="flex flex-col items-center justify-center">
                            <x-heroicon-o-megaphone class="w-12 h-12 text-gray-400" />
                            <span class="mt-2 text-gray-500">{{ __('campaigns.no_campaigns_found') }}</span>
                        </div>
                    </flux:table.cell>
                </flux:table.row>
            @endforelse
        </flux:table.rows>
    </flux:table>
</flux:card>

    <livewire:admin.campaigns.pages.create-campaign />
    <livewire:admin.campaigns.pages.edit-campaign  />
</section>
