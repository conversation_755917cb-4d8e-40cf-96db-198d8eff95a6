<div class="space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
               <flux:icon.bolt />
                {{ $campaign->name }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('campaigns.campaign_details') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('global.home') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.campaigns') }}">{{ __('campaigns.campaigns') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ $campaign->name }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button.group>
                <flux:button
                    variant="outline"
                    icon="pencil-square"
                    :href="route('admin.campaigns.edit', $campaign)"
                >
                    {{ __('campaigns.edit') }}
                </flux:button>

                <flux:button
                    variant="outline"
                    icon="trash"
                    color="red"
                    wire:click="delete"
                    wire:confirm="{{ __('campaigns.confirm_delete_campaign') }}"
                >
                    {{ __('campaigns.delete') }}
                </flux:button>
            </flux:button.group>
        </x-slot:buttons>
    </x-page-heading>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Información Principal -->
        <flux:card>
            <flux:header>
                <flux:heading size="sm">{{ __('global.general_information') }}</flux:heading>
            </flux:header>

            <div class="space-y-6">
                <dl class="divide-y divide-gray-200 dark:divide-gray-700">
                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('campaigns.status') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                            <flux:badge
                                variant="solid"
                                color="{{$this->getStatusColor($campaign->status)}}"
                            >
                                {{ $statusOptions[$campaign->status] ?? $campaign->status }}
                            </flux:badge>
                        </dd>
                    </div>

                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('campaigns.description') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                            {{ $campaign->description }}
                        </dd>
                    </div>

                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('campaigns.start_date') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                            {{ $campaign->start_date->format('d/m/Y') }}
                        </dd>
                    </div>

                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('campaigns.end_date') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                            {{ $campaign->end_date->format('d/m/Y') }}
                        </dd>
                    </div>

                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('campaigns.created_at') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                            {{ $campaign->created_at->format('d/m/Y H:i') }}
                        </dd>
                    </div>

                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('campaigns.updated_at') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                            {{ $campaign->updated_at->format('d/m/Y H:i') }}
                        </dd>
                    </div>
                </dl>
            </div>
        </flux:card>

        <!-- Estadísticas -->
        <flux:card>
            <flux:header>
                <flux:heading size="sm">{{ __('global.statistics') }}</flux:heading>
            </flux:header>

            <div class="space-y-6">

            </div>
        </flux:card>

        <!-- Acciones Rápidas -->
        <flux:card class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('campaigns.quick_actions') }}</flux:heading>
            </div>

            <div class="space-y-6">
                <flux:button
                    wire:click="toggleStatus"
                    :icon="$campaign->status === 'active' ? 'pause' : 'play'"
                    class="w-full"
                >
                    {{ $campaign->status === 'active' ? __('campaigns.pause_campaign') : __('campaigns.activate_campaign') }}
                </flux:button>

                <flux:button
                    wire:click="sendWhatsAppMessage"
                    icon="envelope"
                    variant="outline"
                    class="w-full"
                >
                    {{ __('campaigns.send_messages') }}
                </flux:button>

                <flux:button
                    icon="document-chart-bar"
                    variant="outline"
                    class="w-full"
                    :href="route('admin.campaigns', $campaign)"
                >
                    {{ __('campaigns.view_reports') }}
                </flux:button>
            </div>
        </flux:card>


    </div>

    <!-- Lista de Mensajes -->
    <flux:card>
        <flux:header class="flex items-center justify-between">
            <flux:heading size="sm">{{ __('campaigns.message_details') }}</flux:heading>
            <div class="flex items-center gap-2">
                <flux:button
                    variant="outline"
                    icon="arrow-down-tray"
                    wire:click="exportMessages"
                >
                    {{ __('global.export') }}
                </flux:button>
                <flux:modal.trigger name="upload-excel">
                    <flux:button
                        variant="primary"
                        icon="arrow-up-tray"
                    >
                        {{ __('campaigns.upload_excel') }}
                    </flux:button>
                </flux:modal.trigger>
            </div>
        </flux:header>

        <div class="space-y-4">
            <!-- Filtros -->
            <flux:card class="bg-zinc-50/50 dark:bg-zinc-800/50">
                <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    <flux:input.group class="w-full sm:w-auto">
                        <flux:label>{{ __('global.search') }}</flux:label>
                        <flux:input
                            wire:model.live.debounce.300ms="search"
                            placeholder="{{ __('campaigns.search_placeholder') }}"
                            icon="magnifying-glass"
                        />
                    </flux:input.group>

                    <flux:input.group class="w-full sm:w-auto">
                        <flux:label>{{ __('global.status') }}</flux:label>
                        <flux:select wire:model.live="statusFilter">
                            <flux:select.option value="">{{ __('global.all_statuses') }}</flux:select.option>
                            @foreach($this->getStatusOptions() as $value => $label)
                                <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:input.group>

                    <flux:spacer />

                    <flux:input.group class="w-full sm:w-auto">
                        <flux:label>{{ __('global.per_page') }}</flux:label>
                        <flux:select wire:model.live="perPage">
                            @foreach($perPageOptions as $value)
                                <flux:select.option value="{{ $value }}">{{ $value }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:input.group>
                </div>
            </flux:card>

            <!-- Tabla -->
            <flux:table :paginate="$this->messages">
                <flux:table.columns>
                    <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">
                        {{ __('global.id') }}
                    </flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'phone_number'" :direction="$sortDirection" wire:click="sort('phone_number')">
                        {{ __('campaigns.phone_number') }}
                    </flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'message'" :direction="$sortDirection" wire:click="sort('message')">
                        {{ __('campaigns.message') }}
                    </flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">
                        {{ __('global.status') }}
                    </flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'created_at'" :direction="$sortDirection" wire:click="sort('created_at')">
                        {{ __('global.date') }}
                    </flux:table.column>
                    <flux:table.column>
                        <span class="sr-only">{{ __('global.actions') }}</span>
                    </flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->messages as $message)
                        <flux:table.row wire:key="message-{{ $message->id }}">
                            <flux:table.cell>{{ $message->id }}</flux:table.cell>
                            <flux:table.cell>{{ $message->phone_number }}</flux:table.cell>
                            <flux:table.cell>
                                <div class="max-w-xs truncate" title="{{ $message->message }}">
                                    {{ Str::limit($message->message, 50) }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:badge
                                    variant="solid"
                                    color="{{$this->getStatusColor($message->status)}}"
                                >
                                    {{ $this->getStatusOptions()[$message->status] ?? $message->status }}
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>{{ $message->created_at->format('d/m/Y H:i') }}</flux:table.cell>
                            <flux:table.cell>
                                <flux:dropdown>
                                    <flux:button variant="ghost" size="xs" icon:trailing="chevron-down">
                                        {{ __('global.actions') }}
                                    </flux:button>
                                    <flux:menu>
                                        <flux:menu.item
                                            icon="eye"
                                            wire:click="viewMessage({{ $message->id }})"
                                        >
                                            {{ __('global.view') }}
                                        </flux:menu.item>

                                        <flux:menu.item
                                            icon="pencil-square"
                                            wire:click="editMessage({{ $message->id }})"
                                        >
                                            {{ __('global.edit') }}
                                        </flux:menu.item>

                                        <flux:menu.separator />

                                        <flux:menu.item
                                            variant="danger"
                                            icon="trash"
                                            wire:click="deleteMessage({{ $message->id }})"
                                            wire:confirm="{{ __('global.confirm_delete') }}"
                                        >
                                            {{ __('global.delete') }}
                                        </flux:menu.item>
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="6" class="py-12">
                                <div class="flex flex-col items-center justify-center gap-2">
                                    <x-heroicon-o-megaphone class="size-12 text-gray-400" />
                                    <span class="text-gray-500">{{ __('campaigns.no_messages') }}</span>
                                    <span class="text-sm text-gray-400">{{ __('campaigns.upload_excel_hint') }}</span>
                                    <flux:modal.trigger name="upload-excel">
                                        <flux:button icon="arrow-up-tray" class="mt-4">
                                            {{ __('campaigns.upload_excel') }}
                                        </flux:button>
                                    </flux:modal.trigger>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>
    </flux:card>

    <!-- Modals -->
    <livewire:admin.campaigns.components.modals.upload-excel :campaign="$campaign"/>
    <livewire:admin.campaigns.components.modals.view-message />
</div>
