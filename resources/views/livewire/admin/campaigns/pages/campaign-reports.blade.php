<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-document-chart-bar class="size-6 text-primary-500"/>
                {{ __('campaigns.campaign_reports') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('campaigns.campaign_reports_description') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('global.home') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.campaigns') }}">{{ __('campaigns.campaigns') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('campaigns.reports') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Filtros -->
    <flux:card>
        <div class="p-4 space-y-4">
            <flux:heading size="sm">{{ __('global.filters') }}</flux:heading>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <flux:select
                    wire:model.live="reportType"
                    label="{{ __('campaigns.report_type') }}"
                >
                    <flux:select.option value="overview">{{ __('campaigns.overview') }}</flux:select.option>
                    <flux:select.option value="messages">{{ __('campaigns.messages_stats') }}</flux:select.option>
                    <flux:select.option value="performance">{{ __('campaigns.performance') }}</flux:select.option>
                </flux:select>

                <flux:select
                    wire:model.live="dateRange"
                    label="{{ __('campaigns.date_range') }}"
                >
                    @foreach($this->dateRanges as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>

                @if($dateRange === 'custom')
                    <flux:input
                        wire:model.live="startDate"
                        type="date"
                        label="{{ __('global.start_date') }}"
                    />

                    <flux:input
                        wire:model.live="endDate"
                        type="date"
                        label="{{ __('global.end_date') }}"
                    />
                @endif

                <flux:select
                    wire:model.live="campaignType"
                    label="{{ __('campaigns.campaign_type') }}"
                >
                    @foreach($this->campaignTypes as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select
                    wire:model.live="campaignStatus"
                    label="{{ __('campaigns.status') }}"
                >
                    @foreach($this->campaignStatuses as $value => $label)
                        <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>

            <div class="flex justify-end">
                <flux:button wire:click="exportReport" variant="primary" icon="arrow-down-tray">
                    {{ __('global.export_report') }}
                </flux:button>
            </div>
        </div>
    </flux:card>

    <!-- Estadísticas Generales -->
    <x-dashboard.section title="{{ __('campaigns.overview') }}">
        <x-dashboard.stats-grid>
            <x-dashboard.stats-card
                title="{{ __('campaigns.total_campaigns') }}"
                value="{{ $this->campaignStats['total_campaigns'] }}"
                icon="megaphone"
                color="primary"
            />

            <x-dashboard.stats-card
                title="{{ __('campaigns.active_campaigns') }}"
                value="{{ $this->campaignStats['active_campaigns'] }}"
                icon="play"
                color="success"
            />

            <x-dashboard.stats-card
                title="{{ __('campaigns.completed_campaigns') }}"
                value="{{ $this->campaignStats['completed_campaigns'] }}"
                icon="check-circle"
                color="info"
            />

            <x-dashboard.stats-card
                title="{{ __('campaigns.cancelled_campaigns') }}"
                value="{{ $this->campaignStats['cancelled_campaigns'] }}"
                icon="x-circle"
                color="danger"
            />
        </x-dashboard.stats-grid>
    </x-dashboard.section>

    <!-- Estadísticas de Mensajes -->
    @if($reportType === 'overview' || $reportType === 'messages')
        <x-dashboard.section title="{{ __('campaigns.messages_stats') }}">
            <x-dashboard.stats-grid>
                <x-dashboard.stats-card
                    title="{{ __('campaigns.total_messages') }}"
                    value="{{ $this->campaignStats['total_messages'] }}"
                    icon="chat-bubble-left-right"
                    color="primary"
                />

                <x-dashboard.stats-card
                    title="{{ __('campaigns.delivered_messages') }}"
                    value="{{ $this->campaignStats['delivered_messages'] }}"
                    icon="check"
                    color="success"
                    description="{{ __('campaigns.delivery_rate') }}: {{ $this->campaignStats['delivery_rate'] }}%"
                />

                <x-dashboard.stats-card
                    title="{{ __('campaigns.read_messages') }}"
                    value="{{ $this->campaignStats['read_messages'] }}"
                    icon="eye"
                    color="info"
                    description="{{ __('campaigns.read_rate') }}: {{ $this->campaignStats['read_rate'] }}%"
                />

                <x-dashboard.stats-card
                    title="{{ __('global.failed_messages') }}"
                    value="{{ $this->campaignStats['failed_messages'] }}"
                    icon="exclamation-circle"
                    color="danger"
                    description="{{ __('global.failure_rate') }}: {{ $this->campaignStats['failure_rate'] }}%"
                />
            </x-dashboard.stats-grid>
        </x-dashboard.section>
    @endif

    <!-- Distribución por Tipo y Estado -->
    @if($reportType === 'overview' || $reportType === 'performance')
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Distribución por Tipo -->
            <flux:card>
                <div class="p-4">
                    <flux:heading size="sm" class="mb-4">{{ __('global.campaigns_by_type') }}</flux:heading>
                    <div class="space-y-3">
                        @foreach($this->campaignStats['campaigns_by_type'] as $type => $count)
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <div class="text-sm">{{ $this->campaignTypes[$type] ?? $type }}</div>
                                    <div class="text-sm font-medium">{{ $count }}</div>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-primary-600 dark:bg-primary-500 h-2 rounded-full" style="width: {{ ($count / max(1, $this->campaignStats['total_campaigns'])) * 100 }}%"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </flux:card>

            <!-- Distribución por Estado -->
            <flux:card>
                <div class="p-4">
                    <flux:heading size="sm" class="mb-4">{{ __('global.campaigns_by_status') }}</flux:heading>
                    <div class="space-y-3">
                        @foreach($this->campaignStats['campaigns_by_status'] as $status => $count)
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <div class="text-sm">{{ $this->campaignStatuses[$status] ?? $status }}</div>
                                    <div class="text-sm font-medium">{{ $count }}</div>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-600 dark:bg-blue-500 h-2 rounded-full" style="width: {{ ($count / max(1, $this->campaignStats['total_campaigns'])) * 100 }}%"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </flux:card>
        </div>
    @endif

    <!-- Campañas Recientes -->
    <x-dashboard.section title="{{ __('campaigns.recent_campaigns') }}">
        <flux:card>
            <div class="overflow-x-auto">
                <flux:table>
                    <flux:table.columns>
                        <flux:table.column>{{ __('campaigns.name') }}</flux:table.column>
                        <flux:table.column>{{ __('campaigns.campaign_type') }}</flux:table.column>
                        <flux:table.column>{{ __('campaigns.status') }}</flux:table.column>
                        <flux:table.column>{{ __('campaigns.message_count') }}</flux:table.column>
                        <flux:table.column>{{ __('campaigns.campaign_created_at') }}</flux:table.column>
                        <flux:table.column>{{ __('global.actions') }}</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @forelse($this->recentCampaigns as $campaign)
                            <flux:table.row>
                                <flux:table.cell>{{ $campaign->name }}</flux:table.cell>
                                <flux:table.cell>{{ $this->campaignTypes[$campaign->type] ?? $campaign->type }}</flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge color="{{ match($campaign->status) {
                                        'active' => 'success',
                                        'scheduled' => 'warning',
                                        'completed' => 'info',
                                        'cancelled' => 'danger',
                                        default => 'primary'
                                    } }}">
                                        {{ $this->campaignStatuses[$campaign->status] ?? $campaign->status }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>{{ $campaign->details_count ?? 0 }}</flux:table.cell>
                                <flux:table.cell>{{ $campaign->created_at->format('d/m/Y H:i') }}</flux:table.cell>
                                <flux:table.cell>
                                    <flux:button :href="route('admin.campaigns.show', $campaign)" variant="primary" size="xs">
                                        {{ __('campaigns.view') }}
                                    </flux:button>
                                </flux:table.cell>
                            </flux:table.row>
                        @empty
                            <flux:table.row>
                                <flux:table.cell colspan="6" class="text-center py-4">
                                    <div class="text-gray-500 dark:text-gray-400">
                                        {{ __('global.no_recent_campaigns') }}
                                    </div>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforelse
                    </flux:table.rows>
                </flux:table>
            </div>
        </flux:card>
    </x-dashboard.section>
</div>
