<section class="w-full">
    <x-page-heading>

        <x-slot:title>
            <span class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Listado de empresas
            </span>
        </x-slot:title>
        <x-slot:subtitle>
            En este apartado se muestra un listado de todas las empresas que se encuentran
            registradas en el sistema.

            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{route('admin.index')}}" icon="home" >Inicio</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>Listado de empresas</flux:breadcrumbs.item>
            </flux:breadcrumbs>

        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:modal.trigger name="create-company">
                <flux:button>Nueva empresa</flux:button>
            </flux:modal.trigger>
        </x-slot:buttons>
    </x-page-heading>

    <div class="flex items-center justify-between w-full mb-6 gap-2">
        <flux:input
            wire:model.live.debounce.300ms="search"
            placeholder="Buscar..."
            icon="magnifying-glass"
            class="!w-64"
        />
        <flux:spacer/>

        <flux:select wire:model.live="perPage" class="!w-auto">
            <flux:select.option value="10">10</flux:select.option>
            <flux:select.option value="25">25</flux:select.option>
            <flux:select.option value="50">50</flux:select.option>
            <flux:select.option value="100">100</flux:select.option>
        </flux:select>

        <flux:select wire:model.live="status" class="!w-auto">
            <flux:select.option value="">Todos</flux:select.option>
            <flux:select.option value="active">Activas</flux:select.option>
            <flux:select.option value="inactive">Inactivas</flux:select.option>
        </flux:select>
    </div>

    <flux:table :paginate="$this->companies">
        <flux:table.columns>
            <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">ID</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'country'" :direction="$sortDirection" wire:click="sort('country')">País</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">Nombre</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'company_type'" :direction="$sortDirection" wire:click="sort('company_type')">Tipo</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'rif'" :direction="$sortDirection" wire:click="sort('rif')">RIF</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'phone'" :direction="$sortDirection" wire:click="sort('phone')">Teléfono</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'email'" :direction="$sortDirection" wire:click="sort('email')">Email</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'address'" :direction="$sortDirection" wire:click="sort('address')">Dirección</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'logo'" :direction="$sortDirection" wire:click="sort('logo')">Logo</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">Estado</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'tax_name'" :direction="$sortDirection" wire:click="sort('tax_name')">Nombre Fiscal</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'tax_number'" :direction="$sortDirection" wire:click="sort('tax_number')">Número Fiscal</flux:table.column>
            <flux:table.column sortable :sorted="$sortBy === 'postal_code'" :direction="$sortDirection" wire:click="sort('postal_code')">Código Postal</flux:table.column>
            <flux:table.column>Acciones</flux:table.column>
        </flux:table.columns>

        <flux:table.rows>
            @forelse ($this->companies as $company)
                <flux:table.row :key="$company->id">
                    <flux:table.cell>{{ $company->id }}</flux:table.cell>
                    <flux:table.cell>{{ $company->country }}</flux:table.cell>
                    <flux:table.cell>{{ $company->name }}</flux:table.cell>
                    <flux:table.cell>{{ $company->company_type }}</flux:table.cell>
                    <flux:table.cell>{{ $company->rif }}</flux:table.cell>
                    <flux:table.cell>{{ $company->phone }}</flux:table.cell>
                    <flux:table.cell>{{ $company->email }}</flux:table.cell>
                    <flux:table.cell>{{ $company->address }}</flux:table.cell>
                    <flux:table.cell>
                        @if($company->logo)
                            <img src="{{ Storage::url($company->logo) }}" alt="Logo" class="w-8 h-8 object-cover rounded-full">
                        @else
                            <span class="text-gray-400">Sin logo</span>
                        @endif
                    </flux:table.cell>
                    <flux:table.cell>
                        <flux:badge size="sm" :color="$company->status ? 'success' : 'danger'" inset="top bottom">
                            {{ $company->status ? 'Activo' : 'Inactivo' }}
                        </flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>{{ $company->tax_name }}</flux:table.cell>
                    <flux:table.cell>{{ $company->tax_number }}</flux:table.cell>
                    <flux:table.cell>{{ $company->postal_code }}</flux:table.cell>
                    <flux:table.cell>
                        <flux:dropdown>
                            <flux:button icon:trailing="chevron-down">Options</flux:button>

                            <flux:menu>
                                <flux:menu.group heading="Account">
                                    <flux:menu.item>Profile</flux:menu.item>
                                    <flux:menu.item>Permissions</flux:menu.item>
                                </flux:menu.group>

                                <flux:menu.group heading="Billing">
                                    <flux:menu.item>Transactions</flux:menu.item>
                                    <flux:menu.item>Payouts</flux:menu.item>
                                    <flux:menu.item>Refunds</flux:menu.item>
                                </flux:menu.group>

                                <flux:menu.item>Logout</flux:menu.item>
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
            @empty
                <flux:table.row>
                    <flux:table.cell colspan="13" class="text-center">
                        <x-heroicon-o-exclamation-circle class="w-12 h-12 mx-auto mb-4 text-gray-400" />
                        No se encontraron empresas
                    </flux:table.cell>
                </flux:table.row>

            @endforelse

        </flux:table.rows>

        <x-slot:empty>
            <div class="flex flex-col items-center justify-center py-12 text-gray-400">
                <x-heroicon-o-magnifying-glass class="w-16 h-16 mb-4" />
                <p class="text-lg font-medium">No se encontraron empresas</p>
                <p class="text-sm">Intenta ajustar los filtros o términos de búsqueda</p>
            </div>
        </x-slot:empty>
    </flux:table>


    <!-- Modals -->
    @include('livewire.admin.company.components.modals.create-company-modal')


</section>
