<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <span class="text-xl">🗳️</span>
                {{ __('Dashboard Electoral') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Gestión y seguimiento del proceso electoral') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" wire:navigate icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Electoral') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Estadísticas Generales -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Eventos Electorales</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->statistics['events']['total'] }}</div>
                        <div class="ml-2 text-sm text-green-600 dark:text-green-400">
                            {{ $this->statistics['events']['upcoming'] }} próximos
                        </div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                    <x-heroicon-o-calendar class="h-6 w-6 text-primary-600 dark:text-primary-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Centros de Votación</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->statistics['voting_centers']['total'] }}</div>
                        <div class="ml-2 text-sm text-green-600 dark:text-green-400">
                            {{ $this->statistics['voting_centers']['active'] }} activos
                        </div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                    <x-heroicon-o-building-office class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Movilización</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->statistics['mobilization']['mobilized_voters'] }}</div>
                        <div class="ml-2 text-sm text-green-600 dark:text-green-400">
                            {{ $this->statistics['mobilization']['confirmed_votes'] }} votos
                        </div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                    <x-heroicon-o-truck class="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Votantes</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->statistics['people']['voters'] }}</div>
                        <div class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                            de {{ $this->statistics['people']['total'] }}
                        </div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                    <x-heroicon-o-user-group class="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Accesos Rápidos -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center p-4">
                <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900 mb-3">
                    <x-heroicon-o-calendar class="h-8 w-8 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Eventos Electorales</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 text-center">Gestiona eventos, registra asistencia y genera reportes</p>
                <flux:button :href="route('admin.electoral.events')" wire:navigate class="mt-4" variant="primary">
                    Ir a Eventos
                </flux:button>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center p-4">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 mb-3">
                    <x-heroicon-o-truck class="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Movilización Electoral</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 text-center">Organiza la movilización de votantes y realiza seguimiento</p>
                <flux:button :href="route('admin.electoral.mobilization')" wire:navigate class="mt-4" variant="primary">
                    Ir a Movilización
                </flux:button>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center p-4">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 mb-3">
                    <x-heroicon-o-clipboard-document-check class="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Seguimiento de Votación</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 text-center">Realiza seguimiento en tiempo real del proceso de votación</p>
                <flux:button :href="route('admin.electoral.voting-tracker')" wire:navigate class="mt-4" variant="primary">
                    Ir a Seguimiento
                </flux:button>
            </div>
        </flux:card>
    </div>

    <!-- Próximos Eventos y Movilizaciones Recientes -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <flux:card>
            <div class="flex justify-between itemlinks-center mb-4">
                <flux:heading size="lg">Próximos Eventos</flux:heading>
                <flux:button :href="route('admin.electoral.events')" wire:navigate variant="primary" size="sm">
                    Ver todos
                </flux:button>
            </div>

            <div class="space-y-4">
                @forelse($this->upcomingEvents as $event)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex justify-between">
                            <div>
                                <div class="font-medium text-primary-600 dark:text-primary-400">
                                    {{ $event->name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $event->start_date->format('d/m/Y H:i') }} - {{ $event->end_date->format('d/m/Y H:i') }}
                                </div>
                                <div class="text-sm mt-1">
                                    <span class="text-gray-700 dark:text-gray-300">Ubicación:</span>
                                    {{ $event->location }}
                                </div>
                            </div>
                            <div>
                                <flux:badge color="{{ $event->status === 'scheduled' ? 'blue' : ($event->status === 'in_progress' ? 'yellow' : 'green') }}">
                                    {{ $event->status === 'scheduled' ? 'Programado' : ($event->status === 'in_progress' ? 'En Progreso' : 'Completado') }}
                                </flux:badge>
                            </div>
                        </div>
                        <div class="mt-2 flex justify-end">
                            <flux:button :href="route('admin.electoral.events.show', $event)" variant="primary" size="xs">
                                Ver Detalles
                            </flux:button>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                        No hay eventos próximos programados
                    </div>
                @endforelse
            </div>
        </flux:card>

        <flux:card>
            <div class="flex justify-between items-center mb-4">
                <flux:heading size="lg">Movilizaciones Recientes</flux:heading>
                <flux:button :href="route('admin.electoral.mobilization')" variant="primary" size="sm">
                    Ver todas
                </flux:button>
            </div>

            <div class="space-y-4">
                @forelse($this->recentMobilizations as $mobilization)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex justify-between">
                            <div>
                                <div class="font-medium text-primary-600 dark:text-primary-400">
                                    {{ $mobilization->group->name ?? 'Sin grupo asignado' }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $mobilization->mobilization_date->format('d/m/Y') }}
                                </div>
                                <div class="text-sm mt-1">
                                    <span class="text-gray-700 dark:text-gray-300">Centro:</span>
                                    {{ $mobilization->votingCenter->name ?? 'No especificado' }}
                                </div>
                            </div>
                            <div>
                                <flux:badge color="{{ $mobilization->status === 'scheduled' ? 'blue' : ($mobilization->status === 'in_progress' ? 'yellow' : ($mobilization->status === 'completed' ? 'green' : 'red')) }}">
                                    {{ $mobilization->status === 'scheduled' ? 'Programada' : ($mobilization->status === 'in_progress' ? 'En Progreso' : ($mobilization->status === 'completed' ? 'Completada' : 'Cancelada')) }}
                                </flux:badge>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span>Progreso de Movilización</span>
                                <span>{{ $mobilization->mobilized_voters }}/{{ $mobilization->target_voters }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-primary-600 h-2 rounded-full" style="width: {{ $mobilization->mobilization_progress }}%"></div>
                            </div>
                        </div>
                        <div class="mt-2 flex justify-end">
                            <flux:button :href="route('admin.electoral.mobilization.show', $mobilization)" variant="primary" size="xs">
                                Ver Detalles
                            </flux:button>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                        No hay movilizaciones recientes
                    </div>
                @endforelse
            </div>
        </flux:card>
    </div>
</div>
