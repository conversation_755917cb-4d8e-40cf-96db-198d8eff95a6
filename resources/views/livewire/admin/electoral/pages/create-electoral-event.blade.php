<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.calendar class="size-8" />
                {{ __('Crear Evento Electoral') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Registrar un nuevo evento electoral en el sistema') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('Electoral') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.events') }}">{{ __('Eventos') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Crear') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button :href="route('admin.electoral.events')" variant="subtle" icon="arrow-left">
                {{ __('Volver a Eventos') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <flux:card>
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Nombre y Descripción -->
                <div class="space-y-4">
                    <div>
                        <flux:label for="name">{{ __('Nombre del Evento') }} <span class="text-red-500">*</span></flux:label>
                        <flux:input id="name" wire:model="name" placeholder="Ingrese el nombre del evento" />
                        <flux:error for="name" />
                    </div>

                    <div>
                        <flux:label for="description">{{ __('Descripción') }}</flux:label>
                        <flux:textarea id="description" wire:model="description" placeholder="Describa el propósito y detalles del evento" rows="4" />
                        <flux:error for="description" />
                    </div>
                </div>

                <!-- Tipo y Estado -->
                <div class="space-y-4">
                    <div>
                        <flux:label for="type">{{ __('Tipo de Evento') }} <span class="text-red-500">*</span></flux:label>
                        <flux:select id="type" wire:model="type">
                            <flux:select.option value="training">{{ __('Capacitación') }}</flux:select.option>
                            <flux:select.option value="mobilization">{{ __('Movilización') }}</flux:select.option>
                            <flux:select.option value="voting">{{ __('Votación') }}</flux:select.option>
                            <flux:select.option value="meeting">{{ __('Reunión') }}</flux:select.option>
                            <flux:select.option value="other">{{ __('Otro') }}</flux:select.option>
                        </flux:select>
                        <flux:error for="type" />
                    </div>

                    <div>
                        <flux:label for="status">{{ __('Estado') }} <span class="text-red-500">*</span></flux:label>
                        <flux:select id="status" wire:model="status">
                            <flux:select.option value="scheduled">{{ __('Programado') }}</flux:select.option>
                            <flux:select.option value="in_progress">{{ __('En Progreso') }}</flux:select.option>
                            <flux:select.option value="completed">{{ __('Completado') }}</flux:select.option>
                            <flux:select.option value="cancelled">{{ __('Cancelado') }}</flux:select.option>
                        </flux:select>
                        <flux:error for="status" />
                    </div>

                    <div>
                        <flux:label for="capacity">{{ __('Capacidad') }}</flux:label>
                        <flux:input id="capacity" type="number" wire:model="capacity" placeholder="Número máximo de asistentes" min="1" />
                        <flux:error for="capacity" />
                    </div>
                </div>
            </div>

            <!-- Fechas -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:label for="start_date">{{ __('Fecha y Hora de Inicio') }} <span class="text-red-500">*</span></flux:label>
                    <flux:input id="start_date" type="datetime-local" wire:model="start_date" />
                    <flux:error for="start_date" />
                </div>

                <div>
                    <flux:label for="end_date">{{ __('Fecha y Hora de Finalización') }}</flux:label>
                    <flux:input id="end_date" type="datetime-local" wire:model="end_date" />
                    <flux:error for="end_date" />
                </div>
            </div>

            <!-- Ubicación -->
            <div class="space-y-4">
                <div>
                    <flux:label for="location">{{ __('Nombre del Lugar') }}</flux:label>
                    <flux:input id="location" wire:model="location" placeholder="Ej: Centro Comunitario, Plaza Principal, etc." />
                    <flux:error for="location" />
                </div>

                <div>
                    <flux:label for="address">{{ __('Dirección') }}</flux:label>
                    <flux:textarea id="address" wire:model="address" placeholder="Dirección completa del lugar del evento" rows="2" />
                    <flux:error for="address" />
                </div>

                <!-- Selector de Ubicación -->
                <div>
                    <livewire:location-selector
                        :estate-id="$estate_id"
                        :municipality-id="$municipality_id"
                        :parish-id="$parish_id"
                    />
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                        <flux:error for="estate_id" />
                        <flux:error for="municipality_id" />
                        <flux:error for="parish_id" />
                    </div>
                </div>
            </div>

            <!-- Botones de Acción -->
            <div class="flex justify-end space-x-3 pt-4">
                <flux:button :href="route('admin.electoral.events')" variant="subtle">
                    {{ __('Cancelar') }}
                </flux:button>
                <flux:button type="submit" variant="primary">
                    {{ __('Guardar Evento') }}
                </flux:button>
            </div>
        </form>
    </flux:card>
</div>
