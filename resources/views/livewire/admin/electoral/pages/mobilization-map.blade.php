<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-map class="size-6 text-primary-500"/>
                {{ __('Mapa de Movilización Electoral') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Visualización geográfica de la movilización electoral') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('Electoral') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Mapa de Movilización') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Filtros -->
    <flux:card>
        <div class="flex flex-col md:flex-row gap-4 items-start md:items-end">
            <div class="w-full md:w-auto space-y-2">
                <flux:label>Filtrar por Estado</flux:label>
                <flux:select wire:model.live="estateFilter" class="w-full md:w-64">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    @foreach($this->estates as $estate)
                        <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>

            @if($estateFilter)
            <div class="w-full md:w-auto space-y-2">
                <flux:label>Filtrar por Municipio</flux:label>
                <flux:select wire:model.live="municipalityFilter" class="w-full md:w-64">
                    <flux:select.option value="">Todos los municipios</flux:select.option>
                    @foreach($this->municipalities as $municipality)
                        <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>
            @endif

            <div class="w-full md:w-auto space-y-2">
                <flux:label>Filtrar por Estado de Movilización</flux:label>
                <flux:select wire:model.live="statusFilter" class="w-full md:w-64">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    <flux:select.option value="scheduled">Programadas</flux:select.option>
                    <flux:select.option value="in_progress">En Progreso</flux:select.option>
                    <flux:select.option value="completed">Completadas</flux:select.option>
                    <flux:select.option value="cancelled">Canceladas</flux:select.option>
                </flux:select>
            </div>
        </div>
    </flux:card>

    <!-- Estadísticas de Movilización -->
    <flux:card>
        <flux:heading size="lg">Estadísticas de Movilización</flux:heading>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mt-4">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <div class="flex flex-col items-center">
                    <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ $this->mobilizationStatistics['total_mobilizations'] }}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Total de Movilizaciones</span>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <div class="flex flex-col items-center">
                    <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $this->mobilizationStatistics['target_voters'] }}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Meta de Votantes</span>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <div class="flex flex-col items-center">
                    <span class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ $this->mobilizationStatistics['mobilized_voters'] }}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Votantes Movilizados</span>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <div class="flex flex-col items-center">
                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $this->mobilizationStatistics['confirmed_votes'] }}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Votos Confirmados</span>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <div class="flex flex-col items-center">
                    <span class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ $this->mobilizationStatistics['mobilization_progress'] }}%</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Progreso General</span>
                </div>
            </div>
        </div>

        <!-- Barras de Progreso -->
        <div class="mt-6 space-y-4">
            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span>Progreso de Movilización</span>
                    <span>{{ $this->mobilizationStatistics['mobilized_voters'] }}/{{ $this->mobilizationStatistics['target_voters'] }} ({{ $this->mobilizationStatistics['mobilization_progress'] }}%)</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-yellow-500 h-2.5 rounded-full" style="width: {{ $this->mobilizationStatistics['mobilization_progress'] }}%"></div>
                </div>
            </div>

            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span>Progreso de Votación</span>
                    <span>{{ $this->mobilizationStatistics['confirmed_votes'] }}/{{ $this->mobilizationStatistics['target_voters'] }} ({{ $this->mobilizationStatistics['voting_progress'] }}%)</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-green-500 h-2.5 rounded-full" style="width: {{ $this->mobilizationStatistics['voting_progress'] }}%"></div>
                </div>
            </div>
        </div>
    </flux:card>

    <!-- Mapa de Movilización -->
    <flux:card>
        <flux:heading size="lg">Mapa de Centros de Votación</flux:heading>
        <div class="mt-4">
            <!-- Aquí iría el mapa interactivo -->
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 h-[500px] flex items-center justify-center">
                @if(count($this->mapData['markers']) > 0)
                    <div class="w-full h-full relative">
                        <!-- Simulación de mapa con centros de votación -->
                        <div class="absolute inset-0 bg-gray-200 dark:bg-gray-800 rounded-lg overflow-hidden">
                            <!-- Representación visual de los centros de votación -->
                            @foreach($this->mapData['markers'] as $marker)
                                @php
                                    // Simulamos coordenadas para la visualización
                                    $left = rand(5, 95);
                                    $top = rand(5, 95);
                                    $size = min(max(20, $marker['info']['mobilization_progress'] / 2 + 20), 50);
                                @endphp
                                <div
                                    class="absolute rounded-full cursor-pointer transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center"
                                    style="left: {{ $left }}%; top: {{ $top }}%; width: {{ $size }}px; height: {{ $size }}px; background-color: {{ $marker['color'] === 'green' ? '#10B981' : ($marker['color'] === 'blue' ? '#3B82F6' : ($marker['color'] === 'yellow' ? '#F59E0B' : '#EF4444')) }};"
                                    title="{{ $marker['info']['name'] }} - {{ $marker['info']['mobilized_voters'] }}/{{ $marker['info']['target_voters'] }} ({{ $marker['info']['mobilization_progress'] }}%)"
                                >
                                    <span class="text-white text-xs font-bold">{{ $marker['info']['mobilization_progress'] }}%</span>
                                </div>
                            @endforeach
                        </div>

                        <!-- Leyenda del mapa -->
                        <div class="absolute bottom-4 right-4 bg-white dark:bg-gray-800 p-2 rounded-lg shadow-md">
                            <div class="text-sm font-medium mb-2">Leyenda</div>
                            <div class="space-y-1">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                    <span class="text-xs">75-100% Movilización</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                    <span class="text-xs">50-74% Movilización</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                    <span class="text-xs">25-49% Movilización</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                    <span class="text-xs">0-24% Movilización</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <x-heroicon-o-map class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <p class="text-gray-500 text-lg font-medium">No hay centros de votación disponibles</p>
                        <p class="text-gray-400 text-sm">Intenta ajustar los filtros o añade centros de votación</p>
                    </div>
                @endif
            </div>
            <div class="mt-4 text-sm text-gray-500 dark:text-gray-400 text-center">
                Nota: Para una implementación real, se recomienda utilizar una biblioteca de mapas como Leaflet o Google Maps.
            </div>
        </div>
    </flux:card>

    <!-- Tabla de Centros de Votación -->
    <flux:card>
        <flux:heading size="lg">Centros de Votación</flux:heading>
        <div class="overflow-x-auto mt-4">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column>Centro de Votación</flux:table.column>
                    <flux:table.column>Ubicación</flux:table.column>
                    <flux:table.column>Total Votantes</flux:table.column>
                    <flux:table.column>Meta</flux:table.column>
                    <flux:table.column>Movilizados</flux:table.column>
                    <flux:table.column>Votos Confirmados</flux:table.column>
                    <flux:table.column>Progreso</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->mapData['markers'] as $marker)
                        <flux:table.row>
                            <flux:table.cell>
                                <div class="font-medium">{{ $marker['info']['name'] }}</div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div>{{ $marker['info']['location'] ?? 'N/A' }}</div>
                                <div class="text-xs text-gray-500">{{ $marker['info']['address'] ?? 'No especificada' }}</div>
                            </flux:table.cell>
                            <flux:table.cell>{{ number_format($marker['info']['total_voters'] ?? 0) }}</flux:table.cell>
                            <flux:table.cell>{{ number_format($marker['info']['target_voters'] ?? 0) }}</flux:table.cell>
                            <flux:table.cell>{{ number_format($marker['info']['mobilized_voters'] ?? 0) }}</flux:table.cell>
                            <flux:table.cell>{{ number_format($marker['info']['confirmed_votes'] ?? 0) }}</flux:table.cell>
                            <flux:table.cell>
                                <div>
                                    <div class="flex justify-between text-xs mb-1">
                                        <span>Movilización</span>
                                        <span>{{ $marker['info']['mobilization_progress'] ?? 0 }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-yellow-500 h-1.5 rounded-full" style="width: {{ $marker['info']['mobilization_progress'] ?? 0 }}%"></div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="flex justify-between text-xs mb-1">
                                        <span>Votación</span>
                                        <span>{{ $marker['info']['voting_progress'] ?? 0 }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-green-500 h-1.5 rounded-full" style="width: {{ $marker['info']['voting_progress'] ?? 0 }}%"></div>
                                    </div>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="7" class="text-center py-4">
                                <p class="text-gray-500">No hay centros de votación disponibles</p>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>
    </flux:card>
</div>
