<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-building-office class="size-6 text-primary-500"/>
                {{ __('electoral.voting_centers') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('electoral.title_description') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('global.home') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('electoral.title') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('electoral.voting_centers') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button wire:click="openCreateCenterModal" icon="plus">
                {{ __('electoral.add_voting_center') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ $this->centerStats['total'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('electoral.voting_centers') }}</span>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $this->centerStats['active'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('electoral.voting_centers') }} {{ __('global.active') }}</span>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-red-600 dark:text-red-400">{{ $this->centerStats['inactive'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('electoral.voting_centers') }} {{ __('global.inactive') }}</span>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($this->centerStats['total_voters']) }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('electoral.total_voters') }}</span>
            </div>
        </flux:card>
    </div>

    <!-- Filtros y Búsqueda -->
    <flux:card>
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-between mb-6">
            <div class="flex items-center gap-2 w-full lg:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('electoral.search_voting_centers') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            <div class="flex flex-wrap items-center gap-2 w-full lg:w-auto">
                <flux:select wire:model.live="estateFilter" class="w-40">
                    <flux:select.option value="">{{ __('global.all_statuses') }}</flux:select.option>
                    @foreach($this->estates as $estate)
                        <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="statusFilter" class="w-40">
                    <flux:select.option value="">{{ __('global.all_statuses') }}</flux:select.option>
                    <flux:select.option value="active">{{ __('global.active') }}</flux:select.option>
                    <flux:select.option value="inactive">{{ __('global.inactive') }}</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="perPage" class="w-24">
                    <flux:select.option value="10">10</flux:select.option>
                    <flux:select.option value="25">25</flux:select.option>
                    <flux:select.option value="50">50</flux:select.option>
                    <flux:select.option value="100">100</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Tabla de Centros de Votación -->
        <div class="overflow-x-auto">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column class="min-w-[200px]" sortable wire:click="sortBy('name')" :direction="$sortField === 'name' ? $sortDirection : null">Nombre</flux:table.column>
                    <flux:table.column class="min-w-[120px]" sortable wire:click="sortBy('code')" :direction="$sortField === 'code' ? $sortDirection : null">Código</flux:table.column>
                    <flux:table.column class="min-w-[200px]">Dirección</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Ubicación</flux:table.column>
                    <flux:table.column class="min-w-[100px]" sortable wire:click="sortBy('total_voters')" :direction="$sortField === 'total_voters' ? $sortDirection : null">Votantes</flux:table.column>
                    <flux:table.column class="min-w-[100px]" sortable wire:click="sortBy('status')" :direction="$sortField === 'status' ? $sortDirection : null">Estado</flux:table.column>
                    <flux:table.column class="min-w-[100px]">Acciones</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->votingCenters as $center)
                        <flux:table.row wire:key="center-row-{{ $center->id }}">
                            <flux:table.cell>
                                <div class="font-medium text-primary-600 dark:text-primary-400">
                                    {{ $center->name }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $center->code ?: 'N/A' }}
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="text-sm">
                                    {{ $center->address ?: 'No especificada' }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="text-sm">
                                    {{ $center->estate->name ?? 'N/A' }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $center->municipality->name ?? '' }}{{ $center->parish ? ', ' . $center->parish->name : '' }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ number_format($center->total_voters ?: 0) }}
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" color="{{ $center->status === 'active' ? 'green' : 'red' }}" inset="top bottom">
                                    {{ $center->status === 'active' ? 'Activo' : 'Inactivo' }}
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    <flux:button wire:click="openEditCenterModal({{ $center->id }})" variant="primary" size="xs" icon="pencil">Editar</flux:button>
                                    <flux:button wire:click="toggleCenterStatus({{ $center->id }})" wire:confirm="{{ $center->status === 'active' ? '¿Estás seguro de desactivar este centro?' : '¿Estás seguro de activar este centro?' }}" variant="primary" size="xs" icon="{{ $center->status === 'active' ? 'x-mark' : 'check' }}" class="{{ $center->status === 'active' ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800' }}">
                                        {{ $center->status === 'active' ? 'Desactivar' : 'Activar' }}
                                    </flux:button>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="7" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <x-heroicon-o-building-office class="w-12 h-12 text-gray-400 mb-2" />
                                    <p class="text-gray-500 text-lg font-medium">No se encontraron centros de votación</p>
                                    <p class="text-gray-400 text-sm">Intenta ajustar los filtros o crea un nuevo centro</p>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-6 flex justify-center sm:justify-end">
            {{ $this->votingCenters->links() }}
        </div>
    </flux:card>

    <!-- Modal para crear centro de votación -->
    <flux:modal name="create-center-modal" wire:model="showCreateCenterModal" class="md:max-w-2xl">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Crear Centro de Votación</flux:heading>
                <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                    Ingresa la información del nuevo centro de votación.
                </flux:text>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <flux:input
                        wire:model="name"
                        label="Nombre del Centro"
                        placeholder="Ingrese el nombre del centro de votación"
                        required
                    />
                </div>

                <flux:input
                    wire:model="code"
                    label="Código"
                    placeholder="Ingrese el código del centro"
                />

                <flux:select
                    wire:model="status"
                    label="Estado"
                    required
                >
                    <flux:select.option value="active">Activo</flux:select.option>
                    <flux:select.option value="inactive">Inactivo</flux:select.option>
                </flux:select>

                <div class="md:col-span-2">
                    <flux:textarea
                        wire:model="address"
                        label="Dirección"
                        placeholder="Ingrese la dirección del centro"
                        rows="2"
                    />
                </div>

                <!-- Selector de ubicación (Estado, Municipio, Parroquia) -->
                <div class="md:col-span-2">
                    <flux:heading size="sm">Ubicación</flux:heading>
                    <livewire:location-selector
                        wire:key="location-selector-create"
                        :estate-id="$estate_id"
                        :municipality-id="$municipality_id"
                        :parish-id="$parish_id"
                    />
                </div>

                <flux:input
                    wire:model="location_coordinates"
                    label="Coordenadas (Latitud, Longitud)"
                    placeholder="Ej: 10.4806, -66.9036"
                />

                <flux:input
                    wire:model="total_voters"
                    type="number"
                    label="Total de Votantes"
                    min="1"
                />
            </div>

            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:button wire:click="closeCreateCenterModal" variant="primary">
                    Cancelar
                </flux:button>
                <flux:button wire:click="createCenter" variant="primary" icon="plus">
                    Crear Centro
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Modal para editar centro de votación -->
    <flux:modal name="edit-center-modal" wire:model="showEditCenterModal" class="md:max-w-2xl">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Editar Centro de Votación</flux:heading>
                <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                    Actualiza la información del centro de votación.
                </flux:text>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <flux:input
                        wire:model="name"
                        label="Nombre del Centro"
                        placeholder="Ingrese el nombre del centro de votación"
                        required
                    />
                </div>

                <flux:input
                    wire:model="code"
                    label="Código"
                    placeholder="Ingrese el código del centro"
                />

                <flux:select
                    wire:model="status"
                    label="Estado"
                    required
                >
                    <flux:select.option value="active">Activo</flux:select.option>
                    <flux:select.option value="inactive">Inactivo</flux:select.option>
                </flux:select>

                <div class="md:col-span-2">
                    <flux:textarea
                        wire:model="address"
                        label="Dirección"
                        placeholder="Ingrese la dirección del centro"
                        rows="2"
                    />
                </div>

                <!-- Selector de ubicación (Estado, Municipio, Parroquia) -->
                <div class="md:col-span-2">
                    <flux:heading size="sm">Ubicación</flux:heading>
                    <livewire:location-selector
                        wire:key="location-selector-edit"
                        :estate-id="$estate_id"
                        :municipality-id="$municipality_id"
                        :parish-id="$parish_id"
                    />
                </div>

                <flux:input
                    wire:model="location_coordinates"
                    label="Coordenadas (Latitud, Longitud)"
                    placeholder="Ej: 10.4806, -66.9036"
                />

                <flux:input
                    wire:model="total_voters"
                    type="number"
                    label="Total de Votantes"
                    min="1"
                />
            </div>

            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:button wire:click="closeEditCenterModal" variant="primary">
                    Cancelar
                </flux:button>
                <flux:button wire:click="updateCenter" variant="primary" icon="save">
                    Guardar Cambios
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
