<div class="w-full space-y-6">
    <!-- Modal para crear movilización -->
    <flux:modal name="create-mobilization-modal" wire:model="showCreateMobilizationModal" class="md:max-w-lg">
        <div class="space-y-6">
            <flux:heading size="lg">{{ __('electoral.mobilization') }}</flux:heading>

            <div class="space-y-4">
                <div>
                    <flux:label for="group_id">{{ __('person.role') }}</flux:label>
                    <div class="relative">
                        <flux:input
                            wire:model.live.debounce.300ms="searchGroup"
                            placeholder="{{ __('electoral.search_coordinator_placeholder') }}"
                            class="w-full"
                        />
                        @if($searchGroup && $this->groups->count() > 0)
                            <div class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                                @foreach($this->groups as $group)
                                    <div
                                        wire:key="group-{{ $group->id }}"
                                        wire:click="$set('group_id', {{ $group->id }}); $set('searchGroup', '{{ $group->name }}');"
                                        class="px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 {{ $group_id == $group->id ? 'bg-primary-50 dark:bg-primary-900' : '' }}"
                                    >
                                        {{ $group->name }}
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                    @error('group_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <flux:label for="voting_center_id">{{ __('electoral.voting_center') }}</flux:label>
                    <div class="relative">
                        <flux:input
                            wire:model.live.debounce.300ms="searchVotingCenter"
                            placeholder="{{ __('electoral.search_voting_centers') }}"
                            class="w-full"
                        />
                        @if($searchVotingCenter && $this->votingCenters->count() > 0)
                            <div class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                                @foreach($this->votingCenters as $center)
                                    <div
                                        wire:key="center-{{ $center->id }}"
                                        wire:click="$set('voting_center_id', {{ $center->id }}); $set('searchVotingCenter', '{{ $center->name }}');"
                                        class="px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 {{ $voting_center_id == $center->id ? 'bg-primary-50 dark:bg-primary-900' : '' }}"
                                    >
                                        {{ $center->name }} <span class="text-xs text-gray-500">({{ $center->estate->name }})</span>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                    @error('voting_center_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <flux:label for="coordinator_id">Coordinador</flux:label>
                    <div class="relative">
                        <flux:input
                            wire:model.live.debounce.300ms="searchCoordinator"
                            placeholder="Buscar coordinador..."
                            class="w-full"
                        />
                        @if($searchCoordinator && $this->potentialCoordinators->count() > 0)
                            <div class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                                @foreach($this->potentialCoordinators as $coordinator)
                                    <div
                                        wire:key="coordinator-{{ $coordinator->id }}"
                                        wire:click="$set('coordinator_id', {{ $coordinator->id }}); $set('searchCoordinator', '{{ $coordinator->name }}');"
                                        class="px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 {{ $coordinator_id == $coordinator->id ? 'bg-primary-50 dark:bg-primary-900' : '' }}"
                                    >
                                        {{ $coordinator->name }}
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                    @error('coordinator_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <flux:label for="mobilization_date">Fecha de Movilización</flux:label>
                    <flux:input
                        type="datetime-local"
                        wire:model="mobilization_date"
                        class="w-full"
                    />
                    @error('mobilization_date') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <flux:label for="target_voters">Meta de Votantes</flux:label>
                    <flux:input
                        type="number"
                        wire:model="target_voters"
                        class="w-full"
                        min="1"
                    />
                    @error('target_voters') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <flux:label for="notes">Notas</flux:label>
                    <flux:textarea
                        wire:model="notes"
                        class="w-full"
                        rows="3"
                    ></flux:textarea>
                    @error('notes') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="flex justify-end gap-2">
                <flux:button wire:click="closeCreateMobilizationModal" variant="primary">Cancelar</flux:button>
                <flux:button wire:click="createMobilization" variant="primary">Crear Movilización</flux:button>
            </div>
        </div>
    </flux:modal>
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-clipboard-document-check class="size-6 text-primary-500"/>
                {{ __('Seguimiento de Votación') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Seguimiento en tiempo real del proceso de votación') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('Electoral') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Seguimiento de Votación') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="flex flex-wrap gap-4 mb-6 overflow-x-auto pb-2">
        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Total de Movilizaciones</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->mobilizations->total() }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                    <x-heroicon-o-clipboard-document-check class="h-6 w-6 text-primary-600 dark:text-primary-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">En Progreso</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->mobilizations->where('status', 'in_progress')->count() }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                    <x-heroicon-o-play class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Votantes Movilizados</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->mobilizations->sum('mobilized_voters') }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                    <x-heroicon-o-user-group class="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Meta de Votantes</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->mobilizations->sum('target_voters') }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                    <x-heroicon-o-flag class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Votos Confirmados</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->mobilizations->sum('confirmed_votes') }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900">
                    <x-heroicon-o-check-badge class="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Filtros y Búsqueda -->
    <flux:card>
        <div class="flex justify-between items-center mb-4">
            <flux:heading size="md">Movilizaciones Electorales</flux:heading>
            <flux:button wire:click="openCreateMobilizationModal" variant="primary" icon="plus">Nueva Movilización</flux:button>
        </div>
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-between mb-6">
            <div class="flex items-center gap-2 w-full lg:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por grupo, centro, coordinador...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            <div class="flex flex-wrap items-center gap-2 w-full lg:w-auto">
                <flux:select wire:model.live="statusFilter" class="w-40">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    <flux:select.option value="scheduled">Programadas</flux:select.option>
                    <flux:select.option value="in_progress">En Progreso</flux:select.option>
                    <flux:select.option value="completed">Completadas</flux:select.option>
                    <flux:select.option value="cancelled">Canceladas</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="estateFilter" class="w-40">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    @foreach($this->estates as $estate)
                        <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="dateFilter" class="w-40">
                    <flux:select.option value="">Todas las fechas</flux:select.option>
                    <flux:select.option value="today">Hoy</flux:select.option>
                    <flux:select.option value="week">Esta semana</flux:select.option>
                    <flux:select.option value="month">Este mes</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="perPage" class="w-24">
                    <flux:select.option value="10">10</flux:select.option>
                    <flux:select.option value="25">25</flux:select.option>
                    <flux:select.option value="50">50</flux:select.option>
                    <flux:select.option value="100">100</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Tabla de Seguimiento de Votación -->
        <div class="overflow-x-auto">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column class="min-w-[150px]">Centro de Votación</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Grupo</flux:table.column>
                    <flux:table.column class="min-w-[120px]">Coordinador</flux:table.column>
                    <flux:table.column class="min-w-[100px]">Estado</flux:table.column>
                    <flux:table.column class="min-w-[120px]">Progreso</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Acciones</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->mobilizations as $mobilization)
                        <flux:table.row wire:key="mobilization-row-{{ $mobilization->id }}">
                            <flux:table.cell>
                                <div class="font-medium">
                                    {{ $mobilization->votingCenter->name ?? 'No especificado' }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $mobilization->votingCenter->estate->name ?? '' }}{{ $mobilization->votingCenter->municipality ? ', ' . $mobilization->votingCenter->municipality->name : '' }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $mobilization->group->name ?? 'No especificado' }}
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    Líder 1x10
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $mobilization->coordinator->name ?? 'No especificado' }}
                            </flux:table.cell>
                            <flux:table.cell>
                                @php
                                    $statusLabels = [
                                        'scheduled' => 'Programada',
                                        'in_progress' => 'En Progreso',
                                        'completed' => 'Completada',
                                        'cancelled' => 'Cancelada'
                                    ];
                                    $statusColors = [
                                        'scheduled' => 'blue',
                                        'in_progress' => 'yellow',
                                        'completed' => 'green',
                                        'cancelled' => 'red'
                                    ];
                                @endphp
                                <flux:badge size="sm" color="{{ $statusColors[$mobilization->status] ?? 'gray' }}" inset="top bottom">
                                    {{ $statusLabels[$mobilization->status] ?? 'Desconocido' }}
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    <span class="font-medium">{{ $mobilization->mobilized_voters }}</span>
                                    <span class="text-gray-500 dark:text-gray-400">/</span>
                                    <span>{{ $mobilization->target_voters }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ $mobilization->mobilization_progress }}%"></div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    @if($mobilization->status === 'scheduled')
                                        <flux:button wire:click="updateMobilizationStatus({{ $mobilization->id }}, 'in_progress')" variant="primary" size="xs" icon="play" class="text-yellow-600 hover:text-yellow-800">Iniciar</flux:button>
                                    @endif

                                    @if($mobilization->status === 'in_progress')
                                        <flux:button wire:click="updateMobilizationStatus({{ $mobilization->id }}, 'completed')" variant="primary" size="xs" icon="check" class="text-green-600 hover:text-green-800">Completar</flux:button>
                                    @endif

                                    <flux:button :href="route('admin.electoral.mobilization.details', $mobilization)" variant="primary" size="xs" icon="user-group">Votantes</flux:button>

                                    <flux:modal.trigger name="mobilization-actions-{{ $mobilization->id }}">
                                        <flux:button variant="primary" size="xs" icon="ellipsis-horizontal"></flux:button>
                                    </flux:modal.trigger>

                                    <flux:modal name="mobilization-actions-{{ $mobilization->id }}" class="md:max-w-md">
                                        <div class="space-y-4">
                                            <flux:heading size="lg">Acciones de Movilización</flux:heading>
                                            <flux:text>Centro: {{ $mobilization->votingCenter->name }}</flux:text>
                                            <flux:text>Líder 1x10: {{ $mobilization->group->name }}</flux:text>

                                            <div class="space-y-2">
                                                <flux:button :href="route('admin.electoral.mobilization.show', $mobilization)" variant="primary" size="sm" icon="eye" class="w-full">Ver Detalles</flux:button>

                                                @if($mobilization->status === 'scheduled')
                                                    <flux:button wire:click="updateMobilizationStatus({{ $mobilization->id }}, 'in_progress')" variant="primary" size="sm" icon="play" class="w-full text-yellow-600 hover:text-yellow-800">Iniciar Movilización</flux:button>
                                                @endif

                                                @if($mobilization->status === 'in_progress')
                                                    <flux:button wire:click="updateMobilizationStatus({{ $mobilization->id }}, 'completed')" variant="primary" size="sm" icon="check" class="w-full text-green-600 hover:text-green-800">Completar Movilización</flux:button>
                                                @endif

                                                <flux:button :href="route('admin.electoral.mobilization.details', $mobilization)" variant="primary" size="sm" icon="user-group" class="w-full">Gestionar Votantes</flux:button>

                                                @if($mobilization->status !== 'cancelled' && $mobilization->status !== 'completed')
                                                    <flux:button wire:click="updateMobilizationStatus({{ $mobilization->id }}, 'cancelled')" wire:confirm="¿Estás seguro de cancelar esta movilización?" variant="primary" size="sm" icon="x-mark" class="w-full text-red-600 hover:text-red-800">Cancelar Movilización</flux:button>
                                                @endif
                                            </div>
                                        </div>
                                    </flux:modal>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="6" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <x-heroicon-o-clipboard-document-check class="w-12 h-12 text-gray-400 mb-2" />
                                    <p class="text-gray-500 text-lg font-medium">No se encontraron movilizaciones para seguimiento</p>
                                    <p class="text-gray-400 text-sm">Intenta ajustar los filtros o crea una nueva movilización</p>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-6 flex justify-center sm:justify-end">
            {{ $this->mobilizations->links() }}
        </div>
    </flux:card>
</div>
