<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-truck class="size-6 text-primary-500"/>
                {{ __('electoral.mobilization') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('electoral.title_description') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('global.home') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('electoral.title') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('electoral.mobilization') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button wire:click="openCreateMobilizationModal" icon="plus">
                {{ __('electoral.mobilization') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="flex items-center gap-4">
        <flux:card class="bg-white dark:bg-gray-800 flex-1">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ $this->mobilizationStats['total'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('electoral.mobilization') }}</span>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 flex-1">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $this->mobilizationStats['scheduled'] + $this->mobilizationStats['in_progress'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('global.scheduled') }}</span>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 flex-1">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $this->mobilizationStats['mobilized_voters'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('electoral.voters') }}</span>
            </div>
        </flux:card>

        <flux:card class="bg-white dark:bg-gray-800 flex-1">
            <div class="flex flex-col items-center">
                <span class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ $this->mobilizationStats['confirmed_votes'] }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">Votos Confirmados</span>
            </div>
        </flux:card>
    </div>

    <!-- Filtros y Búsqueda -->
    <flux:card>
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-between mb-6">
            <div class="flex items-center gap-2 w-full lg:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por nombre, grupo, centro...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            <div class="flex flex-wrap items-center gap-2 w-full lg:w-auto">
                <flux:select wire:model.live="statusFilter" class="w-40">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    <flux:select.option value="scheduled">Programadas</flux:select.option>
                    <flux:select.option value="in_progress">En Progreso</flux:select.option>
                    <flux:select.option value="completed">Completadas</flux:select.option>
                    <flux:select.option value="cancelled">Canceladas</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="estateFilter" class="w-40">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    @foreach($this->estates as $estate)
                        <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="dateFilter" class="w-40">
                    <flux:select.option value="">Todas las fechas</flux:select.option>
                    <flux:select.option value="today">Hoy</flux:select.option>
                    <flux:select.option value="tomorrow">Mañana</flux:select.option>
                    <flux:select.option value="this_week">Esta semana</flux:select.option>
                    <flux:select.option value="next_week">Próxima semana</flux:select.option>
                    <flux:select.option value="this_month">Este mes</flux:select.option>
                    <flux:select.option value="past">Pasadas</flux:select.option>
                    <flux:select.option value="upcoming">Próximas</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="perPage" class="w-24">
                    <flux:select.option value="10">10</flux:select.option>
                    <flux:select.option value="25">25</flux:select.option>
                    <flux:select.option value="50">50</flux:select.option>
                    <flux:select.option value="100">100</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Tabla de Movilizaciones -->
        <div class="overflow-x-auto">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column class="min-w-[200px]" sortable wire:click="sortBy('name')" :direction="$sortField === 'name' ? $sortDirection : null">{{ __('electoral.name') }}</flux:table.column>
                    <flux:table.column class="min-w-[150px]" sortable wire:click="sortBy('mobilization_date')" :direction="$sortField === 'mobilization_date' ? $sortDirection : null">{{ __('global.date') }}</flux:table.column>
                    <flux:table.column class="min-w-[150px]">{{ __('electoral.voting_center') }}</flux:table.column>
                    <flux:table.column class="min-w-[150px]">{{ __('person.role') }}</flux:table.column>
                    <flux:table.column class="min-w-[120px]">{{ __('electoral.coordinator') }}</flux:table.column>
                    <flux:table.column class="min-w-[100px]" sortable wire:click="sortBy('status')" :direction="$sortField === 'status' ? $sortDirection : null">{{ __('global.status') }}</flux:table.column>
                    <flux:table.column class="min-w-[120px]">{{ __('electoral.participation') }}</flux:table.column>
                    <flux:table.column class="min-w-[100px]">{{ __('global.actions') }}</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->mobilizations as $mobilization)
                        <flux:table.row wire:key="mobilization-row-{{ $mobilization->id }}">
                            <flux:table.cell>
                                <div class="font-medium text-primary-600 dark:text-primary-400">
                                    {{ $mobilization->name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ Str::limit($mobilization->description, 50) }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $mobilization->mobilization_date->format('d/m/Y') }}
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="text-sm">
                                    {{ $mobilization->votingCenter->name ?? __('Not specified') }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $mobilization->votingCenter->estate->name ?? '' }}{{ $mobilization->votingCenter->municipality ? ', ' . $mobilization->votingCenter->municipality->name : '' }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $mobilization->group->name ?? __('Not specified') }}
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $mobilization->coordinator->name ?? __('Not specified') }}
                            </flux:table.cell>
                            <flux:table.cell>
                                @php
                                    $statusLabels = [
                                        'scheduled' => __('global.scheduled'),
                                        'in_progress' => __('In Progress'),
                                        'completed' => __('global.completed'),
                                        'cancelled' => __('Cancelled')
                                    ];
                                    $statusColors = [
                                        'scheduled' => 'blue',
                                        'in_progress' => 'yellow',
                                        'completed' => 'green',
                                        'cancelled' => 'red'
                                    ];
                                @endphp
                                <flux:badge size="sm" color="{{ $statusColors[$mobilization->status] ?? 'gray' }}" inset="top bottom">
                                    {{ $statusLabels[$mobilization->status] ?? 'Desconocido' }}
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    <span class="font-medium">{{ $mobilization->mobilized_voters }}</span>
                                    <span class="text-gray-500 dark:text-gray-400">/</span>
                                    <span>{{ $mobilization->target_voters }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ $mobilization->mobilization_progress }}%"></div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    <flux:button :href="route('admin.electoral.mobilization.show', $mobilization)" variant="primary" size="xs" icon="eye">Ver</flux:button>
                                    @if($mobilization->status !== 'cancelled' && $mobilization->status !== 'completed')
                                        <flux:button wire:click="cancelMobilization({{ $mobilization->id }})" wire:confirm="¿Estás seguro de cancelar esta movilización?" variant="primary" size="xs" icon="x-mark" class="text-red-600 hover:text-red-800">Cancelar</flux:button>
                                    @endif
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="8" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <x-heroicon-o-truck class="w-12 h-12 text-gray-400 mb-2" />
                                    <p class="text-gray-500 text-lg font-medium">No se encontraron movilizaciones</p>
                                    <p class="text-gray-400 text-sm">Intenta ajustar los filtros o crea una nueva movilización</p>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-6 flex justify-center sm:justify-end">
            {{ $this->mobilizations->links() }}
        </div>
    </flux:card>

    <!-- Modal para crear movilización -->
    <flux:modal name="create-mobilization-modal" wire:model="showCreateMobilizationModal" class="md:max-w-2xl">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Crear Movilización Electoral</flux:heading>
                <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                    Ingresa la información para la nueva movilización electoral.
                </flux:text>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <flux:input
                        wire:model="name"
                        label="Nombre de la Movilización"
                        placeholder="Ingrese el nombre de la movilización"
                        required
                    />
                </div>

                <div class="md:col-span-2">
                    <flux:textarea
                        wire:model="description"
                        label="Descripción"
                        placeholder="Ingrese una descripción de la movilización"
                        rows="2"
                    />
                </div>

                <flux:input
                    wire:model="mobilization_date"
                    type="date"
                    label="Fecha de Movilización"
                    required
                />

                <flux:select
                    wire:model="status"
                    label="Estado"
                    required
                >
                    <flux:select.option value="scheduled">Programada</flux:select.option>
                    <flux:select.option value="in_progress">En Progreso</flux:select.option>
                    <flux:select.option value="completed">Completada</flux:select.option>
                    <flux:select.option value="cancelled">Cancelada</flux:select.option>
                </flux:select>

                <div class="md:col-span-2">
                    <flux:label>Centro de Votación</flux:label>
                    <flux:select
                        wire:model="voting_center_id"
                        class="w-full"
                        required
                    >
                        <flux:select.option value="">Seleccione un centro de votación</flux:select.option>
                        @foreach($this->votingCenters as $center)
                            <flux:select.option value="{{ $center->id }}">{{ $center->name }} ({{ $center->estate->name ?? '' }}{{ $center->municipality ? ', ' . $center->municipality->name : '' }})</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>

                <div class="md:col-span-2">
                    <flux:label>Grupo 1x10</flux:label>
                    <flux:input
                        wire:model.live.debounce.300ms="searchGroup"
                        placeholder="Buscar grupo por nombre o líder"
                        icon="magnifying-glass"
                    />

                    <div class="mt-2 space-y-2 max-h-40 overflow-y-auto">
                        @forelse($this->potentialGroups as $group)
                            <div
                                wire:key="group-{{ $group->id }}"
                                wire:click="$set('group_id', '{{ $group->id }}')"
                                class="p-3 rounded-lg border cursor-pointer transition-colors {{ $group_id == $group->id ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800' }}"
                            >
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">{{ $group->name }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            Líder: {{ $group->leader->name ?? 'No especificado' }}
                                        </div>
                                    </div>
                                    <div>
                                        <input type="radio" class="rounded-full border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" {{ $group_id == $group->id ? 'checked' : '' }}>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-2 text-gray-500">
                                {{ empty($searchGroup) ? 'Ingrese un término de búsqueda' : 'No se encontraron grupos' }}
                            </div>
                        @endforelse
                    </div>
                </div>

                <div class="md:col-span-2">
                    <flux:label>Coordinador</flux:label>
                    <flux:input
                        wire:model.live.debounce.300ms="searchCoordinator"
                        placeholder="Buscar coordinador por nombre o identificación"
                        icon="magnifying-glass"
                    />

                    <div class="mt-2 space-y-2 max-h-40 overflow-y-auto">
                        @forelse($this->potentialCoordinators as $coordinator)
                            <div
                                wire:key="coordinator-{{ $coordinator->id }}"
                                wire:click="$set('coordinator_id', '{{ $coordinator->id }}')"
                                class="p-3 rounded-lg border cursor-pointer transition-colors {{ $coordinator_id == $coordinator->id ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800' }}"
                            >
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">{{ $coordinator->name }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $coordinator->cedula }}
                                        </div>
                                    </div>
                                    <div>
                                        <input type="radio" class="rounded-full border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" {{ $coordinator_id == $coordinator->id ? 'checked' : '' }}>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-2 text-gray-500">
                                {{ empty($searchCoordinator) ? 'Ingrese un término de búsqueda' : 'No se encontraron personas' }}
                            </div>
                        @endforelse
                    </div>
                </div>

                <flux:input
                    wire:model="target_voters"
                    type="number"
                    label="Meta de Votantes"
                    min="1"
                    required
                />

                <div class="md:col-span-2">
                    <flux:textarea
                        wire:model="notes"
                        label="Notas"
                        placeholder="Ingrese notas adicionales sobre la movilización"
                        rows="2"
                    />
                </div>
            </div>

            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <flux:button wire:click="closeCreateMobilizationModal" variant="primary">
                    Cancelar
                </flux:button>
                <flux:button wire:click="createMobilization" variant="primary" icon="plus">
                    Crear Movilización
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
