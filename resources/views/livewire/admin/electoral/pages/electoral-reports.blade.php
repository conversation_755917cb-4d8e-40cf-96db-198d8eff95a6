<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-document-chart-bar class="size-6 text-primary-500"/>
                {{ __('Reportes Electorales') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Análisis y reportes del proceso electoral') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('Electoral') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Reportes') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Filtros -->
    <flux:card>
        <div class="flex flex-col md:flex-row gap-4 items-start md:items-end justify-between">
            <div class="flex flex-col md:flex-row gap-4 items-start md:items-end">
                <div class="w-full md:w-auto space-y-2">
                    <flux:label>Tipo de Reporte</flux:label>
                    <flux:select wire:model.live="reportType" class="w-full md:w-64">
                        <flux:select.option value="attendance">Asistencia a Eventos</flux:select.option>
                        <flux:select.option value="mobilization">Movilización Electoral</flux:select.option>
                        <flux:select.option value="voting">Seguimiento de Votación</flux:select.option>
                    </flux:select>
                </div>

                <div class="w-full md:w-auto space-y-2">
                    <flux:label>Rango de Fechas</flux:label>
                    <div class="flex gap-2">
                        <flux:input
                            wire:model.live="dateRange.start"
                            type="date"
                            class="w-full md:w-40"
                        />
                        <flux:input
                            wire:model.live="dateRange.end"
                            type="date"
                            class="w-full md:w-40"
                        />
                    </div>
                </div>

                <div class="w-full md:w-auto space-y-2">
                    <flux:label>Filtrar por Estado</flux:label>
                    <flux:select wire:model.live="estateFilter" class="w-full md:w-64">
                        <flux:select.option value="">Todos los estados</flux:select.option>
                        @foreach($this->estates as $estate)
                            <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>

                @if($estateFilter)
                <div class="w-full md:w-auto space-y-2">
                    <flux:label>Filtrar por Municipio</flux:label>
                    <flux:select wire:model.live="municipalityFilter" class="w-full md:w-64">
                        <flux:select.option value="">Todos los municipios</flux:select.option>
                        @foreach($this->municipalities as $municipality)
                            <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
                @endif
            </div>

            <div>
                <flux:button wire:click="exportToCSV" variant="primary" icon="document-arrow-down">
                    Exportar a CSV
                </flux:button>
            </div>
        </div>
    </flux:card>

    <!-- Reporte de Asistencia a Eventos -->
    @if($reportType === 'attendance')
        <div class="space-y-6">
            <flux:heading size="lg">Reporte de Asistencia a Eventos</flux:heading>

            <!-- Estadísticas -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ $this->attendanceStats['totals']['events'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Total de Eventos</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $this->attendanceStats['totals']['attendees'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Total de Asistentes</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ $this->attendanceStats['totals']['confirmed'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Confirmados</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $this->attendanceStats['totals']['attended'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Asistieron</span>
                    </div>
                </flux:card>
            </div>

            <!-- Gráfico -->
            <flux:card>
                <flux:heading size="md">Asistencia por Día</flux:heading>
                <div class="h-80 mt-4">
                    @if(count($this->attendanceStats['chart']['labels']) > 0)
                        <!-- Representación visual mejorada del gráfico de barras -->
                        <div class="h-full flex items-end gap-1 overflow-x-auto pb-6">
                            @foreach($this->attendanceStats['chart']['values'] as $index => $value)
                                <div class="flex-1 min-w-10 flex flex-col items-center">
                                    <div class="relative group">
                                        <div class="w-full bg-primary-500 rounded-t" style="height: {{ min(100, max(20, $value * 8)) }}%"></div>
                                        <div class="absolute bottom-full mb-1 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                            {{ $value }} asistentes el {{ $this->attendanceStats['chart']['labels'][$index] ?? '' }}
                                        </div>
                                    </div>
                                    <div class="text-xs mt-1 transform -rotate-45 origin-top-left whitespace-nowrap">{{ $this->attendanceStats['chart']['labels'][$index] ?? '' }}</div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="h-full flex items-center justify-center">
                            <p class="text-gray-500">No hay datos disponibles para el periodo seleccionado</p>
                        </div>
                    @endif
                </div>
            </flux:card>
        </div>
    @endif

    <!-- Reporte de Movilización Electoral -->
    @if($reportType === 'mobilization')
        <div class="space-y-6">
            <flux:heading size="lg">Reporte de Movilización Electoral</flux:heading>

            <!-- Estadísticas -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ $this->mobilizationStats['totals']['mobilizations'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Total de Movilizaciones</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $this->mobilizationStats['totals']['target_voters'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Meta de Votantes</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ $this->mobilizationStats['totals']['mobilized_voters'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Votantes Movilizados</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $this->mobilizationStats['totals']['confirmed_votes'] }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Votos Confirmados</span>
                    </div>
                </flux:card>
            </div>

            <!-- Gráfico -->
            <flux:card>
                <flux:heading size="md">Estado de Movilización</flux:heading>
                <div class="h-80 mt-4">
                    @if(array_sum($this->mobilizationStats['status_chart']['values']) > 0)
                        <!-- Representación visual de un gráfico de barras horizontales -->
                        <div class="space-y-4 h-full flex flex-col justify-center">
                            @foreach($this->mobilizationStats['status_chart']['labels'] as $index => $label)
                                @php
                                    $value = $this->mobilizationStats['status_chart']['values'][$index] ?? 0;
                                    $max = max($this->mobilizationStats['status_chart']['values']);
                                    $width = $max > 0 ? ($value / $max) * 100 : 0;
                                    $colors = ['bg-blue-500', 'bg-yellow-500', 'bg-indigo-500', 'bg-purple-500', 'bg-green-500', 'bg-red-500'];
                                @endphp
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span>{{ $label }}</span>
                                        <span>{{ $value }}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="{{ $colors[$index] ?? 'bg-gray-500' }} h-2.5 rounded-full" style="width: {{ $width }}%"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="h-full flex items-center justify-center">
                            <p class="text-gray-500">No hay datos disponibles para el periodo seleccionado</p>
                        </div>
                    @endif
                </div>
            </flux:card>
        </div>
    @endif

    <!-- Reporte de Seguimiento de Votación -->
    @if($reportType === 'voting')
        <div class="space-y-6">
            <flux:heading size="lg">Reporte de Seguimiento de Votación</flux:heading>

            <!-- Estadísticas -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ number_format($this->votingStats['totals']['total_voters']) }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Total de Votantes</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($this->votingStats['totals']['target_voters']) }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Meta de Votantes</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($this->votingStats['totals']['mobilized_voters']) }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Votantes Movilizados</span>
                    </div>
                </flux:card>

                <flux:card class="bg-white dark:bg-gray-800">
                    <div class="flex flex-col items-center">
                        <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($this->votingStats['totals']['confirmed_votes']) }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Votos Confirmados</span>
                    </div>
                </flux:card>
            </div>

            <!-- Progreso General -->
            <flux:card>
                <flux:heading size="md">Progreso General</flux:heading>
                <div class="mt-4 space-y-6">
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Progreso de Movilización</span>
                            <span>{{ $this->votingStats['totals']['mobilized_voters'] }}/{{ $this->votingStats['totals']['target_voters'] }} ({{ $this->votingStats['totals']['mobilization_progress'] }}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-yellow-500 h-2.5 rounded-full" style="width: {{ $this->votingStats['totals']['mobilization_progress'] }}%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Progreso de Votación</span>
                            <span>{{ $this->votingStats['totals']['confirmed_votes'] }}/{{ $this->votingStats['totals']['target_voters'] }} ({{ $this->votingStats['totals']['voting_progress'] }}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-green-500 h-2.5 rounded-full" style="width: {{ $this->votingStats['totals']['voting_progress'] }}%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>Progreso General</span>
                            <span>{{ $this->votingStats['totals']['confirmed_votes'] }}/{{ $this->votingStats['totals']['total_voters'] }} ({{ $this->votingStats['totals']['overall_progress'] }}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-primary-500 h-2.5 rounded-full" style="width: {{ $this->votingStats['totals']['overall_progress'] }}%"></div>
                        </div>
                    </div>
                </div>
            </flux:card>

            <!-- Tabla de Centros de Votación -->
            <flux:card>
                <flux:heading size="md">Desempeño por Centro de Votación</flux:heading>
                <div class="overflow-x-auto mt-4">
                    <flux:table class="min-w-full">
                        <flux:table.columns>
                            <flux:table.column>Centro de Votación</flux:table.column>
                            <flux:table.column>Ubicación</flux:table.column>
                            <flux:table.column>Total Votantes</flux:table.column>
                            <flux:table.column>Meta</flux:table.column>
                            <flux:table.column>Movilizados</flux:table.column>
                            <flux:table.column>Votos Confirmados</flux:table.column>
                            <flux:table.column>Progreso</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            @forelse($this->votingStats['centers'] as $center)
                                <flux:table.row>
                                    <flux:table.cell>
                                        <div class="font-medium">{{ $center['name'] }}</div>
                                        <div class="text-xs text-gray-500">{{ $center['code'] }}</div>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <div>{{ $center['estate'] }}</div>
                                        <div class="text-xs text-gray-500">{{ $center['municipality'] }}</div>
                                    </flux:table.cell>
                                    <flux:table.cell>{{ number_format($center['total_voters']) }}</flux:table.cell>
                                    <flux:table.cell>{{ number_format($center['target_voters']) }}</flux:table.cell>
                                    <flux:table.cell>{{ number_format($center['mobilized_voters']) }}</flux:table.cell>
                                    <flux:table.cell>{{ number_format($center['confirmed_votes']) }}</flux:table.cell>
                                    <flux:table.cell>
                                        <div>
                                            <div class="flex justify-between text-xs mb-1">
                                                <span>Movilización</span>
                                                <span>{{ $center['mobilization_progress'] }}%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                                <div class="bg-yellow-500 h-1.5 rounded-full" style="width: {{ $center['mobilization_progress'] }}%"></div>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <div class="flex justify-between text-xs mb-1">
                                                <span>Votación</span>
                                                <span>{{ $center['voting_progress'] }}%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                                <div class="bg-green-500 h-1.5 rounded-full" style="width: {{ $center['voting_progress'] }}%"></div>
                                            </div>
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @empty
                                <flux:table.row>
                                    <flux:table.cell colspan="7" class="text-center py-4">
                                        <p class="text-gray-500">No hay datos disponibles para el periodo seleccionado</p>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforelse
                        </flux:table.rows>
                    </flux:table>
                </div>
            </flux:card>
        </div>
    @endif
</div>
