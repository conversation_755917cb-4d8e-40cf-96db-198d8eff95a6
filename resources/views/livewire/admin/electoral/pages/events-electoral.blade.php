<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-calendar class="size-6 text-primary-500"/>
                {{ __('Eventos Electorales') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Gestión de eventos electorales y control de asistencia') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.electoral.index') }}">{{ __('Electoral') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Eventos') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button :href="route('admin.electoral.events.create')" icon="plus">
                {{ __('Crear Evento') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="flex flex-wrap gap-4 mb-6 overflow-x-auto pb-2">
        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Total de Eventos</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->eventStats['total'] }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                    <x-heroicon-o-calendar class="h-6 w-6 text-primary-600 dark:text-primary-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Próximos</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->eventStats['upcoming'] }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                    <x-heroicon-o-clock class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">En Progreso</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->eventStats['in_progress'] }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                    <x-heroicon-o-play class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Completados</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->eventStats['completed'] }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                    <x-heroicon-o-check-circle class="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-none min-w-[200px] bg-white dark:bg-gray-800">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Cancelados</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $this->eventStats['cancelled'] }}</div>
                    </div>
                </div>
                <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
                    <x-heroicon-o-x-circle class="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Filtros y Búsqueda -->
    <flux:card>
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-between mb-6">
            <div class="flex items-center gap-2 w-full lg:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por nombre, descripción, ubicación...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            <div class="flex flex-wrap items-center gap-2 w-full lg:w-auto">
                <flux:select wire:model.live="typeFilter" class="w-40">
                    <flux:select.option value="">Todos los tipos</flux:select.option>
                    <flux:select.option value="training">Capacitación</flux:select.option>
                    <flux:select.option value="mobilization">Movilización</flux:select.option>
                    <flux:select.option value="voting">Votación</flux:select.option>
                    <flux:select.option value="meeting">Reunión</flux:select.option>
                    <flux:select.option value="other">Otro</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="statusFilter" class="w-40">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    <flux:select.option value="scheduled">Programados</flux:select.option>
                    <flux:select.option value="in_progress">En Progreso</flux:select.option>
                    <flux:select.option value="completed">Completados</flux:select.option>
                    <flux:select.option value="cancelled">Cancelados</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="estateFilter" class="w-40">
                    <flux:select.option value="">Todos los estados</flux:select.option>
                    @foreach($this->estates as $estate)
                        <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="dateFilter" class="w-40">
                    <flux:select.option value="">Todas las fechas</flux:select.option>
                    <flux:select.option value="today">Hoy</flux:select.option>
                    <flux:select.option value="tomorrow">Mañana</flux:select.option>
                    <flux:select.option value="this_week">Esta semana</flux:select.option>
                    <flux:select.option value="next_week">Próxima semana</flux:select.option>
                    <flux:select.option value="this_month">Este mes</flux:select.option>
                    <flux:select.option value="past">Pasados</flux:select.option>
                    <flux:select.option value="upcoming">Próximos</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="perPage" class="w-24">
                    <flux:select.option value="10">10</flux:select.option>
                    <flux:select.option value="25">25</flux:select.option>
                    <flux:select.option value="50">50</flux:select.option>
                    <flux:select.option value="100">100</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Tabla de Eventos -->
        <div class="overflow-x-auto">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column class="min-w-[200px]" sortable wire:click="sortBy('name')" :direction="$sortField === 'name' ? $sortDirection : null">Nombre</flux:table.column>
                    <flux:table.column class="min-w-[120px]" sortable wire:click="sortBy('type')" :direction="$sortField === 'type' ? $sortDirection : null">Tipo</flux:table.column>
                    <flux:table.column class="min-w-[150px]" sortable wire:click="sortBy('start_date')" :direction="$sortField === 'start_date' ? $sortDirection : null">Fecha</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Ubicación</flux:table.column>
                    <flux:table.column class="min-w-[100px]" sortable wire:click="sortBy('status')" :direction="$sortField === 'status' ? $sortDirection : null">Estado</flux:table.column>
                    <flux:table.column class="min-w-[100px]">Asistencia</flux:table.column>
                    <flux:table.column class="min-w-[100px]">Acciones</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->events as $event)
                        <flux:table.row wire:key="event-row-{{ $event->id }}">
                            <flux:table.cell>
                                <div class="font-medium text-primary-600 dark:text-primary-400">
                                    {{ $event->name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ Str::limit($event->description, 50) }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                @php
                                    $typeLabels = [
                                        'training' => 'Capacitación',
                                        'mobilization' => 'Movilización',
                                        'voting' => 'Votación',
                                        'meeting' => 'Reunión',
                                        'other' => 'Otro'
                                    ];
                                    $typeColors = [
                                        'training' => 'indigo',
                                        'mobilization' => 'purple',
                                        'voting' => 'pink',
                                        'meeting' => 'blue',
                                        'other' => 'gray'
                                    ];
                                @endphp
                                <flux:badge size="sm" color="{{ $typeColors[$event->type] ?? 'gray' }}" inset="top bottom">
                                    {{ $typeLabels[$event->type] ?? 'Desconocido' }}
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="text-sm">
                                    {{ $event->start_date->format('d/m/Y H:i') }}
                                </div>
                                @if($event->end_date)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        hasta {{ $event->end_date->format('d/m/Y H:i') }}
                                    </div>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="text-sm">
                                    {{ $event->location }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $event->estate->name ?? '' }}{{ $event->municipality ? ', ' . $event->municipality->name : '' }}
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                @php
                                    $statusLabels = [
                                        'scheduled' => 'Programado',
                                        'in_progress' => 'En Progreso',
                                        'completed' => 'Completado',
                                        'cancelled' => 'Cancelado'
                                    ];
                                    $statusColors = [
                                        'scheduled' => 'blue',
                                        'in_progress' => 'yellow',
                                        'completed' => 'green',
                                        'cancelled' => 'red'
                                    ];
                                @endphp
                                <flux:badge size="sm" color="{{ $statusColors[$event->status] ?? 'gray' }}" inset="top bottom">
                                    {{ $statusLabels[$event->status] ?? 'Desconocido' }}
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    <span class="font-medium">{{ $event->attendances_count ?? $event->attendances()->count() }}</span>
                                    @if($event->capacity)
                                        <span class="text-gray-500 dark:text-gray-400">/</span>
                                        <span>{{ $event->capacity }}</span>
                                    @endif
                                </div>
                                @if($event->capacity)
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                        <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ min(100, round((($event->attendances_count ?? $event->attendances()->count()) / $event->capacity) * 100)) }}%"></div>
                                    </div>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-1">
                                    <flux:button :href="route('admin.electoral.events.show', $event)" variant="primary" size="xs" icon="eye">Ver</flux:button>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="7" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <x-heroicon-o-calendar class="w-12 h-12 text-gray-400 mb-2" />
                                    <p class="text-gray-500 text-lg font-medium">No se encontraron eventos</p>
                                    <p class="text-gray-400 text-sm">Intenta ajustar los filtros o crea un nuevo evento</p>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-6 flex justify-center sm:justify-end">
            {{ $this->events->links() }}
        </div>
    </flux:card>
</div>
