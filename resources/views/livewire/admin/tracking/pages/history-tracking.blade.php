<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-clock class="size-6 text-primary-500"/>
                {{ __('Historial de Seguimiento') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Historial completo de actividades de seguimiento') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.tracking.index') }}">{{ __('Seguimiento') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Historial') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card class="flex-1">
        <!-- Search and Filters -->
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-between mb-6">
            <div class="flex items-center gap-2 w-full lg:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por nombre, identificación, notas...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            <div class="flex flex-wrap items-center gap-2 w-full lg:w-auto">
                <flux:select wire:model.live="statusFilter" class="w-32">
                    <flux:select.option value="">Todos</flux:select.option>
                    <flux:select.option value="pending">Pendientes</flux:select.option>
                    <flux:select.option value="in_progress">En Progreso</flux:select.option>
                    <flux:select.option value="completed">Completados</flux:select.option>
                    <flux:select.option value="cancelled">Cancelados</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="typeFilter" class="w-32">
                    <flux:select.option value="">Todos</flux:select.option>
                    <flux:select.option value="mobilization">Movilización</flux:select.option>
                    <flux:select.option value="voter_mark">Marcaje</flux:select.option>
                </flux:select>

                <flux:select wire:model.live="selectedPerson" class="w-48">
                    <flux:select.option value="">Todas las personas</flux:select.option>
                    @foreach($this->persons as $person)
                        <flux:select.option value="{{ $person->id }}">{{ $person->name }}</flux:select.option>
                    @endforeach
                </flux:select>

                <flux:select wire:model.live="perPage" class="w-24">
                    <flux:select.option value="10">10</flux:select.option>
                    <flux:select.option value="25">25</flux:select.option>
                    <flux:select.option value="50">50</flux:select.option>
                    <flux:select.option value="100">100</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="flex justify-between mb-6">
            <div class="flex items-center gap-4">
                <flux:button
                    wire:click="clearFilters"
                    variant="primary"
                    size="sm"
                    icon="x-mark"
                >
                    Limpiar Filtros
                </flux:button>
            </div>
            <div class="flex gap-2">
                <flux:button
                    href="{{ route('admin.tracking.reports') }}"
                    icon="chart-bar"
                    class="bg-blue-500 hover:bg-blue-600 text-white"
                >
                    {{ __('Ver Reportes') }}
                </flux:button>
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column class="min-w-[200px]">Persona</flux:table.column>
                    <flux:table.column sortable sorted="{{ $sortField === 'tracking_type' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('tracking_type')" class="min-w-[120px]">Tipo</flux:table.column>
                    <flux:table.column sortable sorted="{{ $sortField === 'status' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('status')" class="min-w-[120px]">Estado</flux:table.column>
                    <flux:table.column sortable sorted="{{ $sortField === 'tracking_date' ? 'true' : 'false' }}" direction="{{ $sortDirection }}" wire:click="sortBy('tracking_date')" class="min-w-[150px]">Fecha</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Ubicación</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Responsable</flux:table.column>
                    <flux:table.column class="min-w-[200px]">Notas</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->trackings as $tracking)
                        <flux:table.row wire:key="tracking-row-{{ $tracking->id }}">
                            <flux:table.cell class="flex items-center gap-3">
                                <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 font-medium shadow-sm">
                                    {{ substr($tracking->person->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium">{{ $tracking->person->name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $tracking->person->cedula }}</div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($tracking->tracking_type === 'mobilization')
                                    <flux:badge size="sm" color="green" inset="top bottom">Movilización</flux:badge>
                                @elseif($tracking->tracking_type === 'voter_mark')
                                    <flux:badge size="sm" color="purple" inset="top bottom">Marcaje</flux:badge>
                                @else
                                    <flux:badge size="sm" color="gray" inset="top bottom">{{ $tracking->tracking_type }}</flux:badge>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($tracking->status === 'completed')
                                    <flux:badge size="sm" color="green" inset="top bottom">Completado</flux:badge>
                                @elseif($tracking->status === 'pending')
                                    <flux:badge size="sm" color="yellow" inset="top bottom">Pendiente</flux:badge>
                                @elseif($tracking->status === 'in_progress')
                                    <flux:badge size="sm" color="blue" inset="top bottom">En Progreso</flux:badge>
                                @elseif($tracking->status === 'cancelled')
                                    <flux:badge size="sm" color="red" inset="top bottom">Cancelado</flux:badge>
                                @else
                                    <flux:badge size="sm" color="gray" inset="top bottom">{{ $tracking->status }}</flux:badge>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $tracking->tracking_date->format('d/m/Y H:i') }}
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $tracking->location ?: 'No especificada' }}
                            </flux:table.cell>
                            <flux:table.cell>
                                {{ $tracking->user->name }}
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="max-w-xs truncate">
                                    {{ $tracking->notes ?: 'Sin notas' }}
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="7" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <x-heroicon-o-clock class="w-12 h-12 text-gray-400 mb-2" />
                                    <p class="text-gray-500 text-lg font-medium">No se encontraron registros de seguimiento</p>
                                    <p class="text-gray-400 text-sm">Intenta ajustar los filtros</p>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-6 flex justify-center sm:justify-end">
            {{ $this->trackings->links() }}
        </div>
    </flux:card>
</div>
