<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-phone class="size-6 text-primary-500"/>
                {{ __('Contactos') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Registro de contactos con personas') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.tracking.index') }}">{{ __('Seguimiento') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Contactos') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card>
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div class="flex items-center gap-2 w-full md:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por nombre, identificación, teléfono o email...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>
            <div class="flex items-center gap-2">
                <flux:button wire:click="openContactForm" variant="primary" icon="phone">
                    {{ __('Registrar Contacto') }}
                </flux:button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <flux:table>
                <flux:table.columns>
                    <flux:table.column class="w-10">
                        <flux:checkbox wire:model.live="selectAll" />
                    </flux:table.column>
                    <flux:table.column>{{ __('Persona') }}</flux:table.column>
                    <flux:table.column>{{ __('Identificación') }}</flux:table.column>
                    <flux:table.column>{{ __('Teléfono') }}</flux:table.column>
                    <flux:table.column>{{ __('Email') }}</flux:table.column>
                    <flux:table.column>{{ __('Último Contacto') }}</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($persons as $person)
                        <flux:table.row wire:key="person-row-{{ $person->id }}">
                            <flux:table.cell>
                                <flux:checkbox wire:model.live="selectedPersons" value="{{ $person->id }}" />
                            </flux:table.cell>
                            <flux:table.cell class="flex items-center gap-3">
                                <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 font-medium shadow-sm">
                                    {{ substr($person->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium">{{ $person->name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        @if($person->is_1x10)
                                            <flux:badge size="xs" color="primary">Líder 1x10</flux:badge>
                                        @endif
                                    </div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                            <flux:table.cell>{{ $person->phone ?: 'No registrado' }}</flux:table.cell>
                            <flux:table.cell>{{ $person->email ?: 'No registrado' }}</flux:table.cell>
                            <flux:table.cell>
                                @php
                                    $lastContact = \App\Models\Tracking::where('person_id', $person->id)
                                        ->where('tracking_type', 'contact')
                                        ->orderBy('tracking_date', 'desc')
                                        ->first();
                                @endphp

                                @if($lastContact)
                                    <div class="flex flex-col">
                                        <span>{{ $lastContact->tracking_date->format('d/m/Y H:i') }}</span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ ucfirst($lastContact->contact_method) }}
                                        </span>
                                    </div>
                                @else
                                    <span class="text-gray-500 dark:text-gray-400">Sin contactos</span>
                                @endif
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="6" class="text-center py-4">
                                <div class="text-gray-500 dark:text-gray-400">
                                    {{ __('No se encontraron personas que coincidan con la búsqueda.') }}
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-4">
            {{ $persons->links() }}
        </div>
    </flux:card>

    <flux:modal wire:model="showContactForm" title="Registrar Contacto" max-width="md">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Registrar Contacto</flux:heading>
                <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                    Estás registrando un contacto con <strong>{{ count($selectedPersons) }}</strong> personas.
                </flux:text>
            </div>

            <flux:select
                wire:model="contactMethod"
                label="Método de Contacto"
                required
            >
                <flux:select.option value="phone">Llamada Telefónica</flux:select.option>
                <flux:select.option value="whatsapp">WhatsApp</flux:select.option>
                <flux:select.option value="email">Email</flux:select.option>
                <flux:select.option value="telegram">Telegram</flux:select.option>
                <flux:select.option value="in_person">En Persona</flux:select.option>
                <flux:select.option value="other">Otro</flux:select.option>
            </flux:select>

            <flux:textarea
                wire:model="notes"
                label="Notas del Contacto"
                placeholder="Ingrese detalles sobre el contacto realizado"
                rows="3"
                required
            />

            <flux:textarea
                wire:model="response"
                label="Respuesta Recibida"
                placeholder="Ingrese la respuesta recibida de la persona contactada"
                rows="3"
            />

            <flux:input
                wire:model="followUpDate"
                label="Fecha de Seguimiento"
                type="datetime-local"
                hint="Deje en blanco si no requiere seguimiento"
            />

            <flux:select
                wire:model="priority"
                label="Prioridad"
                required
            >
                <flux:select.option value="low">Baja</flux:select.option>
                <flux:select.option value="medium">Media</flux:select.option>
                <flux:select.option value="high">Alta</flux:select.option>
            </flux:select>

            <div class="flex justify-end gap-2 mt-6">
                <flux:button wire:click="closeContactForm" variant="primary">
                    Cancelar
                </flux:button>
                <flux:button wire:click="registerContact" variant="primary">
                    Registrar Contacto
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
