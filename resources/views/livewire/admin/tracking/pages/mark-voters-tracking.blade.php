<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-check-circle class="size-6 text-primary-500"/>
                {{ __('Marcar Votantes') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Registro de personas que han ejercido su voto') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.tracking.index') }}">{{ __('Seguimiento') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Marcar Votantes') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <flux:card class="flex-1">
        <!-- Search and Filters -->
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-between mb-6">
            <div class="flex items-center gap-2 w-full lg:w-auto">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('Buscar por nombre, identificación, email...') }}"
                    icon="magnifying-glass"
                    class="w-full md:w-64"
                />
            </div>

            <div class="flex flex-wrap items-center gap-2 w-full lg:w-auto">
                <flux:select wire:model.live="perPage" class="w-24">
                    <flux:select.option value="10">10</flux:select.option>
                    <flux:select.option value="25">25</flux:select.option>
                    <flux:select.option value="50">50</flux:select.option>
                    <flux:select.option value="100">100</flux:select.option>
                </flux:select>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="flex justify-between mb-6">
            <div class="flex items-center gap-4">
                <span class="text-sm text-gray-500">{{ count($selectedPersons) }} personas seleccionadas</span>
            </div>
            <div class="flex gap-2">
                <flux:modal.trigger name="mark-voters-form">
                    <flux:button
                        wire:click="openMarkVotersForm"
                        icon="check-circle"
                        class="bg-purple-500 hover:bg-purple-600 text-white"
                        :disabled="empty($selectedPersons)"
                    >
                        {{ __('Marcar como Votantes') }}
                    </flux:button>
                </flux:modal.trigger>
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
            <flux:table class="min-w-full">
                <flux:table.columns>
                    <flux:table.column class="w-10">
                        <input type="checkbox" wire:model.live="selectAll" id="select-all-checkbox" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                    </flux:table.column>
                    <flux:table.column class="min-w-[200px]">Persona</flux:table.column>
                    <flux:table.column class="min-w-[120px]">Identificación</flux:table.column>
                    <flux:table.column class="min-w-[120px]">Teléfono</flux:table.column>
                    <flux:table.column class="min-w-[120px]">Rol</flux:table.column>
                    <flux:table.column class="min-w-[150px]">Centro de Votación</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($persons as $person)
                        <flux:table.row wire:key="person-row-{{ $person->id }}">
                            <flux:table.cell>
                                <input
                                    type="checkbox"
                                    wire:model.live="selectedPersons"
                                    value="{{ $person->id }}"
                                    id="checkbox-{{ $person->id }}"
                                    class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                                />
                            </flux:table.cell>
                            <flux:table.cell class="flex items-center gap-3">
                                <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 font-medium shadow-sm">
                                    {{ substr($person->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium">{{ $person->name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $person->email ?: 'Sin email' }}</div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                            <flux:table.cell>{{ $person->phone }}</flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" color="{{ $person->role === 'Militant' ? 'green' : 'blue' }}" inset="top bottom">{{ $person->role }}</flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>{{ $person->polling_center ?: 'No especificado' }}</flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="6" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center py-6">
                                    <x-heroicon-o-users class="w-12 h-12 text-gray-400 mb-2" />
                                    <p class="text-gray-500 text-lg font-medium">No se encontraron personas</p>
                                    <p class="text-gray-400 text-sm">Intenta ajustar los filtros</p>
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>
        </div>

        <div class="mt-6 flex justify-center sm:justify-end">
            {{ $persons->links() }}
        </div>
    </flux:card>

    <!-- Modal de Marcaje de Votantes -->

    <flux:modal name="mark-voters-form" class="md:max-w-lg" wire:model="showMarkVotersForm">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Marcar Votantes</flux:heading>
                <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                    Estás marcando a <strong>{{ count($selectedPersons) }}</strong> personas como votantes.
                </flux:text>
            </div>

            <flux:input
                wire:model="location"
                label="Centro de Votación"
                placeholder="Ingrese el centro de votación"
                required
            />

            <flux:textarea
                wire:model="notes"
                label="Notas"
                placeholder="Ingrese notas adicionales sobre el proceso de votación"
                rows="3"
            />

            <div class="flex justify-end space-x-3">
                <flux:button wire:click="cancelMarkVoters" variant="primary">
                    Cancelar
                </flux:button>
                <flux:button wire:click="saveMarkVoters" variant="primary" icon="save">
                    Guardar
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
