<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <x-heroicon-o-chart-bar class="size-6 text-primary-500"/>
                {{ __('Reportes de Seguimiento') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Estadísticas y reportes de actividades de seguimiento') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.tracking.index') }}">{{ __('Seguimiento') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Reportes') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Filters -->
    <flux:card>
        <div class="flex flex-col md:flex-row gap-4 md:items-end md:justify-between">
            <div class="space-y-4">
                <flux:heading size="lg">Filtros de Reporte</flux:heading>
                
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                        <flux:label>Fecha Inicio</flux:label>
                        <flux:input type="date" wire:model.live="dateRange.start" />
                    </div>
                    
                    <div>
                        <flux:label>Fecha Fin</flux:label>
                        <flux:input type="date" wire:model.live="dateRange.end" />
                    </div>
                    
                    <div>
                        <flux:label>Agrupación</flux:label>
                        <flux:select wire:model.live="reportType">
                            <flux:select.option value="daily">Diario</flux:select.option>
                            <flux:select.option value="weekly">Semanal</flux:select.option>
                            <flux:select.option value="monthly">Mensual</flux:select.option>
                        </flux:select>
                    </div>
                </div>
            </div>
            
            <div>
                <flux:select wire:model.live="chartType">
                    <flux:select.option value="bar">Gráfico de Barras</flux:select.option>
                    <flux:select.option value="line">Gráfico de Líneas</flux:select.option>
                    <flux:select.option value="pie">Gráfico Circular</flux:select.option>
                </flux:select>
            </div>
        </div>
    </flux:card>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('Total de Movilizaciones') }}</flux:text>
                <flux:heading size="xl" class="mb-1">{{ $this->mobilizationStats->sum('count') }}</flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.truck variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm font-medium text-green-600 dark:text-green-500">En el período</span>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('Total de Marcajes') }}</flux:text>
                <flux:heading size="xl" class="mb-1">{{ $this->voterMarkStats->sum('count') }}</flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.check-circle variant="micro" class="text-purple-600 dark:text-purple-500" />
                    <span class="text-sm font-medium text-purple-600 dark:text-purple-500">En el período</span>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('Completados') }}</flux:text>
                <flux:heading size="xl" class="mb-1">
                    {{ $this->statusStats->firstWhere('status', 'Completado')['count'] ?? 0 }}
                </flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.check variant="micro" class="text-green-600 dark:text-green-500" />
                    <span class="text-sm font-medium text-green-600 dark:text-green-500">Finalizados</span>
                </div>
            </div>
        </flux:card>

        <flux:card class="flex-1 space-y-4">
            <div>
                <flux:text class="text-gray-600 dark:text-gray-400">{{ __('Pendientes') }}</flux:text>
                <flux:heading size="xl" class="mb-1">
                    {{ $this->statusStats->firstWhere('status', 'Pendiente')['count'] ?? 0 }}
                </flux:heading>
                <div class="flex items-center gap-2">
                    <flux:icon.clock variant="micro" class="text-yellow-600 dark:text-yellow-500" />
                    <span class="text-sm font-medium text-yellow-600 dark:text-yellow-500">En proceso</span>
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Movilización Chart -->
        <flux:card>
            <flux:heading size="lg" class="mb-4">Movilización por {{ $reportType === 'daily' ? 'Día' : ($reportType === 'weekly' ? 'Semana' : 'Mes') }}</flux:heading>
            
            @if($this->mobilizationStats->isEmpty())
                <div class="flex flex-col items-center justify-center py-12">
                    <x-heroicon-o-chart-bar class="w-16 h-16 text-gray-400 mb-4" />
                    <p class="text-lg font-medium text-gray-500">No hay datos disponibles</p>
                    <p class="text-sm text-gray-400">Intenta ajustar el rango de fechas</p>
                </div>
            @else
                <div class="h-80">
                    <!-- Aquí iría el componente de gráfico, por ejemplo con ApexCharts o Chart.js -->
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg h-full flex items-center justify-center">
                        <p class="text-gray-500">Gráfico de Movilización</p>
                    </div>
                </div>
                
                <div class="mt-4 overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Fecha</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cantidad</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                            @foreach($this->mobilizationStats as $stat)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $stat['date'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $stat['count'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </flux:card>

        <!-- Marcaje Chart -->
        <flux:card>
            <flux:heading size="lg" class="mb-4">Marcaje de Votantes por {{ $reportType === 'daily' ? 'Día' : ($reportType === 'weekly' ? 'Semana' : 'Mes') }}</flux:heading>
            
            @if($this->voterMarkStats->isEmpty())
                <div class="flex flex-col items-center justify-center py-12">
                    <x-heroicon-o-chart-bar class="w-16 h-16 text-gray-400 mb-4" />
                    <p class="text-lg font-medium text-gray-500">No hay datos disponibles</p>
                    <p class="text-sm text-gray-400">Intenta ajustar el rango de fechas</p>
                </div>
            @else
                <div class="h-80">
                    <!-- Aquí iría el componente de gráfico, por ejemplo con ApexCharts o Chart.js -->
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg h-full flex items-center justify-center">
                        <p class="text-gray-500">Gráfico de Marcaje de Votantes</p>
                    </div>
                </div>
                
                <div class="mt-4 overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Fecha</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cantidad</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                            @foreach($this->voterMarkStats as $stat)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $stat['date'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $stat['count'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </flux:card>
    </div>

    <!-- Additional Stats -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Status Distribution -->
        <flux:card>
            <flux:heading size="lg" class="mb-4">Distribución por Estado</flux:heading>
            
            @if($this->statusStats->isEmpty())
                <div class="flex flex-col items-center justify-center py-12">
                    <x-heroicon-o-chart-pie class="w-16 h-16 text-gray-400 mb-4" />
                    <p class="text-lg font-medium text-gray-500">No hay datos disponibles</p>
                </div>
            @else
                <div class="h-60">
                    <!-- Aquí iría el componente de gráfico circular -->
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg h-full flex items-center justify-center">
                        <p class="text-gray-500">Gráfico de Estados</p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Estado</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cantidad</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                            @foreach($this->statusStats as $stat)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $stat['status'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $stat['count'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </flux:card>

        <!-- Top Locations -->
        <flux:card>
            <flux:heading size="lg" class="mb-4">Ubicaciones Principales</flux:heading>
            
            @if($this->topLocations->isEmpty())
                <div class="flex flex-col items-center justify-center py-12">
                    <x-heroicon-o-map-pin class="w-16 h-16 text-gray-400 mb-4" />
                    <p class="text-lg font-medium text-gray-500">No hay datos disponibles</p>
                </div>
            @else
                <div class="space-y-4">
                    @foreach($this->topLocations as $location)
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $location->location }}</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $location->count }}</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: {{ ($location->count / $this->topLocations->max('count')) * 100 }}%"></div>
                        </div>
                    @endforeach
                </div>
            @endif
        </flux:card>

        <!-- Top Users -->
        <flux:card>
            <flux:heading size="lg" class="mb-4">Usuarios Más Activos</flux:heading>
            
            @if($this->topUsers->isEmpty())
                <div class="flex flex-col items-center justify-center py-12">
                    <x-heroicon-o-user-group class="w-16 h-16 text-gray-400 mb-4" />
                    <p class="text-lg font-medium text-gray-500">No hay datos disponibles</p>
                </div>
            @else
                <div class="space-y-4">
                    @foreach($this->topUsers as $user)
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $user->name }}</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $user->count }}</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: {{ ($user->count / $this->topUsers->max('count')) * 100 }}%"></div>
                        </div>
                    @endforeach
                </div>
            @endif
        </flux:card>
    </div>
</div>
