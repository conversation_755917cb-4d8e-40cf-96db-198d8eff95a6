<div class="space-y-8 animate-fade-in">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.chart-bar class="h-6 w-6 text-primary-500" />
                Dashboard
            </span>
        </x-slot:title>
        <x-slot:subtitle>
            <div class="text-gray-600 dark:text-gray-400">Bienvenido al sistema de gestión electoral</div>
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">Inicio</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>Dashboard</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>
    </x-page-heading>

    <!-- Estadísticas Generales -->
    <x-dashboard.section title="Resumen General" class="bg-gradient-to-r from-primary-50 to-white dark:from-gray-900 dark:to-gray-800 border border-primary-100 dark:border-primary-900/30 rounded-lg shadow-sm">
        <x-dashboard.stats-grid>
            <x-dashboard.stats-card
                title="Total de Personas"
                value="{{ $stats['total_persons'] }}"
                icon="users"
                color="primary"
                trend="{{ $stats['persons_trend'] > 0 ? 'up' : ($stats['persons_trend'] < 0 ? 'down' : 'neutral') }}"
                trendValue="{{ abs($stats['persons_trend']) }}% este mes"
                href="{{ route('admin.persons.index') }}"
            />

            <x-dashboard.stats-card
                title="Líderes 1x10"
                value="{{ $stats['total_1x10_leaders'] }}"
                icon="user-group"
                color="success"
                trend="{{ $stats['leaders_trend'] > 0 ? 'up' : ($stats['leaders_trend'] < 0 ? 'down' : 'neutral') }}"
                trendValue="{{ abs($stats['leaders_trend']) }}% este mes"
                href="{{ route('admin.persons.assign') }}"
            />

            <x-dashboard.stats-card
                title="Grupos de Patrulla"
                value="{{ $stats['total_groups'] }}"
                icon="users-group"
                color="info"
                trend="{{ $stats['groups_trend'] > 0 ? 'up' : ($stats['groups_trend'] < 0 ? 'down' : 'neutral') }}"
                trendValue="{{ abs($stats['groups_trend']) }}% este mes"
                href="{{ route('admin.patrol.index') }}"
            />

            <x-dashboard.stats-card
                title="Eventos Electorales"
                value="{{ $stats['total_events'] }}"
                icon="calendar"
                color="warning"
                trend="{{ $stats['events_trend'] > 0 ? 'up' : ($stats['events_trend'] < 0 ? 'down' : 'neutral') }}"
                trendValue="{{ abs($stats['events_trend']) }}% este mes"
                href="{{ route('admin.electoral.events') }}"
            />
        </x-dashboard.stats-grid>
    </x-dashboard.section>

    <!-- Actividad Reciente -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 transition-all duration-500 ease-in-out transform hover:translate-y-[-5px]">
        <!-- Próximos Eventos -->
        <x-dashboard.section title="Próximos Eventos" description="Eventos programados para los próximos días" class="bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 border border-blue-100 dark:border-blue-900/30 rounded-lg shadow-sm">
            <x-slot:actions>
                <flux:button :href="route('admin.electoral.events')" variant="outline" size="sm">
                    Ver todos
                </flux:button>
            </x-slot:actions>

            <div class="space-y-4">
                @forelse($upcomingEvents as $event)
                    <x-electoral.event-card :event="$event" :show-actions="false" />
                @empty
                    <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                        No hay eventos próximos programados
                    </div>
                @endforelse
            </div>
        </x-dashboard.section>

        <!-- Seguimientos Recientes -->
        <x-dashboard.section title="Seguimientos Recientes" description="Últimas actividades de seguimiento registradas" class="bg-gradient-to-br from-purple-50 to-white dark:from-gray-900 dark:to-gray-800 border border-purple-100 dark:border-purple-900/30 rounded-lg shadow-sm">
            <x-slot:actions>
                <flux:button :href="route('admin.tracking.index')" variant="outline" size="sm">
                    Ver todos
                </flux:button>
            </x-slot:actions>

            <div class="space-y-4">
                @forelse($recentTrackings as $tracking)
                    <x-tracking.tracking-card :tracking="$tracking" :show-actions="false" />
                @empty
                    <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                        No hay seguimientos recientes
                    </div>
                @endforelse
            </div>
        </x-dashboard.section>
    </div>

    <!-- Estadísticas por Ubicación -->
    <x-dashboard.section title="Distribución por Ubicación" description="Personas registradas por estado y municipio" class="bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800 border border-green-100 dark:border-green-900/30 rounded-lg shadow-sm transition-all duration-500 ease-in-out transform hover:shadow-md">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Estados con más personas -->
            <x-dashboard.chart
                title="Top Estados"
                type="bar"
                :data="$personsByLocation['estates']"
                :options="[
                    'indexAxis' => 'y',
                    'plugins' => ['legend' => ['display' => false]],
                    'scales' => [
                        'x' => ['grid' => ['display' => false]],
                        'y' => ['grid' => ['display' => false]]
                    ]
                ]"
                color="rgba(79, 70, 229, 0.8)"
                label="Personas"
                height="300px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />

            <!-- Municipios con más personas -->
            <x-dashboard.chart
                title="Top Municipios"
                type="bar"
                :data="$personsByLocation['municipalities']"
                :options="[
                    'indexAxis' => 'y',
                    'plugins' => ['legend' => ['display' => false]],
                    'scales' => [
                        'x' => ['grid' => ['display' => false]],
                        'y' => ['grid' => ['display' => false]]
                    ]
                ]"
                color="rgba(16, 185, 129, 0.8)"
                label="Personas"
                height="300px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />
        </div>
    </x-dashboard.section>

    <!-- Estadísticas por Rol -->
    <x-dashboard.section title="Distribución por Rol" description="Personas registradas por rol" class="bg-gradient-to-br from-purple-50 to-white dark:from-gray-900 dark:to-gray-800 border border-purple-100 dark:border-purple-900/30 rounded-lg shadow-sm transition-all duration-500 ease-in-out transform hover:shadow-md">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Distribución por rol -->
            <x-dashboard.chart
                title="Distribución por Rol"
                type="pie"
                :data="$personsByRole"
                :options="[
                    'plugins' => [
                        'legend' => ['position' => 'right'],
                        'tooltip' => ['callbacks' => ['label' => 'function(context) { return context.label + ": " + context.raw; }']]
                    ],
                ]"
                height="300px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />

            <!-- Estadísticas de Personas -->
            <flux:card class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]">
                <div class="p-4">
                    <flux:heading size="sm" class="mb-4">Estadísticas de Personas</flux:heading>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="text-sm font-medium">Total de Personas</div>
                                <div class="text-sm font-bold">{{ $stats['total_persons'] }}</div>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-primary-600 dark:bg-primary-500 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="text-sm font-medium">Líderes 1x10</div>
                                <div class="text-sm font-bold">{{ $stats['total_1x10_leaders'] }}</div>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-600 dark:bg-purple-500 h-2 rounded-full" style="width: {{ ($stats['total_1x10_leaders'] / $stats['total_persons']) * 100 }}%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="text-sm font-medium">Miembros 1x10</div>
                                <div class="text-sm font-bold">{{ $stats['total_persons'] - $stats['total_1x10_leaders'] }}</div>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-green-600 dark:bg-green-500 h-2 rounded-full" style="width: {{ (($stats['total_persons'] - $stats['total_1x10_leaders']) / $stats['total_persons']) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </flux:card>
        </div>
    </x-dashboard.section>

    <!-- Estadísticas Electorales -->
    <x-dashboard.section title="Estadísticas Electorales" description="Eventos y movilizaciones electorales" class="bg-gradient-to-br from-amber-50 to-white dark:from-gray-900 dark:to-gray-800 border border-amber-100 dark:border-amber-900/30 rounded-lg shadow-sm transition-all duration-500 ease-in-out transform hover:shadow-md">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Eventos por estado -->
            <x-dashboard.chart
                title="Eventos por Estado"
                type="doughnut"
                :data="$electoralEventsStats['by_status']"
                :options="[
                    'plugins' => [
                        'legend' => ['position' => 'bottom'],
                        'tooltip' => ['callbacks' => ['label' => 'function(context) { return context.label + ": " + context.raw; }']]
                    ],
                ]"
                height="250px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />

            <!-- Eventos por tipo -->
            <x-dashboard.chart
                title="Eventos por Tipo"
                type="doughnut"
                :data="$electoralEventsStats['by_type']"
                :options="[
                    'plugins' => [
                        'legend' => ['position' => 'bottom'],
                        'tooltip' => ['callbacks' => ['label' => 'function(context) { return context.label + ": " + context.raw; }']]
                    ],
                ]"
                height="250px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />

            <!-- Eventos por mes -->
            <x-dashboard.chart
                title="Eventos por Mes"
                type="line"
                :data="$electoralEventsStats['by_month']"
                :options="[
                    'plugins' => ['legend' => ['display' => false]],
                    'scales' => [
                        'x' => ['grid' => ['display' => false]],
                        'y' => ['grid' => ['display' => true], 'beginAtZero' => true]
                    ]
                ]"
                color="rgba(245, 158, 11, 0.8)"
                label="Eventos"
                height="250px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />
        </div>
    </x-dashboard.section>

    <!-- Estadísticas de Seguimiento -->
    <x-dashboard.section title="Estadísticas de Seguimiento" description="Actividades de seguimiento y movilización" class="bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 border border-blue-100 dark:border-blue-900/30 rounded-lg shadow-sm transition-all duration-500 ease-in-out transform hover:shadow-md">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Seguimientos por tipo -->
            <x-dashboard.chart
                title="Seguimientos por Tipo"
                type="pie"
                :data="$trackingStats['by_type']"
                :options="[
                    'plugins' => [
                        'legend' => ['position' => 'right'],
                        'tooltip' => ['callbacks' => ['label' => 'function(context) { return context.label + ": " + context.raw; }']]
                    ],
                ]"
                height="250px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />

            <!-- Seguimientos por estado -->
            <x-dashboard.chart
                title="Seguimientos por Estado"
                type="pie"
                :data="$trackingStats['by_status']"
                :options="[
                    'plugins' => [
                        'legend' => ['position' => 'right'],
                        'tooltip' => ['callbacks' => ['label' => 'function(context) { return context.label + ": " + context.raw; }']]
                    ],
                ]"
                height="250px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />

            <!-- Seguimientos por día -->
            <x-dashboard.chart
                title="Seguimientos por Día (Últimos 30 días)"
                type="bar"
                :data="$trackingStats['by_day']"
                :options="[
                    'plugins' => ['legend' => ['display' => false]],
                    'scales' => [
                        'x' => ['grid' => ['display' => false]],
                        'y' => ['grid' => ['display' => true], 'beginAtZero' => true]
                    ]
                ]"
                color="rgba(6, 182, 212, 0.8)"
                label="Seguimientos"
                height="250px"
                class="transition-all duration-300 ease-in-out transform hover:shadow-md hover:scale-[1.01]"
            />
        </div>
    </x-dashboard.section>
</div>
