<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.shield-check class="size-8" />
                {{ __('Grupos de Patrullaje') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Gestión de grupos de patrulleros y patrullados') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Grupos de Patrullaje') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            @can('create patrol')
                <flux:button :href="route('admin.patrol.create')" variant="filled">
                    <flux:icon.plus class="size-8" />
                    {{ __('Crear Grupo') }}
                </flux:button>
            @endcan
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-6 overflow-x-auto pb-2">
        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-primary-100 dark:border-primary-900/30">
            <flux:text>Total Grupos</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ $this->statistics['total_groups'] }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['total_groups_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-primary-200 dark:text-primary-400" />
                    <flux:chart.area class="text-primary-100 dark:text-primary-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-green-100 dark:border-green-900/30">
            <flux:text>Grupos Activos</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ $this->statistics['active_groups'] }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['active_groups_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-green-200 dark:text-green-400" />
                    <flux:chart.area class="text-green-100 dark:text-green-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-blue-100 dark:border-blue-900/30">
            <flux:text>Total Miembros</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ $this->statistics['total_members'] }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['total_members_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-blue-200 dark:text-blue-400" />
                    <flux:chart.area class="text-blue-100 dark:text-blue-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-indigo-100 dark:border-indigo-900/30">
            <flux:text>Total Actividades</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ $this->statistics['total_activities'] }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['total_activities_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-indigo-200 dark:text-indigo-400" />
                    <flux:chart.area class="text-indigo-100 dark:text-indigo-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-teal-100 dark:border-teal-900/30">
            <flux:text>Actividades Completadas</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ $this->statistics['completed_activities'] }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['completed_activities_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-teal-200 dark:text-teal-400" />
                    <flux:chart.area class="text-teal-100 dark:text-teal-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>

        <flux:card class="overflow-hidden min-w-[12rem] transition-all duration-300 hover:shadow-md hover:scale-[1.02] border border-amber-100 dark:border-amber-900/30">
            <flux:text>Actividades Próximas</flux:text>
            <flux:heading size="xl" class="mt-2 tabular-nums">{{ $this->statistics['upcoming_activities'] }}</flux:heading>
            <flux:chart class="-mx-8 -mb-8 h-[3rem]" :value="$this->statistics['upcoming_activities_trend']">
                <flux:chart.svg gutter="0">
                    <flux:chart.line class="text-amber-200 dark:text-amber-400" />
                    <flux:chart.area class="text-amber-100 dark:text-amber-400/30" />
                </flux:chart.svg>
            </flux:chart>
        </flux:card>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Lista de Grupos -->
        <div class="col-span-2 space-y-4">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold">Grupos de Patrullaje</h2>
                <div class="flex items-center gap-2">
                    <flux:input wire:model.live.debounce.300ms="search" placeholder="Buscar grupos..." icon="magnifying-glass" />
                    <flux:select wire:model.live="statusFilter">
                        <flux:select.option value="">Todos los estados</flux:select.option>
                        <flux:select.option value="active">Activos</flux:select.option>
                        <flux:select.option value="inactive">Inactivos</flux:select.option>
                    </flux:select>
                    <flux:select wire:model.live="perPage">
                        <flux:select.option value="10">10 por página</flux:select.option>
                        <flux:select.option value="25">25 por página</flux:select.option>
                        <flux:select.option value="50">50 por página</flux:select.option>
                    </flux:select>
                </div>
            </div>

            <flux:table>
                <flux:table.columns>
                    <flux:table.column wire:click="sortBy('name')" sortable :sorted="$sortField === 'name'" :direction="$sortField === 'name' ? $sortDirection : null">Nombre</flux:table.column>
                    <flux:table.column>Líder</flux:table.column>
                    <flux:table.column>Miembros</flux:table.column>
                    <flux:table.column>Ubicación</flux:table.column>
                    <flux:table.column wire:click="sortBy('status')" sortable :sorted="$sortField === 'status'" :direction="$sortField === 'status' ? $sortDirection : null">Estado</flux:table.column>
                    <flux:table.column>Acciones</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @forelse($this->patrolGroups as $group)
                        <flux:table.row wire:key="group-{{ $group->id }}">
                            <flux:table.cell>
                                <div class="font-medium">{{ $group->name }}</div>
                                <div class="text-xs text-gray-500">Creado: {{ $group->created_at->format('d/m/Y') }}</div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-2">
                                    <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                                        {{ substr($group->leader->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <div class="font-medium">{{ $group->leader->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $group->leader->phone }}</div>
                                    </div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-2">
                                    <div class="font-medium">{{ $group->active_members_count ?? 0 }}</div>
                                    <div class="text-xs text-gray-500">/ {{ $group->goal_members }}</div>
                                    <div class="w-16">
                                    </div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="text-sm">
                                    {{ $group->estate?->name ?? 'N/A' }}
                                    @if($group->municipality)
                                        , {{ $group->municipality->name }}
                                    @endif
                                </div>
                                @if($group->zone)
                                    <div class="text-xs text-gray-500">{{ $group->zone }}</div>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($group->status === 'active')
                                    <flux:badge variant="success">Activo</flux:badge>
                                @else
                                    <flux:badge variant="danger">Inactivo</flux:badge>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-2">
                                    <flux:button :href="route('admin.patrol.show', $group)" variant="ghost" size="sm">
                                        <flux:icon.eye class="size-8" />
                                        Ver
                                    </flux:button>
                                    @can('manage patrol members')
                                        <flux:button :href="route('admin.patrol.members', $group)" variant="ghost" size="sm">
                                            <flux:icon.users class="size-8" />
                                            Miembros
                                        </flux:button>
                                    @endcan
                                    @can('manage patrol activities')
                                        <flux:button :href="route('admin.patrol.activities', $group)" variant="ghost" size="sm">
                                            <flux:icon.calendar class="size-8" />
                                            Actividades
                                        </flux:button>
                                    @endcan
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @empty
                        <flux:table.row>
                            <flux:table.cell colspan="6" class="text-center py-4">
                                <div class="flex flex-col items-center justify-center gap-2 py-4">
                                    <flux:icon.shield-exclamation class="size-10" />
                                    <span class="text-gray-500">No se encontraron grupos de patrullaje</span>
                                    @can('create patrol')
                                        <flux:button :href="route('admin.patrol.create')" variant="filled" size="sm" class="mt-2">
                                            Crear Grupo
                                        </flux:button>
                                    @endcan
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforelse
                </flux:table.rows>
            </flux:table>

            <div>
                {{ $this->patrolGroups->links() }}
            </div>
        </div>

        <!-- Actividades Recientes -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">Actividades Recientes</h2>

            <div class="space-y-4">
                @forelse($this->recentActivities as $activity)
                    <flux:card>
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">{{ $activity->title }}</h3>
                                <p class="text-sm text-gray-500">{{ $activity->group->name }}</p>
                            </div>
                            <div>
                                @if($activity->status === 'planned')
                                    <flux:badge variant="info">Planificada</flux:badge>
                                @elseif($activity->status === 'in_progress')
                                    <flux:badge variant="warning">En Progreso</flux:badge>
                                @elseif($activity->status === 'completed')
                                    <flux:badge variant="success">Completada</flux:badge>
                                @else
                                    <flux:badge variant="danger">Cancelada</flux:badge>
                                @endif
                            </div>
                        </div>
                        <div class="mt-2 flex items-center gap-2 text-sm">
                            <flux:icon.calendar class="size-8" />
                            <span>{{ $activity->scheduled_date->format('d/m/Y H:i') }}</span>
                        </div>
                        @if($activity->location)
                            <div class="mt-1 flex items-center gap-2 text-sm">
                                <flux:icon.map-pin class="size-8" />
                                <span>{{ $activity->location }}</span>
                            </div>
                        @endif
                        <div class="mt-3 flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <flux:icon.users class="size-8" />
                                <span class="text-sm">{{ $activity->attendances()->where('attended', true)->count() }} asistentes</span>
                            </div>
                            <flux:button :href="route('admin.patrol.activities', $activity->group)" variant="ghost" size="sm">
                                <flux:icon.arrow-right class="size-8" />
                                Ver
                            </flux:button>
                        </div>
                    </flux:card>
                @empty
                    <flux:card>
                        <div class="flex flex-col items-center justify-center gap-2 py-4">

                            <span class="text-gray-500">No hay actividades recientes</span>
                        </div>
                    </flux:card>
                @endforelse
            </div>

            @can('view patrol reports')
                <div class="mt-6">
                    <flux:button :href="route('admin.patrol.reports')" variant="subtle" class="w-full">
                        <flux:icon.document-chart-bar class="size-8" />
                        Ver Todos los Reportes
                    </flux:button>
                </div>
            @endcan
        </div>
    </div>
</div>
