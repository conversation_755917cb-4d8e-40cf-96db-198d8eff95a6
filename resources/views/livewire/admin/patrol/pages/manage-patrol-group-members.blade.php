<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.users class="size-6 text-primary-500"/>
                {{ __('Gestionar Miembros') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Administrar miembros del grupo de patrullaje') }}: {{ $group->name }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.index') }}">{{ __('Grupos de Patrullaje') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.show', $group) }}">{{ $group->name }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Gestionar Miembros') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <div class="flex items-center gap-2">
                <flux:button wire:click="openAddMembersModal" variant="filled" icon="user-plus">
                    {{ __('Agregar Miembros') }}
                </flux:button>
                <flux:button :href="route('admin.patrol.show', $group)" variant="outline" icon="arrow-left">
                    {{ __('Volver') }}
                </flux:button>
            </div>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Filtros -->
    <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex flex-wrap items-center gap-2">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="Buscar miembros..." icon="magnifying-glass" />

            <flux:select wire:model.live="roleFilter">
                <flux:select.option value="">Todos los roles</flux:select.option>
                <flux:select.option value="leader">Líderes</flux:select.option>
                <flux:select.option value="coordinator">Coordinadores</flux:select.option>
                <flux:select.option value="member">Miembros</flux:select.option>
            </flux:select>

            <flux:select wire:model.live="statusFilter">
                <flux:select.option value="active">Activos</flux:select.option>
                <flux:select.option value="inactive">Inactivos</flux:select.option>
                <flux:select.option value="">Todos</flux:select.option>
            </flux:select>
        </div>

        <flux:select wire:model.live="perPage">
            <flux:select.option value="10">10 por página</flux:select.option>
            <flux:select.option value="25">25 por página</flux:select.option>
            <flux:select.option value="50">50 por página</flux:select.option>
        </flux:select>
    </div>

    <!-- Tabla de miembros -->
    <flux:card>
        <flux:table>
            <flux:table.columns>
                <flux:table.column>Miembro</flux:table.column>
                <flux:table.column>Rol</flux:table.column>
                <flux:table.column>Contacto</flux:table.column>
                <flux:table.column>Fecha de Ingreso</flux:table.column>
                <flux:table.column>Estado</flux:table.column>
                <flux:table.column>Acciones</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @forelse($this->members as $member)
                    <flux:table.row wire:key="member-{{ $member->id }}">
                        <flux:table.cell>
                            <div class="flex items-center gap-2">
                                <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                                    {{ substr($member->person->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="font-medium">{{ $member->person->name }}</div>
                                    <div class="text-xs text-gray-500">CI: {{ $member->person->cedula }}</div>
                                </div>
                            </div>
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($member->role === 'leader')
                                <flux:badge variant="success">Líder</flux:badge>
                            @elseif($member->role === 'coordinator')
                                <flux:badge variant="info">Coordinador</flux:badge>
                            @else
                                <flux:badge variant="primary">Miembro</flux:badge>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="text-sm">{{ $member->person->phone }}</div>
                            @if($member->person->email)
                                <div class="text-xs text-gray-500">{{ $member->person->email }}</div>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $member->joined_date ? $member->joined_date->format('d/m/Y') : 'N/A' }}
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($member->status === 'active')
                                <flux:badge variant="success">Activo</flux:badge>
                            @else
                                <flux:badge variant="danger">Inactivo</flux:badge>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="flex items-center gap-2">
                                <flux:button wire:click="openEditMemberModal({{ $member->id }})" variant="ghost" size="sm">
                                    <flux:icon.pencil class="size-8" />
                                    Editar
                                </flux:button>

                                @if($member->role !== 'leader')
                                    <flux:button
                                        wire:click="removeMember({{ $member->id }})"
                                        wire:confirm="¿Está seguro de remover a este miembro del grupo?"
                                        variant="ghost"
                                        size="sm"
                                    >
                                        <flux:icon.x-mark class="size-8" />
                                        Remover
                                    </flux:button>
                                @endif

                                <flux:button :href="route('admin.persons.show', $member->person)" variant="ghost" size="sm">
                                    <flux:icon.eye class="size-8" />
                                    Ver
                                </flux:button>
                            </div>
                        </flux:table.cell>
                    </flux:table.row>
                @empty
                    <flux:table.row>
                        <flux:table.cell colspan="6" class="text-center py-4">
                            <div class="flex flex-col items-center justify-center gap-2 py-4">
                                <flux:icon.users class="size-8 text-gray-400" />
                                <span class="text-gray-500">No se encontraron miembros</span>
                                <flux:button wire:click="openAddMembersModal" variant="filled" size="sm" class="mt-2">
                                    Agregar Miembros
                                </flux:button>
                            </div>
                        </flux:table.cell>
                    </flux:table.row>
                @endforelse
            </flux:table.rows>
        </flux:table>

        <div class="mt-4">
            {{ $this->members->links() }}
        </div>
    </flux:card>

    <!-- Modal para agregar miembros -->
    <flux:modal.trigger name="add-members-modal">
        <span></span>
    </flux:modal.trigger>
    <flux:modal name="add-members-modal" wire:model="showAddMembersModal">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Agregar Miembros</flux:heading>
            </div>

            <div class="space-y-4">
                <flux:input wire:model.live.debounce.300ms="searchPerson" placeholder="Buscar por nombre o cédula..." icon="magnifying-glass" />

                @if($searchPerson && count($this->potentialMembers) > 0)
                    <div class="max-h-60 overflow-y-auto rounded-md border border-gray-200">
                        <ul class="divide-y divide-gray-200">
                            @foreach($this->potentialMembers as $person)
                                <li wire:key="potential-{{ $person->id }}" class="flex items-center justify-between p-3 hover:bg-gray-50">
                                    <div class="flex items-center gap-2">
                                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                                            {{ substr($person->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $person->name }}</div>
                                            <div class="text-xs text-gray-500">CI: {{ $person->cedula }} | {{ $person->phone }}</div>
                                        </div>
                                    </div>
                                    <flux:checkbox wire:click="togglePersonSelection({{ $person->id }})" :checked="in_array($person->id, $selectedPersons)" />
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @elseif($searchPerson && count($this->potentialMembers) === 0)
                    <div class="rounded-md border border-gray-200 p-4 text-center">
                        <p class="text-gray-500">No se encontraron personas disponibles</p>
                        <p class="mt-1 text-xs text-gray-400">Las personas ya pueden ser miembros de otro grupo o no estar registradas</p>
                    </div>
                @else
                    <div class="rounded-md border border-gray-200 p-4 text-center">
                        <p class="text-gray-500">Busca personas para agregarlas al grupo</p>
                    </div>
                @endif

                @if(count($selectedPersons) > 0)
                    <div class="mt-4 rounded-md border border-gray-200 p-3">
                        <h4 class="mb-2 font-medium">Personas seleccionadas ({{ count($selectedPersons) }})</h4>
                        <div class="flex flex-wrap gap-2">
                            @foreach($selectedPersons as $personId)
                                @php
                                    $person = $this->potentialMembers->firstWhere('id', $personId);
                                @endphp
                                @if($person)
                                    <flux:badge variant="primary">
                                        {{ $person->name }}
                                        <button wire:click="togglePersonSelection({{ $person->id }})" class="ml-1">
                                            <flux:icon.x-mark class="size-3" />
                                        </button>
                                    </flux:badge>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <div class="flex justify-end gap-2">
                <flux:button wire:click="closeAddMembersModal" variant="outline">Cancelar</flux:button>
                <flux:button wire:click="addMembers" variant="filled" :disabled="count($selectedPersons) === 0">Agregar Seleccionados</flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Modal para editar miembro -->
    <flux:modal.trigger name="edit-member-modal">
        <span></span>
    </flux:modal.trigger>
    <flux:modal name="edit-member-modal" wire:model="showEditMemberModal">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Editar Miembro</flux:heading>
            </div>

            @if($editingMember)
                <div class="mb-4 flex items-center gap-2">
                    <div class="flex h-10 w-10 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                        {{ substr($editingMember->person->name, 0, 1) }}
                    </div>
                    <div>
                        <div class="font-medium">{{ $editingMember->person->name }}</div>
                        <div class="text-xs text-gray-500">CI: {{ $editingMember->person->cedula }} | {{ $editingMember->person->phone }}</div>
                    </div>
                </div>

                <div class="space-y-4">
                    <flux:input.group label="Rol" for="memberRole" error="{{ $errors->first('memberRole') }}">
                        <flux:select wire:model="memberRole" id="memberRole">
                            <flux:select.option value="leader">Líder</flux:select.option>
                            <flux:select.option value="coordinator">Coordinador</flux:select.option>
                            <flux:select.option value="member">Miembro</flux:select.option>
                        </flux:select>
                    </flux:input.group>

                    <flux:input.group label="Estado" for="memberStatus" error="{{ $errors->first('memberStatus') }}">
                        <flux:select wire:model="memberStatus" id="memberStatus">
                            <flux:select.option value="active">Activo</flux:select.option>
                            <flux:select.option value="inactive">Inactivo</flux:select.option>
                        </flux:select>
                    </flux:input.group>

                    <flux:input.group label="Notas" for="memberNotes" error="{{ $errors->first('memberNotes') }}">
                        <flux:textarea wire:model="memberNotes" id="memberNotes" rows="3" placeholder="Notas adicionales sobre este miembro" />
                    </flux:input.group>
                </div>
            @endif

            <div class="flex justify-end gap-2">
                <flux:button wire:click="closeEditMemberModal" variant="outline">Cancelar</flux:button>
                <flux:button wire:click="updateMember" variant="filled">Guardar Cambios</flux:button>
            </div>
        </div>
    </flux:modal>
</div>
