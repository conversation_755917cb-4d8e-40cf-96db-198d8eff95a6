<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.shield-check class="size-6 text-primary-500"/>
                {{ $group->name }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Detalles del grupo de patrullaje') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.index') }}">{{ __('Grupos de Patrullaje') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ $group->name }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <div class="flex items-center gap-2">
                @if($group->status === 'active')
                    <flux:button wire:click="deactivateGroup" wire:confirm="¿Está seguro de desactivar este grupo?" variant="outline" icon="x-mark">
                        {{ __('Desactivar') }}
                    </flux:button>
                @else
                    <flux:button wire:click="activateGroup" variant="outline" icon="check">
                        {{ __('Activar') }}
                    </flux:button>
                @endif

                <flux:button :href="route('admin.patrol.index')" variant="outline" icon="arrow-left">
                    {{ __('Volver') }}
                </flux:button>
            </div>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estado del grupo -->
    <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center gap-4">
            <div class="flex h-12 w-12 items-center justify-center rounded-full bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300">
                <flux:icon.shield-check class="size-6" />
            </div>
            <div>
                <h2 class="text-xl font-semibold">{{ $group->name }}</h2>
                <p class="text-sm text-gray-500">{{ $group->description }}</p>
            </div>
        </div>
        <div>
            @if($group->status === 'active')
                <flux:badge variant="success">Activo</flux:badge>
            @else
                <flux:badge variant="danger">Inactivo</flux:badge>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Información del grupo -->
        <flux:card>
            <h3 class="mb-4 text-lg font-medium">Información del Grupo</h3>

            <div class="space-y-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Líder</h4>
                    <div class="mt-1 flex items-center gap-2">
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                            {{ substr($group->leader->name, 0, 1) }}
                        </div>
                        <div>
                            <div class="font-medium">{{ $group->leader->name }}</div>
                            <div class="text-xs text-gray-500">CI: {{ $group->leader->cedula }} | {{ $group->leader->phone }}</div>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">Ubicación</h4>
                    <p class="mt-1">
                        {{ $group->estate?->name ?? 'N/A' }}
                        @if($group->municipality)
                            , {{ $group->municipality->name }}
                        @endif
                        @if($group->parish)
                            , {{ $group->parish->name }}
                        @endif
                    </p>
                    @if($group->zone || $group->sector)
                        <p class="text-sm text-gray-500">
                            @if($group->zone)
                                Zona: {{ $group->zone }}
                            @endif
                            @if($group->sector)
                                @if($group->zone), @endif
                                Sector: {{ $group->sector }}
                            @endif
                        </p>
                    @endif
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">Fechas</h4>
                    <p class="mt-1">
                        <span class="font-medium">Inicio:</span> {{ $group->start_date ? $group->start_date->format('d/m/Y') : 'No definida' }}
                    </p>
                    <p class="text-sm">
                        <span class="font-medium">Fin:</span> {{ $group->end_date ? $group->end_date->format('d/m/Y') : 'No definida' }}
                    </p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">Metas</h4>
                    <div class="mt-1 grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm">
                                <span class="font-medium">Miembros:</span> {{ $this->memberStatistics['active'] }} / {{ $group->goal_members }}
                            </p>
                        </div>
                        <div>
                            <p class="text-sm">
                                <span class="font-medium">Actividades:</span> {{ $this->activityStatistics['completed'] }} / {{ $group->goal_activities }}
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500">Creación</h4>
                    <p class="mt-1 text-sm">
                        Creado el {{ $group->created_at->format('d/m/Y H:i') }}
                    </p>
                    <p class="text-sm text-gray-500">
                        Última actualización: {{ $group->updated_at->format('d/m/Y H:i') }}
                    </p>
                </div>
            </div>

            <div class="mt-6 flex items-center justify-between">
                @can('manage patrol members')
                    <flux:button :href="route('admin.patrol.members', $group)" variant="outline" icon="users">
                        Gestionar Miembros
                    </flux:button>
                @endcan
                @can('manage patrol activities')
                    <flux:button :href="route('admin.patrol.activities', $group)" variant="outline" icon="calendar">
                        Gestionar Actividades
                    </flux:button>
                @endcan
            </div>
        </flux:card>

        <!-- Miembros -->
        <flux:card>
            <div class="mb-4 flex items-center justify-between">
                <h3 class="text-lg font-medium">Miembros</h3>
                <flux:badge>{{ $this->totalMembers }} miembros</flux:badge>
            </div>

            <div class="space-y-4">
                @forelse($this->members as $member)
                    <div class="flex items-center justify-between rounded-md border border-gray-200 p-2">
                        <div class="flex items-center gap-2">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                                {{ substr($member->person->name, 0, 1) }}
                            </div>
                            <div>
                                <div class="font-medium">{{ $member->person->name }}</div>
                                <div class="text-xs text-gray-500">
                                    @if($member->role === 'leader')
                                        <flux:badge variant="success" size="xs">Líder</flux:badge>
                                    @elseif($member->role === 'coordinator')
                                        <flux:badge variant="info" size="xs">Coordinador</flux:badge>
                                    @else
                                        <flux:badge variant="primary" size="xs">Miembro</flux:badge>
                                    @endif
                                    {{ $member->person->phone }}
                                </div>
                            </div>
                        </div>
                        <div>
                            <flux:button :href="route('admin.persons.show', $member->person)" variant="ghost" size="sm">
                                <flux:icon.eye class="size-8" />
                                Ver
                            </flux:button>
                        </div>
                    </div>
                @empty
                    <div class="flex flex-col items-center justify-center gap-2 py-4">
                        <flux:icon.users class="size-8 text-gray-400" />
                        <span class="text-gray-500">No hay miembros registrados</span>
                    </div>
                @endforelse
            </div>

            @if($this->totalMembers > count($this->members))
                <div class="mt-4 text-center">
                    <flux:button :href="route('admin.patrol.members', $group)" variant="link" size="sm">
                        Ver todos los miembros ({{ $this->totalMembers }})
                    </flux:button>
                </div>
            @endif

            <div class="mt-6">
                <h4 class="mb-2 text-sm font-medium text-gray-500">Estadísticas de Miembros</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm">
                            <span class="font-medium">Activos:</span> {{ $this->memberStatistics['active'] }}
                        </p>
                        <p class="text-sm">
                            <span class="font-medium">Inactivos:</span> {{ $this->memberStatistics['inactive'] }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm">
                            <span class="font-medium">Líderes:</span> {{ $this->memberStatistics['leaders'] }}
                        </p>
                        <p class="text-sm">
                            <span class="font-medium">Coordinadores:</span> {{ $this->memberStatistics['coordinators'] }}
                        </p>
                    </div>
                </div>
            </div>
        </flux:card>

        <!-- Actividades -->
        <flux:card>
            <div class="mb-4 flex items-center justify-between">
                <h3 class="text-lg font-medium">Actividades Recientes</h3>
                <flux:badge>{{ $this->activityStatistics['total'] }} actividades</flux:badge>
            </div>

            <div class="space-y-4">
                @forelse($this->recentActivities as $activity)
                    <div class="rounded-md border border-gray-200 p-3">
                        <div class="flex items-start justify-between">
                            <div>
                                <h4 class="font-medium">{{ $activity->title }}</h4>
                                <p class="text-xs text-gray-500">{{ $activity->type }}</p>
                            </div>
                            <div>
                                @if($activity->status === 'planned')
                                    <flux:badge variant="info">Planificada</flux:badge>
                                @elseif($activity->status === 'in_progress')
                                    <flux:badge variant="warning">En Progreso</flux:badge>
                                @elseif($activity->status === 'completed')
                                    <flux:badge variant="success">Completada</flux:badge>
                                @else
                                    <flux:badge variant="danger">Cancelada</flux:badge>
                                @endif
                            </div>
                        </div>
                        <div class="mt-2 flex items-center gap-2 text-sm">
                            <flux:icon.calendar class="size-4 text-gray-500" />
                            <span>{{ $activity->scheduled_date->format('d/m/Y H:i') }}</span>
                        </div>
                        @if($activity->location)
                            <div class="mt-1 flex items-center gap-2 text-sm">
                                <flux:icon.map-pin class="size-4 text-gray-500" />
                                <span>{{ $activity->location }}</span>
                            </div>
                        @endif
                    </div>
                @empty
                    <div class="flex flex-col items-center justify-center gap-2 py-4">
                        <flux:icon.calendar class="size-8 text-gray-400" />
                        <span class="text-gray-500">No hay actividades registradas</span>
                    </div>
                @endforelse
            </div>

            <div class="mt-6">
                <h4 class="mb-2 text-sm font-medium text-gray-500">Próximas Actividades</h4>
                @forelse($this->upcomingActivities as $activity)
                    <div class="mb-2 flex items-center justify-between rounded-md border border-gray-200 p-2">
                        <div>
                            <div class="font-medium">{{ $activity->title }}</div>
                            <div class="text-xs text-gray-500">{{ $activity->scheduled_date->format('d/m/Y H:i') }}</div>
                        </div>
                        <flux:badge variant="{{ $activity->status === 'planned' ? 'info' : 'warning' }}">
                            {{ $activity->status === 'planned' ? 'Planificada' : 'En Progreso' }}
                        </flux:badge>
                    </div>
                @empty
                    <p class="text-sm text-gray-500">No hay actividades próximas</p>
                @endforelse

                <div class="mt-4">
                    <flux:button :href="route('admin.patrol.activities', $group)" variant="outline" class="w-full" icon="calendar">
                        Ver Todas las Actividades
                    </flux:button>
                </div>
            </div>
        </flux:card>
    </div>
</div>
