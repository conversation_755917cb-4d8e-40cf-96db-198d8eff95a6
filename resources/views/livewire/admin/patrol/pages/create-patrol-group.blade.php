<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.shield-check class="size-6 text-primary-500"/>
                {{ __('Crear Grupo de Patrullaje') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Registrar un nuevo grupo de patrulleros y patrullados') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.index') }}">{{ __('Grupos de Patrullaje') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Crear Grupo') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <flux:button :href="route('admin.patrol.index')" icon="arrow-left" variant="outline">
                {{ __('Volver') }}
            </flux:button>
        </x-slot:buttons>
    </x-page-heading>

    <flux:card>
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Información básica -->
                <div class="space-y-6">
                    <h3 class="text-lg font-medium">Información Básica</h3>

                    <flux:input.group label="Nombre del Grupo" required for="name" error="{{ $errors->first('name') }}">
                        <flux:input wire:model="name" id="name" placeholder="Ingrese el nombre del grupo" />
                    </flux:input.group>

                    <flux:input.group label="Descripción" for="description" error="{{ $errors->first('description') }}">
                        <flux:textarea wire:model="description" id="description" placeholder="Descripción del grupo y sus objetivos" rows="3" />
                    </flux:input.group>

                    <flux:input.group label="Estado" required for="status" error="{{ $errors->first('status') }}">
                        <flux:select wire:model="status" id="status">
                            <flux:select.option value="active">Activo</flux:select.option>
                            <flux:select.option value="inactive">Inactivo</flux:select.option>
                        </flux:select>
                    </flux:input.group>

                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <flux:input.group label="Meta de Miembros" for="goal_members" error="{{ $errors->first('goal_members') }}">
                            <flux:input wire:model="goal_members" id="goal_members" type="number" min="1" />
                        </flux:input.group>

                        <flux:input.group label="Meta de Actividades" for="goal_activities" error="{{ $errors->first('goal_activities') }}">
                            <flux:input wire:model="goal_activities" id="goal_activities" type="number" min="1" />
                        </flux:input.group>
                    </div>

                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <flux:input.group label="Fecha de Inicio" for="start_date" error="{{ $errors->first('start_date') }}">
                            <flux:input wire:model="start_date" id="start_date" type="date" />
                        </flux:input.group>

                        <flux:input.group label="Fecha de Fin" for="end_date" error="{{ $errors->first('end_date') }}">
                            <flux:input wire:model="end_date" id="end_date" type="date" />
                        </flux:input.group>
                    </div>
                </div>

                <!-- Ubicación y Líder -->
                <div class="space-y-6">
                    <h3 class="text-lg font-medium">Ubicación y Líder</h3>

                    <flux:input.group label="Líder del Grupo" required for="leader_id" error="{{ $errors->first('leader_id') }}">
                        <div class="space-y-2">
                            @if($selectedPerson)
                                <div class="flex items-center justify-between rounded-md border border-gray-300 p-2">
                                    <div class="flex items-center gap-2">
                                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                                            {{ substr($selectedPerson->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $selectedPerson->name }}</div>
                                            <div class="text-xs text-gray-500">CI: {{ $selectedPerson->cedula }} | {{ $selectedPerson->phone }}</div>
                                        </div>
                                    </div>
                                    <flux:button wire:click="clearSelectedPerson" variant="ghost" size="sm">
                                        <flux:icon.x-mark class="size-4" />
                                    </flux:button>
                                </div>
                            @else
                                <div class="relative">
                                    <flux:input wire:model.live.debounce.300ms="searchPerson" placeholder="Buscar por nombre o cédula..." icon="magnifying-glass" />

                                    @if($searchPerson && count($this->searchResults) > 0)
                                        <div class="absolute z-10 mt-1 w-full rounded-md border border-gray-300 bg-white shadow-lg">
                                            <ul class="max-h-60 overflow-auto py-1">
                                                @foreach($this->searchResults as $person)
                                                    <li wire:key="person-{{ $person->id }}" wire:click="selectPerson({{ $person->id }})" class="cursor-pointer px-4 py-2 hover:bg-gray-100">
                                                        <div class="font-medium">{{ $person->name }}</div>
                                                        <div class="text-xs text-gray-500">CI: {{ $person->cedula }} | {{ $person->phone }}</div>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @elseif($searchPerson && count($this->searchResults) === 0)
                                        <div class="absolute z-10 mt-1 w-full rounded-md border border-gray-300 bg-white p-4 shadow-lg">
                                            <p class="text-center text-gray-500">No se encontraron resultados</p>
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </flux:input.group>

                    <flux:input.group label="Estado" for="estate_id" error="{{ $errors->first('estate_id') }}">
                        <flux:select wire:model.live="estate_id" id="estate_id">
                            <flux:select.option value="">Seleccione un estado</flux:select.option>
                            @foreach($this->estates as $estate)
                                <flux:select.option value="{{ $estate->id }}">{{ $estate->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:input.group>

                    <flux:input.group label="Municipio" for="municipality_id" error="{{ $errors->first('municipality_id') }}">
                        <flux:select wire:model.live="municipality_id" id="municipality_id" :disabled="!$estate_id">
                            <flux:select.option value="">Seleccione un municipio</flux:select.option>
                            @foreach($this->municipalities as $municipality)
                                <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:input.group>

                    <flux:input.group label="Parroquia" for="parish_id" error="{{ $errors->first('parish_id') }}">
                        <flux:select wire:model="parish_id" id="parish_id" :disabled="!$municipality_id">
                            <flux:select.option value="">Seleccione una parroquia</flux:select.option>
                            @foreach($this->parishes as $parish)
                                <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:input.group>

                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <flux:input.group label="Zona" for="zone" error="{{ $errors->first('zone') }}">
                            <flux:input wire:model="zone" id="zone" placeholder="Zona o sector" />
                        </flux:input.group>

                        <flux:input.group label="Sector" for="sector" error="{{ $errors->first('sector') }}">
                            <flux:input wire:model="sector" id="sector" placeholder="Sector específico" />
                        </flux:input.group>
                    </div>
                </div>
            </div>

            <div class="flex justify-end gap-2 pt-4">
                <flux:button :href="route('admin.patrol.index')" variant="outline">Cancelar</flux:button>
                <flux:button type="submit" variant="filled">Guardar Grupo</flux:button>
            </div>
        </form>
    </flux:card>
</div>
