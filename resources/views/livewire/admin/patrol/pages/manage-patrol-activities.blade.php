<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.calendar class="size-6 text-primary-500"/>
                {{ __('Gestionar Actividades') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Administrar actividades del grupo de patrullaje') }}: {{ $group->name }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.index') }}">{{ __('Grupos de Patrullaje') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.show', $group) }}">{{ $group->name }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Gestionar Actividades') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <div class="flex items-center gap-2">
                <flux:button wire:click="openActivityModal" variant="filled" icon="plus">
                    {{ __('Nueva Actividad') }}
                </flux:button>
                <flux:button :href="route('admin.patrol.show', $group)" variant="outline" icon="arrow-left">
                    {{ __('Volver') }}
                </flux:button>
            </div>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-6">
        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Total</flux:stat.heading>
                <flux:stat.number>{{ $this->activityStatistics['total'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.calendar class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Planificadas</flux:stat.heading>
                <flux:stat.number>{{ $this->activityStatistics['planned'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.clipboard-document class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>En Progreso</flux:stat.heading>
                <flux:stat.number>{{ $this->activityStatistics['in_progress'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.play class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Completadas</flux:stat.heading>
                <flux:stat.number>{{ $this->activityStatistics['completed'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.check-badge class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Canceladas</flux:stat.heading>
                <flux:stat.number>{{ $this->activityStatistics['cancelled'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.x-mark class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Patrullajes</flux:stat.heading>
                <flux:stat.number>{{ $this->activityStatistics['patrol'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.shield-check class="size-5" />
            </flux:stat.icon>
        </flux:stat>
    </div>

    <!-- Filtros -->
    <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex flex-wrap items-center gap-2">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="Buscar actividades..." icon="magnifying-glass" />

            <flux:select wire:model.live="typeFilter">
                <flux:select.option value="">Todos los tipos</flux:select.option>
                <flux:select.option value="patrol">Patrullaje</flux:select.option>
                <flux:select.option value="meeting">Reunión</flux:select.option>
                <flux:select.option value="training">Capacitación</flux:select.option>
                <flux:select.option value="other">Otro</flux:select.option>
            </flux:select>

            <flux:select wire:model.live="statusFilter">
                <flux:select.option value="">Todos los estados</flux:select.option>
                <flux:select.option value="planned">Planificadas</flux:select.option>
                <flux:select.option value="in_progress">En Progreso</flux:select.option>
                <flux:select.option value="completed">Completadas</flux:select.option>
                <flux:select.option value="cancelled">Canceladas</flux:select.option>
            </flux:select>
        </div>

        <flux:select wire:model.live="perPage">
            <flux:select.option value="10">10 por página</flux:select.option>
            <flux:select.option value="25">25 por página</flux:select.option>
            <flux:select.option value="50">50 por página</flux:select.option>
        </flux:select>
    </div>

    <!-- Tabla de actividades -->
    <flux:card>
        <flux:table>
            <flux:table.columns>
                <flux:table.column>Actividad</flux:table.column>
                <flux:table.column>Tipo</flux:table.column>
                <flux:table.column>Fecha</flux:table.column>
                <flux:table.column>Ubicación</flux:table.column>
                <flux:table.column>Estado</flux:table.column>
                <flux:table.column>Asistencia</flux:table.column>
                <flux:table.column>Acciones</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @forelse($this->activities as $activity)
                    <flux:table.row wire:key="activity-{{ $activity->id }}">
                        <flux:table.cell>
                            <div class="font-medium">{{ $activity->title }}</div>
                            @if($activity->description)
                                <div class="text-xs text-gray-500">{{ Str::limit($activity->description, 50) }}</div>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($activity->type === 'patrol')
                                <flux:badge variant="primary">Patrullaje</flux:badge>
                            @elseif($activity->type === 'meeting')
                                <flux:badge variant="primary">Reunión</flux:badge>
                            @elseif($activity->type === 'training')
                                <flux:badge variant="info">Capacitación</flux:badge>
                            @else
                                <flux:badge variant="default">Otro</flux:badge>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="text-sm">{{ $activity->scheduled_date->format('d/m/Y') }}</div>
                            <div class="text-xs text-gray-500">{{ $activity->scheduled_date->format('H:i') }}</div>
                        </flux:table.cell>
                        <flux:table.cell>
                            {{ $activity->location ?: 'N/A' }}
                        </flux:table.cell>
                        <flux:table.cell>
                            @if($activity->status === 'planned')
                                <flux:badge variant="info">Planificada</flux:badge>
                            @elseif($activity->status === 'in_progress')
                                <flux:badge variant="warning">En Progreso</flux:badge>
                            @elseif($activity->status === 'completed')
                                <flux:badge variant="success">Completada</flux:badge>
                            @else
                                <flux:badge variant="danger">Cancelada</flux:badge>
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="flex items-center gap-2">
                                <div class="text-sm">{{ $activity->attendances()->where('attended', true)->count() }}/{{ $activity->attendances()->count() }}</div>
                                <div class="w-16">
                                    <flux:progress :value="$activity->attendance_rate" />
                                </div>
                            </div>
                        </flux:table.cell>
                        <flux:table.cell>
                            <div class="flex items-center gap-2">
                                <flux:button wire:click="openActivityModal({{ $activity->id }})" variant="ghost" size="sm">
                                    <flux:icon.pencil class="size-8" />
                                    Editar
                                </flux:button>

                                <flux:button wire:click="openAttendanceModal({{ $activity->id }})" variant="ghost" size="sm">
                                    <flux:icon.users class="size-8" />
                                    Asistencia
                                </flux:button>

                                @if($activity->status !== 'cancelled' && $activity->status !== 'completed')
                                    <flux:button
                                        wire:click="deleteActivity({{ $activity->id }})"
                                        wire:confirm="¿Está seguro de cancelar esta actividad?"
                                        variant="ghost"
                                        size="sm"
                                    >
                                        <flux:icon.x-mark class="size-8" />
                                        Cancelar
                                    </flux:button>
                                @endif
                            </div>
                        </flux:table.cell>
                    </flux:table.row>
                @empty
                    <flux:table.row>
                        <flux:table.cell colspan="7" class="text-center py-4">
                            <div class="flex flex-col items-center justify-center gap-2 py-4">
                                <flux:icon.calendar class="size-8 text-gray-400" />
                                <span class="text-gray-500">No se encontraron actividades</span>
                                <flux:button wire:click="openActivityModal" variant="filled" size="sm" class="mt-2">
                                    Nueva Actividad
                                </flux:button>
                            </div>
                        </flux:table.cell>
                    </flux:table.row>
                @endforelse
            </flux:table.rows>
        </flux:table>

        <div class="mt-4">
            {{ $this->activities->links() }}
        </div>
    </flux:card>

    <!-- Modal para crear/editar actividad -->
    <flux:modal.trigger name="activity-modal">
        <span></span>
    </flux:modal.trigger>
    <flux:modal name="activity-modal" wire:model="showActivityModal">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ $editingActivity ? 'Editar Actividad' : 'Nueva Actividad' }}</flux:heading>
            </div>

            <div class="space-y-4">
                <flux:input.group label="Título" required for="title" error="{{ $errors->first('title') }}">
                    <flux:input wire:model="title" id="title" placeholder="Título de la actividad" />
                </flux:input.group>

                <flux:input.group label="Descripción" for="description" error="{{ $errors->first('description') }}">
                    <flux:textarea wire:model="description" id="description" rows="3" placeholder="Descripción de la actividad" />
                </flux:input.group>

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <flux:input.group label="Tipo" required for="type" error="{{ $errors->first('type') }}">
                        <flux:select wire:model="type" id="type">
                            <flux:select.option value="patrol">Patrullaje</flux:select.option>
                            <flux:select.option value="meeting">Reunión</flux:select.option>
                            <flux:select.option value="training">Capacitación</flux:select.option>
                            <flux:select.option value="other">Otro</flux:select.option>
                        </flux:select>
                    </flux:input.group>

                    <flux:input.group label="Estado" required for="status" error="{{ $errors->first('status') }}">
                        <flux:select wire:model="status" id="status">
                            <flux:select.option value="planned">Planificada</flux:select.option>
                            <flux:select.option value="in_progress">En Progreso</flux:select.option>
                            <flux:select.option value="completed">Completada</flux:select.option>
                            <flux:select.option value="cancelled">Cancelada</flux:select.option>
                        </flux:select>
                    </flux:input.group>
                </div>

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <flux:input.group label="Fecha y Hora de Inicio" required for="scheduled_date" error="{{ $errors->first('scheduled_date') }}">
                        <flux:input wire:model="scheduled_date" id="scheduled_date" type="datetime-local" />
                    </flux:input.group>

                    <flux:input.group label="Fecha y Hora de Fin" for="end_date" error="{{ $errors->first('end_date') }}">
                        <flux:input wire:model="end_date" id="end_date" type="datetime-local" />
                    </flux:input.group>
                </div>

                <flux:input.group label="Ubicación" for="location" error="{{ $errors->first('location') }}">
                    <flux:input wire:model="location" id="location" placeholder="Ubicación de la actividad" />
                </flux:input.group>

                <flux:input.group label="Notas Adicionales" for="notes" error="{{ $errors->first('notes') }}">
                    <flux:textarea wire:model="notes" id="notes" rows="2" placeholder="Notas adicionales sobre la actividad" />
                </flux:input.group>
            </div>

            <div class="flex justify-end gap-2">
                <flux:button wire:click="closeActivityModal" variant="outline">Cancelar</flux:button>
                <flux:button wire:click="saveActivity" variant="filled">{{ $editingActivity ? 'Guardar Cambios' : 'Crear Actividad' }}</flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Modal para gestionar asistencia -->
    <flux:modal.trigger name="attendance-modal">
        <span></span>
    </flux:modal.trigger>
    <flux:modal name="attendance-modal" wire:model="showAttendanceModal" size="xl">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Gestionar Asistencia</flux:heading>
            </div>

            @if($currentActivity)
                <div class="mb-4">
                    <h3 class="text-lg font-medium">{{ $currentActivity->title }}</h3>
                    <div class="mt-1 flex items-center gap-4 text-sm">
                        <div class="flex items-center gap-1">
                            <flux:icon.calendar class="size-4 text-gray-500" />
                            <span>{{ $currentActivity->scheduled_date->format('d/m/Y H:i') }}</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <flux:icon.map-pin class="size-4 text-gray-500" />
                            <span>{{ $currentActivity->location ?: 'Sin ubicación' }}</span>
                        </div>
                        <div>
                            @if($currentActivity->status === 'planned')
                                <flux:badge variant="info">Planificada</flux:badge>
                            @elseif($currentActivity->status === 'in_progress')
                                <flux:badge variant="warning">En Progreso</flux:badge>
                            @elseif($currentActivity->status === 'completed')
                                <flux:badge variant="success">Completada</flux:badge>
                            @else
                                <flux:badge variant="danger">Cancelada</flux:badge>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="mb-4 overflow-hidden rounded-md border border-gray-200">
                    <div class="flex items-center justify-between bg-gray-50 px-4 py-2">
                        <div class="font-medium">Asistentes</div>
                        <div class="text-sm text-gray-500">
                            {{ $currentActivity->attendances()->where('attended', true)->count() }} de {{ $currentActivity->attendances()->count() }} asistentes
                        </div>
                    </div>

                    <div class="max-h-96 overflow-y-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Miembro</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Rol</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Estado</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Asistió</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Check-in/out</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                @forelse($currentActivity->attendances as $attendance)
                                    <tr wire:key="attendance-{{ $attendance->id }}">
                                        <td class="whitespace-nowrap px-4 py-2">
                                            <div class="flex items-center gap-2">
                                                <div class="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                                                    {{ substr($attendance->person->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="font-medium">{{ $attendance->person->name }}</div>
                                                    <div class="text-xs text-gray-500">{{ $attendance->person->phone }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-2">
                                            @php
                                                $member = $group->members()->where('person_id', $attendance->person_id)->first();
                                                $role = $member ? $member->role : 'member';
                                            @endphp

                                            @if($role === 'leader')
                                                <flux:badge variant="success">Líder</flux:badge>
                                            @elseif($role === 'coordinator')
                                                <flux:badge variant="info">Coordinador</flux:badge>
                                            @else
                                                <flux:badge variant="primary">Miembro</flux:badge>
                                            @endif
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-2">
                                            <div class="flex items-center gap-2">
                                                <button wire:click="toggleAttendance({{ $attendance->id }}, 'status')" class="rounded-md p-1 hover:bg-gray-100">
                                                    @if($attendance->status === 'confirmed')
                                                        <flux:badge variant="success">Confirmado</flux:badge>
                                                    @elseif($attendance->status === 'pending')
                                                        <flux:badge variant="warning">Pendiente</flux:badge>
                                                    @else
                                                        <flux:badge variant="danger">Declinado</flux:badge>
                                                    @endif
                                                </button>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-2">
                                            <div class="flex items-center">
                                                <flux:checkbox wire:click="toggleAttendance({{ $attendance->id }}, 'attended')" :checked="$attendance->attended" />
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-2">
                                            <div class="flex items-center gap-2">
                                                @if($attendance->check_in_time)
                                                    <div class="text-xs">
                                                        <span class="font-medium">In:</span> {{ $attendance->check_in_time->format('H:i') }}
                                                    </div>
                                                @else
                                                    <flux:button wire:click="checkInAttendee({{ $attendance->id }})" variant="outline" size="xs">Check-in</flux:button>
                                                @endif

                                                @if($attendance->check_in_time && !$attendance->check_out_time)
                                                    <flux:button wire:click="checkOutAttendee({{ $attendance->id }})" variant="outline" size="xs">Check-out</flux:button>
                                                @elseif($attendance->check_out_time)
                                                    <div class="text-xs">
                                                        <span class="font-medium">Out:</span> {{ $attendance->check_out_time->format('H:i') }}
                                                    </div>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-4 py-4 text-center text-gray-500">No hay asistentes registrados</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            <div class="flex justify-end gap-2">
                <flux:button wire:click="closeAttendanceModal" variant="outline">Cerrar</flux:button>
            </div>
        </div>
    </flux:modal>
</div>
