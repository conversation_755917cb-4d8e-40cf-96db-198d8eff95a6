<div class="w-full space-y-6">
    <x-page-heading>
        <x-slot:title>
            <span class="flex items-center gap-2">
                <flux:icon.document-chart-bar class="size-6 text-primary-500"/>
                {{ __('Reportes de Patrullaje') }}
            </span>
        </x-slot:title>

        <x-slot:subtitle>
            {{ __('Informes y estadísticas de grupos de patrullaje') }}
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home">{{ __('Inicio') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item href="{{ route('admin.patrol.index') }}">{{ __('Grupos de Patrullaje') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item>{{ __('Reportes') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </x-slot:subtitle>

        <x-slot:buttons>
            <div class="flex items-center gap-2">
                <flux:button wire:click="exportCsv" variant="outline" icon="arrow-down-tray">
                    {{ __('Exportar CSV') }}
                </flux:button>
                <flux:button :href="route('admin.patrol.index')" variant="outline" icon="arrow-left">
                    {{ __('Volver') }}
                </flux:button>
            </div>
        </x-slot:buttons>
    </x-page-heading>

    <!-- Estadísticas generales -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-6">
        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Grupos Activos</flux:stat.heading>
                <flux:stat.number>{{ $this->overallStatistics['active_groups'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.shield-check class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Total Miembros</flux:stat.heading>
                <flux:stat.number>{{ $this->overallStatistics['total_members'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.users class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Actividades</flux:stat.heading>
                <flux:stat.number>{{ $this->overallStatistics['total_activities'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.calendar class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Completadas</flux:stat.heading>
                <flux:stat.number>{{ $this->overallStatistics['completed_activities'] }}</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.check-badge class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Tasa Completado</flux:stat.heading>
                <flux:stat.number>{{ $this->overallStatistics['activity_completion_rate'] }}%</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.chart-bar class="size-5" />
            </flux:stat.icon>
        </flux:stat>

        <flux:stat>
            <flux:stat.content>
                <flux:stat.heading>Tasa Asistencia</flux:stat.heading>
                <flux:stat.number>{{ $this->overallStatistics['attendance_rate'] }}%</flux:stat.number>
            </flux:stat.content>
            <flux:stat.icon>
                <flux:icon.user-check class="size-5" />
            </flux:stat.icon>
        </flux:stat>
    </div>

    <!-- Filtros y selección de reporte -->
    <flux:card>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div>
                <h3 class="mb-4 text-lg font-medium">Tipo de Reporte</h3>
                <flux:radio.group wire:model.live="selectedReport">
                    <div class="space-y-2">
                        <flux:radio value="group_summary" label="Resumen de Grupos" />
                        <flux:radio value="member_activity" label="Actividad de Miembros" />
                        <flux:radio value="activity_summary" label="Resumen de Actividades" />
                    </div>
                </flux:radio.group>
            </div>

            <div>
                <h3 class="mb-4 text-lg font-medium">Filtros</h3>
                <div class="space-y-4">
                    <flux:input.group label="Grupo" for="selectedGroup">
                        <flux:select wire:model.live="selectedGroup">
                            <flux:select.option value="">Todos los grupos</flux:select.option>
                            @foreach($this->groups as $group)
                                <flux:select.option value="{{ $group->id }}">{{ $group->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:input.group>

                    <div class="flex items-center gap-2">
                        <flux:checkbox wire:model.live="showInactive" id="showInactive" />
                        <flux:label for="showInactive">Incluir inactivos</flux:label>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="mb-4 text-lg font-medium">Rango de Fechas</h3>
                <div class="space-y-4">
                    <flux:input.group label="Desde" for="dateRangeStart">
                        <flux:input wire:model.live="dateRange.start" id="dateRangeStart" type="date" />
                    </flux:input.group>

                    <flux:input.group label="Hasta" for="dateRangeEnd">
                        <flux:input wire:model.live="dateRange.end" id="dateRangeEnd" type="date" />
                    </flux:input.group>
                </div>
            </div>
        </div>
    </flux:card>

    <!-- Contenido del reporte -->
    <flux:card>
        @if($selectedReport === 'group_summary')
            <h3 class="mb-4 text-xl font-medium">Resumen de Grupos de Patrullaje</h3>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Nombre</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Líder</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Ubicación</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Miembros</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Meta Miembros</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Actividades</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Meta Actividades</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Estado</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        @forelse($this->groupSummaryReport as $group)
                            <tr>
                                <td class="whitespace-nowrap px-4 py-3">
                                    <a href="{{ route('admin.patrol.show', $group) }}" class="font-medium text-primary-600 hover:text-primary-900">
                                        {{ $group->name }}
                                    </a>
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $group->leader->name }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    {{ $group->estate?->name ?? 'N/A' }}
                                    @if($group->municipality)
                                        , {{ $group->municipality->name }}
                                    @endif
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    <div class="flex items-center gap-2">
                                        <span>{{ $group->active_members_count }}</span>
                                        <div class="w-16">
                                            <flux:progress :value="$group->members_completion_percentage" />
                                        </div>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $group->goal_members }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    <div class="flex items-center gap-2">
                                        <span>{{ $group->completed_activities_count }}</span>
                                        <div class="w-16">
                                            <flux:progress :value="$group->activities_completion_percentage" />
                                        </div>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $group->goal_activities }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    @if($group->status === 'active')
                                        <flux:badge variant="success">Activo</flux:badge>
                                    @else
                                        <flux:badge variant="danger">Inactivo</flux:badge>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-4 py-4 text-center text-gray-500">No se encontraron grupos de patrullaje</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        @elseif($selectedReport === 'member_activity')
            <h3 class="mb-4 text-xl font-medium">Actividad de Miembros</h3>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Miembro</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Cédula</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Grupo</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Rol</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Actividades Asistidas</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Actividades Totales</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">% Asistencia</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Estado</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        @forelse($this->memberActivityReport as $member)
                            <tr>
                                <td class="whitespace-nowrap px-4 py-3 font-medium">{{ $member->person_name }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $member->person_identification }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $member->group_name }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    @if($member->role === 'leader')
                                        <flux:badge variant="success">Líder</flux:badge>
                                    @elseif($member->role === 'coordinator')
                                        <flux:badge variant="info">Coordinador</flux:badge>
                                    @else
                                        <flux:badge variant="primary">Miembro</flux:badge>
                                    @endif
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $member->attended_activities }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $member->total_activities }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    <div class="flex items-center gap-2">
                                        <span>{{ $member->attendance_percentage }}%</span>
                                        <div class="w-16">
                                            <flux:progress :value="$member->attendance_percentage" />
                                        </div>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    @if($member->status === 'active')
                                        <flux:badge variant="success">Activo</flux:badge>
                                    @else
                                        <flux:badge variant="danger">Inactivo</flux:badge>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-4 py-4 text-center text-gray-500">No se encontraron datos de actividad de miembros</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        @elseif($selectedReport === 'activity_summary')
            <h3 class="mb-4 text-xl font-medium">Resumen de Actividades</h3>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Actividad</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Grupo</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Tipo</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Fecha</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Ubicación</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Asistentes</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Total Invitados</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">% Asistencia</th>
                            <th class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Estado</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        @forelse($this->activitySummaryReport as $activity)
                            <tr>
                                <td class="whitespace-nowrap px-4 py-3 font-medium">{{ $activity->title }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $activity->group_name }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    @if($activity->type === 'patrol')
                                        <flux:badge variant="primary">Patrullaje</flux:badge>
                                    @elseif($activity->type === 'meeting')
                                        <flux:badge variant="primary">Reunión</flux:badge>
                                    @elseif($activity->type === 'training')
                                        <flux:badge variant="info">Capacitación</flux:badge>
                                    @else
                                        <flux:badge variant="default">Otro</flux:badge>
                                    @endif
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">{{ \Carbon\Carbon::parse($activity->scheduled_date)->format('d/m/Y H:i') }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $activity->location ?: 'N/A' }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $activity->attended_count }}</td>
                                <td class="whitespace-nowrap px-4 py-3">{{ $activity->total_attendees }}</td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    <div class="flex items-center gap-2">
                                        <span>{{ $activity->attendance_percentage }}%</span>
                                        <div class="w-16">
                                            <flux:progress :value="$activity->attendance_percentage" />
                                        </div>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-4 py-3">
                                    @if($activity->status === 'planned')
                                        <flux:badge variant="info">Planificada</flux:badge>
                                    @elseif($activity->status === 'in_progress')
                                        <flux:badge variant="warning">En Progreso</flux:badge>
                                    @elseif($activity->status === 'completed')
                                        <flux:badge variant="success">Completada</flux:badge>
                                    @else
                                        <flux:badge variant="danger">Cancelada</flux:badge>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="px-4 py-4 text-center text-gray-500">No se encontraron actividades</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        @endif
    </flux:card>
</div>
