<div>
<div class="min-h-screen bg-gradient-to-b from-primary-50 via-white to-white dark:from-primary-950/30 dark:via-gray-950 dark:to-gray-950">
    <!-- Hero Section -->
    <div class="relative overflow-hidden">
        <!-- Colorful Background Elements -->


        <div class="relative mx-auto max-w-7xl px-4 py-20 sm:px-6 sm:py-28 lg:px-8">
            <div class="text-center max-w-3xl mx-auto">
                <div class="mb-8 relative">
                    <div class="absolute -inset-4 bg-primary-100 rounded-full blur-lg opacity-70 dark:bg-primary-900/30"></div>
                </div>

                <h1 class="text-5xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl md:text-7xl">
                    <span class="inline-block">Sistema</span>
                    <span class="inline-block bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">1x10</span>
                </h1>

                <p class="mt-6 text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Plataforma integral para la organización, seguimiento y movilización electoral con gestión de grupos 1x10 y patrullas.
                </p>

                <div class="mt-10 flex flex-col sm:flex-row justify-center gap-4">
                    <flux:button href="{{ route('login') }}" wire:navigate variant="primary" class="w-full sm:w-auto group relative overflow-hidden">
                        <span class="flex items-center justify-center">
                            <flux:icon.user-circle class="mr-2 size-5" />
                            Iniciar Sesión
                        </span>
                    </flux:button>
                    <flux:button href="{{ route('register') }}" wire:navigate variant="outline" class="w-full sm:w-auto group">
                        <span class="flex items-center justify-center">
                            <flux:icon.user-plus class="mr-2 size-5" />
                            Registrarse
                        </span>
                    </flux:button>
                </div>
            </div>

            <!-- Dashboard Preview -->
            <div class="mt-16 relative mx-auto max-w-5xl">
                <div class="relative rounded-xl overflow-hidden shadow-2xl">
                    <!-- Dashboard Mockup -->
                    <div class="bg-white dark:bg-gray-800 aspect-video w-full">
                        <!-- Dashboard Header -->
                        <div class="bg-primary-600 dark:bg-primary-700 h-16 flex items-center px-6">
                            <div class="flex items-center space-x-3">
                                <x-app-logo-icon class="h-8 w-8 text-white" />
                                <span class="text-white font-semibold">{{ config('app.name') }}</span>
                            </div>
                            <div class="ml-auto flex items-center space-x-4">
                                <div class="w-8 h-8 rounded-full bg-white/20"></div>
                                <div class="w-8 h-8 rounded-full bg-white/20"></div>
                                <div class="w-8 h-8 rounded-full bg-white/20"></div>
                            </div>
                        </div>

                        <!-- Dashboard Content -->
                        <div class="flex h-[calc(100%-4rem)]">
                            <!-- Sidebar -->
                            <div class="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4">
                                <div class="space-y-2">
                                    <div class="h-8 bg-primary-100 dark:bg-primary-900/40 rounded w-full"></div>
                                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                                </div>
                            </div>

                            <!-- Main Content -->
                            <div class="flex-1 p-6 bg-gray-100 dark:bg-gray-800 overflow-hidden">
                                <!-- Stats Cards -->
                                <div class="grid grid-cols-4 gap-4 mb-6">
                                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                                        <div class="h-4 w-1/2 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                                        <div class="h-6 w-1/3 bg-primary-200 dark:bg-primary-900/40 rounded"></div>
                                    </div>
                                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                                        <div class="h-4 w-1/2 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                                        <div class="h-6 w-1/3 bg-primary-200 dark:bg-primary-900/40 rounded"></div>
                                    </div>
                                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                                        <div class="h-4 w-1/2 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                                        <div class="h-6 w-1/3 bg-primary-200 dark:bg-primary-900/40 rounded"></div>
                                    </div>
                                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                                        <div class="h-4 w-1/2 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
                                        <div class="h-6 w-1/3 bg-primary-200 dark:bg-primary-900/40 rounded"></div>
                                    </div>
                                </div>

                                <!-- Table -->
                                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm p-4">
                                    <div class="h-4 w-1/4 bg-gray-200 dark:bg-gray-600 rounded mb-4"></div>
                                    <div class="space-y-2">
                                        <div class="h-8 bg-gray-100 dark:bg-gray-600 rounded w-full"></div>
                                        <div class="h-8 bg-gray-50 dark:bg-gray-800 rounded w-full"></div>
                                        <div class="h-8 bg-gray-50 dark:bg-gray-800 rounded w-full"></div>
                                        <div class="h-8 bg-gray-50 dark:bg-gray-800 rounded w-full"></div>
                                        <div class="h-8 bg-gray-50 dark:bg-gray-800 rounded w-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
