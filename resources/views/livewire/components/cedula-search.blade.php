<div>
    <flux:card>
        <flux:card.header>
            <flux:card.title>Buscar por Cédula</flux:card.title>
        </flux:card.header>
        <flux:card.content>
            <div class="space-y-4">
                <div class="flex items-end gap-2">
                    <div class="flex-1">
                        <flux:input 
                            wire:model="cedula" 
                            label="Número de Cédula" 
                            placeholder="Ingrese el número de cédula" 
                            wire:keydown.enter="search"
                        />
                    </div>
                    <flux:button wire:click="search" variant="primary" :disabled="$loading">
                        @if($loading)
                            <flux:icon.loader-2 class="size-4 animate-spin" />
                            Buscando...
                        @else
                            <flux:icon.search class="size-4" />
                            Buscar
                        @endif
                    </flux:button>
                </div>
                
                @error('cedula')
                    <flux:callout variant="danger" icon="alert-triangle">
                        <flux:callout.text>{{ $message }}</flux:callout.text>
                    </flux:callout>
                @enderror
                
                @if($error)
                    <flux:callout variant="danger" icon="alert-triangle">
                        <flux:callout.text>{{ $error }}</flux:callout.text>
                    </flux:callout>
                @endif
                
                @if($personData)
                    <flux:callout variant="success" icon="check">
                        <flux:callout.heading>Persona encontrada</flux:callout.heading>
                        <flux:callout.text>
                            <div class="space-y-2">
                                <p><strong>Nombre:</strong> {{ $personData['nombres'] }} {{ $personData['apellidos'] }}</p>
                                <p><strong>Cédula:</strong> {{ $cedula }}</p>
                                @if(isset($personData['fecha_nacimiento']))
                                    <p><strong>Fecha de Nacimiento:</strong> {{ $personData['fecha_nacimiento'] }}</p>
                                @endif
                                <div class="mt-4">
                                    <flux:button wire:click="fillForm" variant="filled" size="sm">
                                        Usar estos datos
                                    </flux:button>
                                </div>
                            </div>
                        </flux:callout.text>
                    </flux:callout>
                @endif
            </div>
        </flux:card.content>
    </flux:card>
</div>
