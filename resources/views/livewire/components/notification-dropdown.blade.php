<div>
    <flux:dropdown position="bottom-end" class="relative">
        <div class="relative">
            <flux:button variant="filled" size="sm" class="rounded-full p-2 relative">
                <flux:icon.bell class="size-5" />
                @if($unreadCount > 0)
                    <span class="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white">
                        {{ $unreadCount > 99 ? '99+' : $unreadCount }}
                    </span>
                @endif
            </flux:button>
        </div>

        <flux:menu class="w-80 max-h-[80vh] overflow-y-auto">
            <div class="p-2 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Notificaciones</h3>
                <div class="flex items-center gap-2">
                    <flux:button
                        wire:click="toggleShowAll"
                        variant="filled"
                        size="xs"
                        class="text-xs"
                    >
                        {{ $showAll ? 'Ver no leídas' : 'Ver todas' }}
                    </flux:button>
                    @if($unreadCount > 0)
                        <flux:button
                            wire:click="markAllAsRead"
                            variant="filled"
                            size="xs"
                            class="text-xs"
                        >
                            Marcar todas como leídas
                        </flux:button>
                    @endif
                </div>
            </div>

            @forelse($notifications as $notification)
                <div
                    class="p-3 border-b border-gray-200 dark:border-gray-700 {{ $notification['read_at'] ? 'bg-gray-50 dark:bg-gray-800/50' : 'bg-white dark:bg-gray-800' }}"
                    wire:key="notification-{{ $notification['id'] }}"
                >
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            @php
                                $iconColor = match($notification['type']) {
                                    'success' => 'text-green-500 dark:text-green-400',
                                    'warning' => 'text-yellow-500 dark:text-yellow-400',
                                    'error' => 'text-red-500 dark:text-red-400',
                                    'info' => 'text-blue-500 dark:text-blue-400',
                                    default => 'text-gray-500 dark:text-gray-400',
                                };

                                $iconName = match($notification['type']) {
                                    'success' => 'check-circle',
                                    'warning' => 'exclamation-circle',
                                    'error' => 'x-circle',
                                    'info' => 'information-circle',
                                    default => 'bell',
                                };
                            @endphp

                            @if($iconName === 'check-circle')
                                <flux:icon.check-circle class="size-5 {{ $iconColor }}" />
                            @elseif($iconName === 'exclamation-circle')
                                <flux:icon.exclamation-circle class="size-5 {{ $iconColor }}" />
                            @elseif($iconName === 'x-circle')
                                <flux:icon.x-circle class="size-5 {{ $iconColor }}" />
                            @elseif($iconName === 'information-circle')
                                <flux:icon.information-circle class="size-5 {{ $iconColor }}" />
                            @else
                                <flux:icon.bell class="size-5 {{ $iconColor }}" />
                            @endif
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $notification['title'] }}
                                </p>
                                <div class="flex items-center gap-1">
                                    @if(!$notification['read_at'])
                                        <flux:button
                                            wire:click="markAsRead({{ $notification['id'] }})"
                                            variant="filled"
                                            size="xs"
                                            class="p-1"
                                            title="Marcar como leída"
                                        >
                                            <flux:icon.check class="size-3" />
                                        </flux:button>
                                    @endif
                                    <flux:button
                                        wire:click="deleteNotification({{ $notification['id'] }})"
                                        variant="filled"
                                        size="xs"
                                        class="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                        title="Eliminar notificación"
                                    >
                                        <flux:icon.trash class="size-3" />
                                    </flux:button>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                {{ $notification['message'] }}
                            </p>
                            @if($notification['link'])
                                <a
                                    href="{{ $notification['link'] }}"
                                    class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1 inline-block"
                                >
                                    Ver más
                                </a>
                            @endif
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ \Carbon\Carbon::parse($notification['created_at'])->diffForHumans() }}
                            </p>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                    <flux:icon.inbox class="size-8 mx-auto mb-2 opacity-50" />
                    <p>No tienes notificaciones {{ $showAll ? '' : 'no leídas' }}.</p>
                </div>
            @endforelse

            @if(count($notifications) > 0)
                <div class="p-2 border-t border-gray-200 dark:border-gray-700 text-center">
                    <a href="{{ route('notifications') }}" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                        Ver todas las notificaciones
                    </a>
                </div>
            @endif
        </flux:menu>
    </flux:dropdown>
</div>
