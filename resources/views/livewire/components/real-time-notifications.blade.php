<div>
    <flux:dropdown position="bottom" align="end">
        <flux:button variant="ghost" size="sm" class="relative">
            <flux:icon.bell class="size-5" />
            @if($unreadCount > 0)
                <span class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                    {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                </span>
            @endif
        </flux:button>

        <flux:menu class="w-80">
            <div class="flex items-center justify-between px-4 py-2 border-b border-gray-100 dark:border-gray-700">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Notificaciones</h3>
                <div class="flex space-x-2">
                    <button wire:click="toggleShowAll" class="text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300">
                        {{ $showAll ? 'Mostrar no leídas' : 'Mostrar todas' }}
                    </button>
                    @if($unreadCount > 0)
                        <button wire:click="markAllAsRead" class="text-xs text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300">
                            Marcar todas como leídas
                        </button>
                    @endif
                </div>
            </div>

            @forelse($notifications as $notification)
                <flux:menu.item wire:key="{{ $notification['id'] }}" class="border-b border-gray-100 dark:border-gray-700 last:border-0 {{ $notification['read_at'] ? 'bg-gray-50 dark:bg-gray-800/30' : '' }}">
                    <div class="flex flex-col w-full">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start gap-2">
                                <div class="mt-0.5">
                                    @php
                                        $iconColor = match($notification['type']) {
                                            'success' => 'text-green-500',
                                            'warning' => 'text-yellow-500',
                                            'error' => 'text-red-500',
                                            default => 'text-blue-500'
                                        };

                                        $icon = match($notification['type']) {
                                            'success' => 'check-circle',
                                            'warning' => 'exclamation-triangle',
                                            'error' => 'x-circle',
                                            default => 'information-circle'
                                        };
                                    @endphp

                                    @if($icon === 'check-circle')
                                        <flux:icon.check-circle class="size-5 {{ $iconColor }}" />
                                    @elseif($icon === 'exclamation-triangle')
                                        <flux:icon.exclamation-triangle class="size-5 {{ $iconColor }}" />
                                    @elseif($icon === 'x-circle')
                                        <flux:icon.x-circle class="size-5 {{ $iconColor }}" />
                                    @else
                                        <flux:icon.information-circle class="size-5 {{ $iconColor }}" />
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $notification['title'] }}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $notification['message'] }}
                                    </p>
                                </div>
                            </div>
                            <button wire:click="markAsRead('{{ $notification['id'] }}')" class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400">
                                <flux:icon.x-mark class="size-4" />
                            </button>
                        </div>
                        <div class="flex items-center justify-between mt-1 pl-7">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $notification['created_at'] }}
                            </span>
                            @if($notification['link'])
                                <a href="{{ $notification['link'] }}" wire:navigate class="text-xs text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300">
                                    Ver detalles
                                </a>
                            @endif
                        </div>
                    </div>
                </flux:menu.item>
            @empty
                <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                    No tienes notificaciones {{ $showAll ? '' : 'nuevas' }}
                </div>
            @endforelse

            <div class="px-4 py-2 border-t border-gray-100 dark:border-gray-700">
                <a href="{{ route('notifications') }}" wire:navigate class="block text-xs text-center text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300">
                    Ver todas las notificaciones
                </a>
            </div>
        </flux:menu>
    </flux:dropdown>

    <script>
        document.addEventListener('notification-alert', function() {
            // Reproducir un sonido de notificación
            const audio = new Audio('/sounds/notification.mp3');
            audio.play();

            // Mostrar una notificación del navegador si está permitido
            if (Notification.permission === 'granted') {
                new Notification('Nueva notificación', {
                    body: 'Has recibido una nueva notificación',
                    icon: '/images/logo.png'
                });
            }
        });
    </script>
</div>
