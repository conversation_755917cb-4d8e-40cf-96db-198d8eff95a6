<div>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Resumen Integrado: ') }} {{ $summary['person']->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Información Personal -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            @if($summary['person']->profile_photo_path)
                                <img class="h-16 w-16 rounded-full object-cover" src="{{ Storage::url($summary['person']->profile_photo_path) }}" alt="{{ $summary['person']->name }}">
                            @else
                                <div class="h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                    <span class="text-primary-700 dark:text-primary-300 text-xl font-bold">{{ substr($summary['person']->name, 0, 1) }}</span>
                                </div>
                            @endif
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-lg font-medium">{{ $summary['person']->name }}</h3>
                            <div class="mt-1 grid grid-cols-1 sm:grid-cols-2 gap-2">
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Cédula: {{ $summary['person']->cedula }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Teléfono: {{ $summary['person']->phone ?: 'No especificado' }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Email: {{ $summary['person']->email ?: 'No especificado' }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Dirección: {{ $summary['person']->address ?: 'No especificada' }}</p>
                                </div>
                            </div>
                            <div class="mt-2 flex flex-wrap gap-2">
                                @if($summary['person']->is_1x10)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        1x10
                                    </span>
                                @endif
                                @if($summary['person']->is_leader)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        Líder
                                    </span>
                                @endif
                                @if($summary['person']->is_volunteer)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                        Voluntario
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="ml-4">
                            <a href="{{ route('admin.persons.show', $summary['person']) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-500 focus:outline-none focus:border-primary-700 focus:shadow-outline-primary active:bg-primary-700 transition ease-in-out duration-150">
                                Ver Perfil
                            </a>
                            <form action="{{ route('admin.integration.create-integrated-tracking', $summary['person']) }}" method="POST" class="mt-2">
                                @csrf
                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-5 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:border-primary-300 focus:shadow-outline-primary active:bg-primary-200 transition ease-in-out duration-150">
                                    Actualizar Tracking
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Grupos 1x10 -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h3 class="text-lg font-medium mb-4">{{ __('Grupos 1x10') }}</h3>

                        @if($summary['groups']->count() > 0)
                            <div class="space-y-4">
                                @foreach($summary['groups'] as $membership)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="font-medium">{{ $membership->group->name }}</h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    Líder: {{ $membership->group->leader->name ?? 'No especificado' }}
                                                </p>
                                                <div class="mt-1">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $membership->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                                        {{ $membership->status === 'active' ? 'Activo' : 'Inactivo' }}
                                                    </span>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                        {{ $membership->role }}
                                                    </span>
                                                </div>
                                            </div>
                                            <a href="{{ route('admin.groups.show', $membership->group) }}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:border-primary-300 focus:shadow-outline-primary active:bg-primary-200 transition ease-in-out duration-150">
                                                Ver Grupo
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                No pertenece a ningún grupo 1x10
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Eventos Electorales -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h3 class="text-lg font-medium mb-4">{{ __('Eventos Electorales') }}</h3>

                        @if($summary['electoral_events']->count() > 0)
                            <div class="space-y-4">
                                @foreach($summary['electoral_events'] as $attendance)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="font-medium">{{ $attendance->event->name }}</h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    Fecha: {{ $attendance->event->start_date->format('d/m/Y H:i') }}
                                                </p>
                                                <div class="mt-1">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        {{ $attendance->status === 'attended' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                                           ($attendance->status === 'confirmed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                                           ($attendance->status === 'registered' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                                           'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200')) }}">
                                                        {{ $attendance->status === 'attended' ? 'Asistió' :
                                                           ($attendance->status === 'confirmed' ? 'Confirmado' :
                                                           ($attendance->status === 'registered' ? 'Registrado' : 'Ausente')) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <a href="{{ route('admin.electoral.events.show', $attendance->event) }}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:border-primary-300 focus:shadow-outline-primary active:bg-primary-200 transition ease-in-out duration-150">
                                                Ver Evento
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                No ha asistido a eventos electorales
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Movilizaciones Electorales -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h3 class="text-lg font-medium mb-4">{{ __('Movilizaciones Electorales') }}</h3>

                        @if($summary['mobilizations']->count() > 0)
                            <div class="space-y-4">
                                @foreach($summary['mobilizations'] as $detail)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="font-medium">{{ $detail->mobilization->name }}</h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    Fecha: {{ $detail->mobilization->mobilization_date->format('d/m/Y') }}
                                                </p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    Centro: {{ $detail->mobilization->votingCenter->name ?? 'No especificado' }}
                                                </p>
                                                <div class="mt-1">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        {{ $detail->status === 'voted' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                                           ($detail->status === 'mobilized' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                                           ($detail->status === 'confirmed' ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200' :
                                                           ($detail->status === 'contacted' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                                           ($detail->status === 'pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                                                           'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200')))) }}">
                                                        {{ $detail->status === 'voted' ? 'Votó' :
                                                           ($detail->status === 'mobilized' ? 'Movilizado' :
                                                           ($detail->status === 'confirmed' ? 'Confirmado' :
                                                           ($detail->status === 'contacted' ? 'Contactado' :
                                                           ($detail->status === 'pending' ? 'Pendiente' : 'No Asistió')))) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <a href="{{ route('admin.electoral.mobilization.show', $detail->mobilization) }}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:border-primary-300 focus:shadow-outline-primary active:bg-primary-200 transition ease-in-out duration-150">
                                                Ver Movilización
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                No ha participado en movilizaciones electorales
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Campañas -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h3 class="text-lg font-medium mb-4">{{ __('Campañas') }}</h3>

                        @if($summary['campaigns']->count() > 0)
                            <div class="space-y-4">
                                @foreach($summary['campaigns'] as $target)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="font-medium">{{ $target->campaign->name }}</h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    Tipo: {{ ucfirst($target->campaign->type) }}
                                                </p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    Fecha: {{ $target->campaign->scheduled_date->format('d/m/Y') }}
                                                </p>
                                                <div class="mt-1">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        {{ $target->status === 'delivered' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                                           ($target->status === 'sent' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                                           ($target->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                                           'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200')) }}">
                                                        {{ $target->status === 'delivered' ? 'Entregado' :
                                                           ($target->status === 'sent' ? 'Enviado' :
                                                           ($target->status === 'pending' ? 'Pendiente' : 'Fallido')) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <a href="{{ route('admin.campaigns.show', $target->campaign) }}" class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:border-primary-300 focus:shadow-outline-primary active:bg-primary-200 transition ease-in-out duration-150">
                                                Ver Campaña
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                No ha sido objetivo de campañas
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Tracking Integrado -->
            <div class="mt-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">{{ __('Tracking Integrado') }}</h3>

                    @if($summary['trackings']->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-800">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Fecha</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tipo</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Estado</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Notas</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Usuario</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                                    @foreach($summary['trackings'] as $tracking)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                {{ $tracking->tracking_date->format('d/m/Y H:i') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                {{ ucfirst(str_replace('_', ' ', $tracking->tracking_type)) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    {{ in_array($tracking->status, ['completed', 'attended', 'voted', 'delivered']) ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                                       (in_array($tracking->status, ['in_progress', 'confirmed', 'mobilized', 'sent']) ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                                       (in_array($tracking->status, ['pending', 'registered', 'contacted']) ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                                       'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200')) }}">
                                                    {{ ucfirst($tracking->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                {{ $tracking->notes }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                {{ $tracking->user->name ?? 'Sistema' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            No hay registros de tracking
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
