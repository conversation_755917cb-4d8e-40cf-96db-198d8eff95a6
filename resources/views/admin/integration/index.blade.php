<div>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Integración de Módulos') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">{{ __('Panel de Integración') }}</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Integración Personas - Asignación 1x10 -->
                        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Personas → Asignación 1x10</h4>
                            <p class="text-sm mb-4">Gestiona la relación entre líderes 1x10 y sus asignados.</p>
                            <a href="{{ route('admin.persons.assign') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                Ir a Asignar 1x10
                            </a>
                        </div>

                        <!-- Integración Líderes 1x10 - Electoral -->
                        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Líderes 1x10 → Electoral</h4>
                            <p class="text-sm mb-4">Asigna líderes 1x10 a movilizaciones electorales.</p>
                            <a href="{{ route('admin.electoral.mobilization') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                Ir a Movilización
                            </a>
                        </div>

                        <!-- Integración Electoral - Campañas -->
                        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Electoral → Campañas</h4>
                            <p class="text-sm mb-4">Crea campañas para eventos y movilizaciones electorales.</p>
                            <a href="{{ route('admin.campaigns') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                Ir a Campañas
                            </a>
                        </div>

                        <!-- Integración Tracking - Todos los módulos -->
                        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Tracking Integrado</h4>
                            <p class="text-sm mb-4">Visualiza el seguimiento integrado de personas en todos los módulos.</p>
                            <a href="{{ route('admin.tracking.index') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                Ir a Tracking
                            </a>
                        </div>

                        <!-- Integración Grupos de Patrulla - Campañas -->
                        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Grupos de Patrulla → Campañas</h4>
                            <p class="text-sm mb-4">Crea campañas dirigidas a grupos de patrulla específicos.</p>
                            <a href="{{ route('admin.patrol.index') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                Ir a Grupos de Patrulla
                            </a>
                        </div>


                    </div>
                </div>
            </div>


        </div>
    </div>
</div>
