@props([
    'message' => 'Loading...',
    'size' => 'md',
    'variant' => 'primary',
])

@php
    $containerClasses = match ($size) {
        'sm' => 'min-h-[100px]',
        'md' => 'min-h-[200px]',
        'lg' => 'min-h-[300px]',
        'xl' => 'min-h-[400px]',
        default => 'min-h-[200px]',
    };

    $spinnerClasses = match ($size) {
        'sm' => 'size-8',
        'md' => 'size-12',
        'lg' => 'size-16',
        'xl' => 'size-20',
        default => 'size-12',
    };

    $spinnerColor = match ($variant) {
        'primary' => 'text-blue-600 dark:text-blue-500',
        'secondary' => 'text-gray-600 dark:text-gray-400',
        'success' => 'text-green-600 dark:text-green-500',
        'warning' => 'text-yellow-600 dark:text-yellow-500',
        'danger' => 'text-red-600 dark:text-red-500',
        default => 'text-blue-600 dark:text-blue-500',
    };
@endphp
<div>
<div {{ $attributes->merge([
    'class' => 'flex flex-col items-center justify-center w-full ' . $containerClasses
]) }}>
    <div class="flex flex-col items-center gap-4">
        <div class="{{ $spinnerClasses }} animate-spin">
            <svg
                class="{{ $spinnerColor }}"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg>
        </div>

        @if($message)
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ $message }}
            </p>
        @endif
    </div>
</div>
