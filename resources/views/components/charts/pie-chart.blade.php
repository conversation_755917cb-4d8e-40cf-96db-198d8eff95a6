@props([
    'labels' => [],
    'values' => [],
    'colors' => ['primary', 'blue', 'green', 'purple', 'amber', 'red'],
    'height' => 'h-64',
    'title' => null,
])

@php
    // Asegurarse de que values sea un array
    $valuesArray = is_array($values) ? $values : [$values];
    $total = array_sum($valuesArray);

    // Convertir colors a array si es string
    if (is_string($colors) && strpos($colors, '[') !== false) {
        // Intentar evaluar la string como array
        try {
            $colorsEval = eval('return ' . $colors . ';');
            if (is_array($colorsEval)) {
                $colors = $colorsEval;
            }
        } catch (\Throwable $e) {
            // Si falla, usar el array por defecto
            $colors = ['primary', 'blue', 'green', 'purple', 'amber', 'red'];
        }
    } elseif (!is_array($colors)) {
        $colors = ['primary', 'blue', 'green', 'purple', 'amber', 'red'];
    }

    // Asegurarse de que labels sea un array
    $labelsArray = is_array($labels) ? $labels : [$labels];
@endphp

<div {{ $attributes->merge(['class' => 'w-full ' . $height]) }}>
    @if($title)
        <h3 class="text-lg font-medium mb-4">{{ $title }}</h3>
    @endif

    <div class="flex flex-col md:flex-row items-center justify-center h-full gap-6">
        <!-- Gráfico circular visual -->
        <div class="relative w-40 h-40">
            @php
                $cumulativePercentage = 0;
                $colorClasses = [
                    'primary' => 'bg-primary-500',
                    'blue' => 'bg-blue-500',
                    'green' => 'bg-green-500',
                    'purple' => 'bg-purple-500',
                    'amber' => 'bg-amber-500',
                    'red' => 'bg-red-500',
                ];
            @endphp

            <div class="absolute inset-0 rounded-full overflow-hidden">
                @foreach($valuesArray as $index => $value)
                    @php
                        $percentage = $total > 0 ? ($value / $total) * 100 : 0;
                        $color = $colors[$index % count($colors)];
                        $barColor = $colorClasses[$color] ?? $colorClasses['primary'];

                        // Calcular ángulos para el gráfico circular
                        $startAngle = $cumulativePercentage * 3.6; // 360 / 100 = 3.6
                        $endAngle = ($cumulativePercentage + $percentage) * 3.6;
                        $cumulativePercentage += $percentage;

                        // Crear un gradiente cónico para simular un gráfico circular
                        $gradientStyle = "background-image: conic-gradient(transparent {$startAngle}deg, {$barColor} {$startAngle}deg, {$barColor} {$endAngle}deg, transparent {$endAngle}deg);";
                    @endphp

                    <div class="absolute inset-0" style="{{ $gradientStyle }}"></div>
                @endforeach

                <!-- Círculo central para crear efecto de dona -->
                <div class="absolute inset-0 m-auto w-24 h-24 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium">Total: {{ $total }}</span>
                </div>
            </div>
        </div>

        <!-- Leyenda -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
            @foreach($valuesArray as $index => $value)
                @php
                    $label = $labelsArray[$index] ?? '';
                    $color = $colors[$index % count($colors)];
                    $percentage = $total > 0 ? round(($value / $total) * 100) : 0;
                    $barColor = $colorClasses[$color] ?? $colorClasses['primary'];
                @endphp

                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 rounded-full {{ $barColor }}"></div>
                    <span class="text-sm">{{ $label }}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        {{ $value }} ({{ $percentage }}%)
                    </span>
                </div>
            @endforeach
        </div>
    </div>
</div>
