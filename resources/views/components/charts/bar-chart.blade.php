@props([
    'labels' => [],
    'values' => [],
    'colors' => ['primary', 'blue', 'green', 'purple', 'amber', 'red'],
    'height' => 'h-64',
    'showValues' => true,
    'showLabels' => true,
    'title' => null,
])

@php
    // Asegurarse de que values sea un array
    $valuesArray = is_array($values) ? $values : [$values];
    $max = count($valuesArray) > 0 ? max($valuesArray) : 0;

    // Convertir colors a array si es string
    if (is_string($colors) && strpos($colors, '[') !== false) {
        // Intentar evaluar la string como array
        try {
            $colorsEval = eval('return ' . $colors . ';');
            if (is_array($colorsEval)) {
                $colors = $colorsEval;
            }
        } catch (\Throwable $e) {
            // Si falla, usar el array por defecto
            $colors = ['primary', 'blue', 'green', 'purple', 'amber', 'red'];
        }
    } elseif (!is_array($colors)) {
        $colors = ['primary', 'blue', 'green', 'purple', 'amber', 'red'];
    }
@endphp

<div {{ $attributes->merge(['class' => 'w-full ' . $height]) }}>
    @if($title)
        <h3 class="text-lg font-medium mb-4">{{ $title }}</h3>
    @endif

    <div class="flex items-end h-full gap-2 overflow-x-auto pb-6">
        @foreach($valuesArray as $index => $value)
            @php
                $label = $labels[$index] ?? '';
                $color = $colors[$index % count($colors)];
                $percentage = $max > 0 ? ($value / $max) * 100 : 0;
                $colorClasses = [
                    'primary' => 'bg-primary-500 hover:bg-primary-600',
                    'blue' => 'bg-blue-500 hover:bg-blue-600',
                    'green' => 'bg-green-500 hover:bg-green-600',
                    'purple' => 'bg-purple-500 hover:bg-purple-600',
                    'amber' => 'bg-amber-500 hover:bg-amber-600',
                    'red' => 'bg-red-500 hover:bg-red-600',
                ];
                $barColor = $colorClasses[$color] ?? $colorClasses['primary'];
            @endphp

            <div class="flex-1 min-w-14 flex flex-col items-center">
                <div class="relative group w-full">
                    <div class="w-full {{ $barColor }} rounded-t transition-all duration-300"
                         style="height: {{ max(5, $percentage) }}%">
                    </div>

                    @if($showValues)
                        <div class="absolute bottom-full mb-1 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                            {{ $value }}
                        </div>
                    @endif
                </div>

                @if($showLabels && $label)
                    <div class="text-xs text-gray-600 dark:text-gray-400 mt-2 truncate max-w-full text-center">
                        {{ $label }}
                    </div>
                @endif
            </div>
        @endforeach
    </div>
</div>
