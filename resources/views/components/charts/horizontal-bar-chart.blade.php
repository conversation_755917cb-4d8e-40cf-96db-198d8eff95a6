@props([
    'labels' => [],
    'values' => [],
    'colors' => ['primary', 'blue', 'green', 'purple', 'amber', 'red'],
    'height' => 'h-64',
    'showValues' => true,
    'title' => null,
])

@php
    // Asegurarse de que values sea un array
    $valuesArray = is_array($values) ? $values : [$values];
    $max = count($valuesArray) > 0 ? max($valuesArray) : 0;

    // Convertir colors a array si es string
    if (is_string($colors) && strpos($colors, '[') !== false) {
        // Intentar evaluar la string como array
        try {
            $colorsEval = eval('return ' . $colors . ';');
            if (is_array($colorsEval)) {
                $colors = $colorsEval;
            }
        } catch (\Throwable $e) {
            // Si falla, usar el array por defecto
            $colors = ['primary', 'blue', 'green', 'purple', 'amber', 'red'];
        }
    } elseif (!is_array($colors)) {
        $colors = ['primary', 'blue', 'green', 'purple', 'amber', 'red'];
    }

    // Asegurarse de que labels sea un array
    $labelsArray = is_array($labels) ? $labels : [$labels];
@endphp

<div {{ $attributes->merge(['class' => 'w-full ' . $height]) }}>
    @if($title)
        <h3 class="text-lg font-medium mb-4">{{ $title }}</h3>
    @endif

    <div class="space-y-4 h-full flex flex-col justify-center">
        @foreach($valuesArray as $index => $value)
            @php
                $label = $labelsArray[$index] ?? '';
                $color = $colors[$index % count($colors)];
                $percentage = $max > 0 ? ($value / $max) * 100 : 0;
                $colorClasses = [
                    'primary' => 'bg-primary-500',
                    'blue' => 'bg-blue-500',
                    'green' => 'bg-green-500',
                    'purple' => 'bg-purple-500',
                    'amber' => 'bg-amber-500',
                    'red' => 'bg-red-500',
                ];
                $barColor = $colorClasses[$color] ?? $colorClasses['primary'];
            @endphp

            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span>{{ $label }}</span>
                    @if($showValues)
                        <span>{{ $value }}</span>
                    @endif
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
                    <div class="{{ $barColor }} h-2.5 rounded-full transition-all duration-500"
                         style="width: {{ max(3, $percentage) }}%"></div>
                </div>
            </div>
        @endforeach
    </div>
</div>
