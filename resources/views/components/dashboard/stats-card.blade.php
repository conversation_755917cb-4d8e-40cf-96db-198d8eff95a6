@props([
    'title',
    'value',
    'icon' => null,
    'description' => null,
    'trend' => null,
    'trendValue' => null,
    'color' => 'primary',
    'href' => null,
])

@php
    $colorClasses = [
        'primary' => 'bg-primary-50 text-primary-700 dark:bg-primary-900/50 dark:text-primary-300',
        'secondary' => 'bg-zinc-100 text-zinc-700 dark:bg-zinc-800 dark:text-zinc-300',
        'success' => 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300',
        'danger' => 'bg-red-50 text-red-700 dark:bg-red-900/50 dark:text-red-300',
        'warning' => 'bg-amber-50 text-amber-700 dark:bg-amber-900/50 dark:text-amber-300',
        'info' => 'bg-sky-50 text-sky-700 dark:bg-sky-900/50 dark:text-sky-300',
    ];
    
    $trendClasses = [
        'up' => 'text-emerald-600 dark:text-emerald-400',
        'down' => 'text-red-600 dark:text-red-400',
        'neutral' => 'text-zinc-600 dark:text-zinc-400',
    ];
    
    $trendIcons = [
        'up' => 'arrow-up',
        'down' => 'arrow-down',
        'neutral' => 'minus',
    ];
@endphp

<div {{ $attributes->merge(['class' => 'rounded-lg border border-zinc-200 bg-white p-6 shadow-sm dark:border-zinc-700 dark:bg-zinc-800']) }}>
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">{{ $title }}</h3>
            <div class="mt-1 flex items-baseline">
                <p class="text-2xl font-semibold text-zinc-900 dark:text-white">{{ $value }}</p>
                @if($trend && $trendValue)
                    <p class="ml-2 flex items-baseline text-sm font-semibold {{ $trendClasses[$trend] }}">
                        <flux:icon :name="$trendIcons[$trend]" class="mr-0.5 size-3" />
                        <span>{{ $trendValue }}</span>
                    </p>
                @endif
            </div>
            @if($description)
                <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">{{ $description }}</p>
            @endif
        </div>
        @if($icon)
            <div class="rounded-md p-2 {{ $colorClasses[$color] }}">
                <flux:icon :name="$icon" class="size-5" />
            </div>
        @endif
    </div>
    
    @if($href)
        <div class="mt-4">
            <a href="{{ $href }}" class="text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                Ver detalles
                <flux:icon name="arrow-right" class="ml-1 inline-block size-3" />
            </a>
        </div>
    @endif
</div>
