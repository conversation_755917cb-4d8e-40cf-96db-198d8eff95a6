<div
    x-data="{
        chartData: @js($data),
        chartType: '{{ $type }}',
        chartOptions: @js($options),
        chart: null,
        init() {
            this.initChart();
            this.$watch('chartData', () => {
                this.updateChart();
            });
        },
        initChart() {
            const ctx = this.$refs.canvas.getContext('2d');
            this.chart = new Chart(ctx, {
                type: this.chartType,
                data: this.prepareData(),
                options: this.chartOptions
            });
        },
        prepareData() {
            if (this.chartType === 'pie' || this.chartType === 'doughnut') {
                return {
                    labels: this.chartData.map(item => item.name),
                    datasets: [{
                        data: this.chartData.map(item => item.count),
                        backgroundColor: this.generateColors(this.chartData.length),
                        borderWidth: 1
                    }]
                };
            } else {
                return {
                    labels: this.chartData.map(item => item.name),
                    datasets: [{
                        label: '{{ $label }}',
                        data: this.chartData.map(item => item.count),
                        backgroundColor: '{{ $color }}',
                        borderColor: '{{ $color }}',
                        borderWidth: 1
                    }]
                };
            }
        },
        updateChart() {
            const newData = this.prepareData();
            this.chart.data = newData;
            this.chart.update();
        },
        generateColors(count) {
            const colors = [
                '#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
                '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
            ];
            
            if (count <= colors.length) {
                return colors.slice(0, count);
            }
            
            // If we need more colors than in our predefined array, generate them
            const result = [...colors];
            for (let i = colors.length; i < count; i++) {
                const r = Math.floor(Math.random() * 255);
                const g = Math.floor(Math.random() * 255);
                const b = Math.floor(Math.random() * 255);
                result.push(`rgb(${r}, ${g}, ${b})`);
            }
            
            return result;
        }
    }"
    class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 {{ $class }}"
>
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $title }}</h3>
        @if(isset($actions))
            <div class="flex space-x-2">
                {{ $actions }}
            </div>
        @endif
    </div>
    
    <div class="relative" style="height: {{ $height }}">
        <canvas x-ref="canvas"></canvas>
    </div>
    
    @if(isset($footer))
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            {{ $footer }}
        </div>
    @endif
</div>
