@props([
    'columns' => 4,
])

@php
    $gridClasses = [
        1 => 'grid-cols-1',
        2 => 'grid-cols-1 md:grid-cols-2',
        3 => 'grid-cols-1 md:grid-cols-3',
        4 => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
        5 => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-5',
        6 => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
    ];
    
    $gridClass = $gridClasses[$columns] ?? $gridClasses[4];
@endphp

<div {{ $attributes->merge(['class' => "grid $gridClass gap-6"]) }}>
    {{ $slot }}
</div>
