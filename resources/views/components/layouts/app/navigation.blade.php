{{-- Componente de navegación unificado para todo el sistema --}}
<div>
    {{-- Navegación para el frontend --}}
    @if(request()->routeIs('home') || request()->routeIs('dashboard') || request()->routeIs('my-group*'))
        <flux:navlist variant="outline">
            <flux:navlist.group heading="Plataforma" class="grid">
                <flux:navlist.item icon="home" :href="route('home')" wire:navigate :current="request()->routeIs('home')">
                    Inicio
                </flux:navlist.item>
                <flux:navlist.item icon="layout-grid" :href="route('dashboard')" wire:navigate :current="request()->routeIs('dashboard')">
                    Dashboard
                </flux:navlist.item>
                <flux:navlist.item icon="user-group" :href="route('1x10.manage')" wire:navigate :current="request()->routeIs('1x10.*')">
                    Mis Personas Asignadas
                </flux:navlist.item>
            </flux:navlist.group>
        </flux:navlist>
    {{-- Navegación para el panel de administración --}}
    @else
        <flux:navlist variant="outline">
            <flux:navlist.group heading="Plataforma" class="grid">
                <flux:navlist.item icon="home" :href="route('admin.index')" wire:navigate :current="request()->routeIs('admin.index')">
                    Dashboard
                </flux:navlist.item>
            </flux:navlist.group>

            <flux:navlist.group heading="🔍 Gestión de Personas" expandable :expanded="request()->routeIs('admin.persons.*')">
                <flux:navlist.item icon="users" :href="route('admin.persons.index')" wire:navigate :current="request()->routeIs('admin.persons.index')">
                    Listado de Personas
                </flux:navlist.item>
                <flux:navlist.item icon="user-plus" :href="route('admin.persons.create')" wire:navigate :current="request()->routeIs('admin.persons.create')">
                    Registrar Persona
                </flux:navlist.item>
                <flux:navlist.item icon="user-group" :href="route('admin.persons.assign')" wire:navigate :current="request()->routeIs('admin.persons.assign')">
                    Asignar 1x10
                </flux:navlist.item>
                <flux:navlist.item icon="magnifying-glass" :href="route('admin.persons.search')" wire:navigate :current="request()->routeIs('admin.persons.search')">
                    Búsqueda Avanzada
                </flux:navlist.item>
            </flux:navlist.group>

            {{-- Sección de Grupos 1x10 eliminada --}}

            {{-- Patrullaje - TEMPORALMENTE DESHABILITADO - ARCHIVOS FALTANTES
            @canany(['view patrol', 'create patrol', 'update patrol', 'delete patrol'])
            <flux:navlist.group heading="🚨 Patrullaje" expandable :expanded="request()->routeIs('admin.patrol.*')">
                <flux:navlist.item icon="shield-check" :href="route('admin.patrol.index')" wire:navigate :current="request()->routeIs('admin.patrol.index')">
                    Grupos de Patrullaje
                </flux:navlist.item>
                @can('create patrol')
                <flux:navlist.item icon="plus-circle" :href="route('admin.patrol.create')" wire:navigate :current="request()->routeIs('admin.patrol.create')">
                    Nuevo Grupo
                </flux:navlist.item>
                @endcan
                @can('view patrol reports')
                <flux:navlist.item icon="document-chart-bar" :href="route('admin.patrol.reports')" wire:navigate :current="request()->routeIs('admin.patrol.reports')">
                    Reportes
                </flux:navlist.item>
                @endcan
            </flux:navlist.group>
            @endcanany
            --}}

            <flux:navlist.group heading="📊 Seguimiento" expandable :expanded="request()->routeIs('admin.tracking.*')">
                <flux:navlist.item icon="chart-bar" :href="route('admin.tracking.index')" wire:navigate :current="request()->routeIs('admin.tracking.index')">
                    Estado de Movilización
                </flux:navlist.item>
                <flux:navlist.item icon="truck" :href="route('admin.tracking.mobilization')" wire:navigate :current="request()->routeIs('admin.tracking.mobilization')">
                    Movilización
                </flux:navlist.item>
                <flux:navlist.item icon="check-circle" :href="route('admin.tracking.mark-voters')" wire:navigate :current="request()->routeIs('admin.tracking.mark-voters')">
                    Marcar Votantes
                </flux:navlist.item>
                <flux:navlist.item icon="phone" :href="route('admin.tracking.contacts')" wire:navigate :current="request()->routeIs('admin.tracking.contacts')">
                    Contactos
                </flux:navlist.item>
                <flux:navlist.item icon="document-chart-bar" :href="route('admin.tracking.reports')" wire:navigate :current="request()->routeIs('admin.tracking.reports')">
                    Reportes
                </flux:navlist.item>
            </flux:navlist.group>

            <flux:navlist.group heading="🗳 Electoral" expandable :expanded="request()->routeIs('admin.electoral.*')">
                <flux:navlist.item icon="home" :href="route('admin.electoral.index')" wire:navigate :current="request()->routeIs('admin.electoral.index')">
                    Dashboard Electoral
                </flux:navlist.item>
                <flux:navlist.item icon="calendar" :href="route('admin.electoral.events')" wire:navigate :current="request()->routeIs('admin.electoral.events*')">
                    Eventos Electorales
                </flux:navlist.item>
                <flux:navlist.item icon="building-office" :href="route('admin.electoral.voting-centers')" wire:navigate :current="request()->routeIs('admin.electoral.voting-centers')">
                    Centros de Votación
                </flux:navlist.item>
                <flux:navlist.item icon="truck" :href="route('admin.electoral.mobilization')" wire:navigate :current="request()->routeIs('admin.electoral.mobilization*')">
                    Movilización
                </flux:navlist.item>
                <flux:navlist.item icon="map" :href="route('admin.electoral.mobilization-map')" wire:navigate :current="request()->routeIs('admin.electoral.mobilization-map')">
                    Mapa de Movilización
                </flux:navlist.item>
                <flux:navlist.item icon="clipboard-document-check" :href="route('admin.electoral.voting-tracker')" wire:navigate :current="request()->routeIs('admin.electoral.voting-tracker')">
                    Seguimiento de Votación
                </flux:navlist.item>
                <flux:navlist.item icon="document-chart-bar" :href="route('admin.electoral.reports')" wire:navigate :current="request()->routeIs('admin.electoral.reports')">
                    Reportes Electorales
                </flux:navlist.item>
            </flux:navlist.group>

            <flux:navlist.group heading="📱 Campañas" expandable :expanded="request()->routeIs('admin.campaigns.*')">
                <flux:navlist.item icon="home" :href="route('admin.campaigns')" wire:navigate :current="request()->routeIs('admin.campaigns')">
                    Dashboard Campañas
                </flux:navlist.item>
{{--                <flux:navlist.item icon="plus-circle" :href="route('admin.campaigns.create')" wire:navigate :current="request()->routeIs('admin.campaigns.create')">--}}
{{--                    Nueva Campaña--}}
{{--                </flux:navlist.item>--}}
                <flux:navlist.item icon="document-chart-bar" :href="route('admin.campaigns.reports')" wire:navigate :current="request()->routeIs('admin.campaigns.reports')">
                    Reportes
                </flux:navlist.item>
            </flux:navlist.group>

            @canany(['view users', 'view roles', 'view permissions'])
                <flux:navlist.group heading="👤 Usuarios y Permisos" expandable :expanded="request()->routeIs('admin.users.*') || request()->routeIs('admin.roles.*') || request()->routeIs('admin.permissions.*')">
                    @can('view users')
                        <flux:navlist.item icon="user" :href="route('admin.users.index')" wire:navigate :current="request()->routeIs('admin.users.*')">
                            {{ __('users.title') }}
                        </flux:navlist.item>
                    @endcan
                    @can('view roles')
                        <flux:navlist.item icon="shield-user" :href="route('admin.roles.index')" wire:navigate :current="request()->routeIs('admin.roles.*')">
                            {{ __('roles.title') }}
                        </flux:navlist.item>
                    @endcan
                    @can('view permissions')
                        <flux:navlist.item icon="shield-check" :href="route('admin.permissions.index')" wire:navigate :current="request()->routeIs('admin.permissions.*')">
                            {{ __('permissions.title') }}
                        </flux:navlist.item>
                    @endcan
                </flux:navlist.group>
            @endcanany

            @can('view analytics')
            <flux:navlist.group heading="📈 Analytics" expandable :expanded="request()->routeIs('admin.analytics')">
                <flux:navlist.item icon="chart-bar" :href="route('admin.analytics')" wire:navigate :current="request()->routeIs('admin.analytics')">
                    Dashboard Analytics
                </flux:navlist.item>
            </flux:navlist.group>
            @endcan

            <flux:navlist.group heading="⚙️ Configuración" expandable :expanded="request()->routeIs('admin.settings.*')">
                <flux:navlist.item icon="cog" :href="route('admin.settings.general')" wire:navigate :current="request()->routeIs('admin.settings.general')">
                    General
                </flux:navlist.item>
                <flux:navlist.item icon="globe-alt" :href="route('admin.settings.locations')" wire:navigate :current="request()->routeIs('admin.settings.locations')">
                    Ubicaciones
                </flux:navlist.item>
                <flux:navlist.item icon="wrench" :href="route('admin.settings.system')" wire:navigate :current="request()->routeIs('admin.settings.system')">
                    Sistema
                </flux:navlist.item>
            </flux:navlist.group>
        </flux:navlist>
    @endif
</div>
