@props([
    'tracking',
    'showPerson' => true,
    'showActions' => true,
])

@php
    $typeColors = [
        'mobilization' => 'blue',
        'voter_mark' => 'green',
        'contact' => 'purple',
        'event' => 'amber',
    ];

    $typeIcons = [
        'mobilization' => 'truck',
        'voter_mark' => 'check-circle',
        'contact' => 'phone',
        'event' => 'calendar',
    ];

    $statusColors = [
        'pending' => 'amber',
        'in_progress' => 'blue',
        'completed' => 'green',
        'cancelled' => 'red',
    ];

    $priorityColors = [
        'low' => 'gray',
        'medium' => 'blue',
        'high' => 'red',
    ];

    $typeColor = $typeColors[$tracking->tracking_type] ?? 'gray';
    $typeIcon = $typeIcons[$tracking->tracking_type] ?? 'document-text';
    $statusColor = $statusColors[$tracking->status] ?? 'gray';
    $priorityColor = $priorityColors[$tracking->priority] ?? 'gray';
@endphp

<div {{ $attributes->merge(['class' => 'border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-sm hover:shadow transition-shadow duration-200']) }}>
    <div class="p-4">
        <div class="flex justify-between items-start mb-3">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-{{ $typeColor }}-100 dark:bg-{{ $typeColor }}-900 flex items-center justify-center text-{{ $typeColor }}-600 dark:text-{{ $typeColor }}-400">
                        <flux:icon :name="$typeIcon" class="h-5 w-5" />
                    </div>
                </div>
                <div class="ml-3">
                    <div class="flex items-center">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ ucfirst(str_replace('_', ' ', $tracking->tracking_type)) }}
                        </h3>
                        <flux:badge variant="{{ $statusColor }}" size="sm" class="ml-2">
                            {{ ucfirst($tracking->status) }}
                        </flux:badge>
                        @if($tracking->priority)
                            <flux:badge variant="{{ $priorityColor }}" size="xs" class="ml-1">
                                {{ ucfirst($tracking->priority) }}
                            </flux:badge>
                        @endif
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $tracking->tracking_date->format('d/m/Y H:i') }}
                        @if($tracking->user)
                            · {{ $tracking->user->name }}
                        @endif
                    </p>
                </div>
            </div>

            @if($showActions)
                <flux:dropdown position="bottom" align="end">
                    <flux:button icon="ellipsis-horizontal" variant="ghost" size="xs" />
                    <flux:menu>
                        <flux:menu.item icon="pencil-square" href="{{ route('admin.tracking.edit', $tracking) }}">
                            Editar
                        </flux:menu.item>
                        <flux:menu.item icon="document-duplicate" wire:click="duplicateTracking({{ $tracking->id }})">
                            Duplicar
                        </flux:menu.item>
                        <flux:menu.item icon="trash" wire:click="deleteTracking({{ $tracking->id }})" class="text-red-600 dark:text-red-400">
                            Eliminar
                        </flux:menu.item>
                    </flux:menu>
                </flux:dropdown>
            @endif
        </div>

        @if($showPerson && $tracking->person)
            <div class="mb-3 flex items-center p-2 rounded-md bg-gray-50 dark:bg-gray-700">
                <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-700 dark:text-primary-300 font-medium">
                    {{ substr($tracking->person->name, 0, 1) }}
                </div>
                <div class="ml-2">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $tracking->person->name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ $tracking->person->cedula }}</div>
                </div>
                <div class="ml-auto">
                    <a href="{{ route('admin.persons.show', $tracking->person) }}" class="text-xs text-primary-600 dark:text-primary-400 hover:underline">
                        Ver perfil
                    </a>
                </div>
            </div>
        @endif

        @if($tracking->notes)
            <div class="mb-3">
                <h4 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Notas</h4>
                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $tracking->notes }}</p>
            </div>
        @endif

        <div class="grid grid-cols-2 gap-2 text-xs">
            @if($tracking->location)
                <div>
                    <span class="text-gray-500 dark:text-gray-400">Ubicación:</span>
                    <span class="text-gray-700 dark:text-gray-300">{{ $tracking->location }}</span>
                </div>
            @endif

            @if($tracking->voting_center_id && $tracking->votingCenter)
                <div>
                    <span class="text-gray-500 dark:text-gray-400">Centro de votación:</span>
                    <span class="text-gray-700 dark:text-gray-300">{{ $tracking->votingCenter->name }}</span>
                </div>
            @endif

            @if($tracking->contact_method)
                <div>
                    <span class="text-gray-500 dark:text-gray-400">Método de contacto:</span>
                    <span class="text-gray-700 dark:text-gray-300">{{ $tracking->contact_method }}</span>
                </div>
            @endif

            @if($tracking->follow_up_date)
                <div>
                    <span class="text-gray-500 dark:text-gray-400">Seguimiento:</span>
                    <span class="text-gray-700 dark:text-gray-300">{{ $tracking->follow_up_date->format('d/m/Y') }}</span>
                </div>
            @endif
        </div>

        @if($tracking->response)
            <div class="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                <h4 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Respuesta</h4>
                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $tracking->response }}</p>
            </div>
        @endif
    </div>
</div>
