@props([
    'event',
    'showActions' => true,
])

@php
    $typeColors = [
        'rally' => 'blue',
        'meeting' => 'green',
        'canvassing' => 'purple',
        'debate' => 'amber',
        'voting_day' => 'red',
        'training' => 'indigo',
        'other' => 'gray',
    ];
    
    $typeIcons = [
        'rally' => 'megaphone',
        'meeting' => 'user-group',
        'canvassing' => 'map',
        'debate' => 'chat-bubble-left-right',
        'voting_day' => 'check-badge',
        'training' => 'academic-cap',
        'other' => 'calendar',
    ];
    
    $statusColors = [
        'scheduled' => 'blue',
        'in_progress' => 'amber',
        'completed' => 'green',
        'cancelled' => 'red',
    ];
    
    $typeColor = $typeColors[$event->type] ?? 'gray';
    $typeIcon = $typeIcons[$event->type] ?? 'calendar';
    $statusColor = $statusColors[$event->status] ?? 'gray';
    
    $isPast = $event->end_date && $event->end_date < now();
    $isUpcoming = $event->start_date && $event->start_date > now();
    $isInProgress = $event->start_date && $event->start_date <= now() && $event->end_date && $event->end_date >= now();
@endphp

<div {{ $attributes->merge(['class' => 'border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-sm hover:shadow transition-shadow duration-200']) }}>
    <div class="p-4">
        <div class="flex justify-between items-start mb-3">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-{{ $typeColor }}-100 dark:bg-{{ $typeColor }}-900 flex items-center justify-center text-{{ $typeColor }}-600 dark:text-{{ $typeColor }}-400">
                        <flux:icon :name="$typeIcon" class="h-5 w-5" />
                    </div>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ $event->name }}</h3>
                    <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <flux:badge variant="{{ $statusColor }}" size="xs" class="mr-1">
                            {{ ucfirst($event->status) }}
                        </flux:badge>
                        <span>{{ ucfirst(str_replace('_', ' ', $event->type)) }}</span>
                    </div>
                </div>
            </div>
            
            @if($showActions)
                <flux:dropdown position="bottom" align="end">
                    <flux:button icon="ellipsis-horizontal" variant="ghost" size="xs" />
                    <flux:menu>
                        <flux:menu.item icon="eye" href="{{ route('admin.electoral.events.show', $event) }}">
                            Ver detalles
                        </flux:menu.item>
                        <flux:menu.item icon="pencil-square" href="{{ route('admin.electoral.events.edit', $event) }}">
                            Editar
                        </flux:menu.item>
                        <flux:menu.item icon="user-plus" href="{{ route('admin.electoral.events.attendees', $event) }}">
                            Gestionar asistentes
                        </flux:menu.item>
                        <flux:menu.item icon="qr-code" href="{{ route('admin.electoral.events.qr', $event) }}">
                            Código QR
                        </flux:menu.item>
                        @if($event->status !== 'cancelled')
                            <flux:menu.item icon="x-mark" wire:click="cancelEvent({{ $event->id }})" class="text-red-600 dark:text-red-400">
                                Cancelar evento
                            </flux:menu.item>
                        @endif
                    </flux:menu>
                </flux:dropdown>
            @endif
        </div>
        
        <div class="mb-3">
            @if($event->description)
                <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">{{ Str::limit($event->description, 100) }}</p>
            @endif
            
            <div class="grid grid-cols-2 gap-2 text-xs">
                <div class="flex items-center">
                    <flux:icon name="calendar" class="h-3 w-3 mr-1 text-gray-500 dark:text-gray-400" />
                    <span class="text-gray-700 dark:text-gray-300">
                        {{ $event->start_date->format('d/m/Y H:i') }}
                    </span>
                </div>
                
                @if($event->end_date)
                    <div class="flex items-center">
                        <flux:icon name="clock" class="h-3 w-3 mr-1 text-gray-500 dark:text-gray-400" />
                        <span class="text-gray-700 dark:text-gray-300">
                            {{ $event->end_date->format('d/m/Y H:i') }}
                        </span>
                    </div>
                @endif
                
                @if($event->location)
                    <div class="flex items-center col-span-2">
                        <flux:icon name="map-pin" class="h-3 w-3 mr-1 text-gray-500 dark:text-gray-400" />
                        <span class="text-gray-700 dark:text-gray-300">{{ $event->location }}</span>
                    </div>
                @endif
                
                @if($event->address)
                    <div class="flex items-center col-span-2">
                        <flux:icon name="home" class="h-3 w-3 mr-1 text-gray-500 dark:text-gray-400" />
                        <span class="text-gray-700 dark:text-gray-300">{{ $event->address }}</span>
                    </div>
                @endif
            </div>
        </div>
        
        <div class="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            <div class="flex justify-between items-center mb-1">
                <div class="text-xs text-gray-500 dark:text-gray-400">Asistencia</div>
                <div class="text-xs font-medium">{{ $event->attendance_count }} / {{ $event->capacity ?: '∞' }}</div>
            </div>
            @if($event->capacity > 0)
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div class="bg-{{ $statusColor }}-600 dark:bg-{{ $statusColor }}-500 h-1.5 rounded-full" style="width: {{ $event->attendance_percentage }}%"></div>
                </div>
            @endif
        </div>
    </div>
</div>
