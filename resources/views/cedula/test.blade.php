<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consulta de Cédula</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">Consulta de Cédula Venezolana</div>
                    <div class="card-body">
                        <form id="cedula-form">
                            <div class="mb-3">
                                <label for="cedula" class="form-label">Número de Cédula</label>
                                <input type="number" class="form-control" id="cedula" name="cedula" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Consultar</button>
                        </form>

                        <div class="mt-4">
                            <h5>Resultado:</h5>
                            <div id="resultado" class="p-3 border rounded">
                                <p class="text-muted">Los resultados se mostrarán aquí...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('cedula-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const cedula = document.getElementById('cedula').value;
            const resultadoDiv = document.getElementById('resultado');
            
            resultadoDiv.innerHTML = '<p class="text-muted">Consultando...</p>';
            
            fetch(`/api/cedula/consultar?cedula=${cedula}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = '<div class="text-success">';
                        html += '<h6>Datos encontrados:</h6>';
                        html += '<ul>';
                        for (const [key, value] of Object.entries(data.data)) {
                            html += `<li><strong>${key}:</strong> ${value}</li>`;
                        }
                        html += '</ul></div>';
                        resultadoDiv.innerHTML = html;
                    } else {
                        resultadoDiv.innerHTML = `<p class="text-danger">Error: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    resultadoDiv.innerHTML = `<p class="text-danger">Error en la consulta: ${error.message}</p>`;
                });
        });
    </script>
</body>
</html>
