import './bootstrap';
import './charts';

// Initialize tooltips, popovers, etc.
document.addEventListener('DOMContentLoaded', function() {
    // Add any global JavaScript initialization here

    // Initialize Livewire Alpine.js components
    window.addEventListener('livewire:load', function() {
        // Add any Livewire-specific initialization here
    });
});

// Handle dark mode toggle
document.addEventListener('flux:appearance-changed', function(e) {
    const isDark = e.detail.appearance === 'dark';
    document.documentElement.classList.toggle('dark', isDark);
});
