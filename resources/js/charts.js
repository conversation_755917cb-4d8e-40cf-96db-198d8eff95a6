/**
 * Initialize charts on the dashboard
 */
document.addEventListener('DOMContentLoaded', function() {
    // Set default Chart.js options
    if (window.Chart) {
        Chart.defaults.font.family = "'Instrument Sans', 'Helvetica', 'Arial', sans-serif";
        Chart.defaults.color = document.documentElement.classList.contains('dark') 
            ? '#94a3b8' // slate-400 in dark mode
            : '#64748b'; // slate-500 in light mode
        
        Chart.defaults.borderColor = document.documentElement.classList.contains('dark')
            ? '#334155' // slate-700 in dark mode
            : '#e2e8f0'; // slate-200 in light mode
            
        Chart.defaults.scale.grid.display = false;
        Chart.defaults.plugins.tooltip.backgroundColor = document.documentElement.classList.contains('dark')
            ? '#1e293b' // slate-800 in dark mode
            : '#ffffff'; // white in light mode
        Chart.defaults.plugins.tooltip.titleColor = document.documentElement.classList.contains('dark')
            ? '#e2e8f0' // slate-200 in dark mode
            : '#1e293b'; // slate-800 in light mode
        Chart.defaults.plugins.tooltip.bodyColor = document.documentElement.classList.contains('dark')
            ? '#cbd5e1' // slate-300 in dark mode
            : '#334155'; // slate-700 in light mode
        Chart.defaults.plugins.tooltip.borderColor = document.documentElement.classList.contains('dark')
            ? '#475569' // slate-600 in dark mode
            : '#e2e8f0'; // slate-200 in light mode
        Chart.defaults.plugins.tooltip.borderWidth = 1;
        Chart.defaults.plugins.tooltip.padding = 10;
        Chart.defaults.plugins.tooltip.cornerRadius = 6;
        
        // Listen for theme changes
        document.addEventListener('flux:appearance-changed', function(e) {
            const isDark = e.detail.appearance === 'dark';
            
            // Update chart colors
            Chart.defaults.color = isDark ? '#94a3b8' : '#64748b';
            Chart.defaults.borderColor = isDark ? '#334155' : '#e2e8f0';
            Chart.defaults.plugins.tooltip.backgroundColor = isDark ? '#1e293b' : '#ffffff';
            Chart.defaults.plugins.tooltip.titleColor = isDark ? '#e2e8f0' : '#1e293b';
            Chart.defaults.plugins.tooltip.bodyColor = isDark ? '#cbd5e1' : '#334155';
            Chart.defaults.plugins.tooltip.borderColor = isDark ? '#475569' : '#e2e8f0';
            
            // Update all charts
            Chart.instances.forEach(chart => {
                chart.update();
            });
        });
    }
});

/**
 * Create a responsive chart
 * 
 * @param {string} selector - The canvas element selector
 * @param {string} type - The chart type (bar, line, pie, doughnut, etc.)
 * @param {object} data - The chart data
 * @param {object} options - The chart options
 * @returns {Chart|null} - The Chart.js instance or null if Chart.js is not available
 */
function createChart(selector, type, data, options = {}) {
    if (!window.Chart) {
        console.error('Chart.js is not loaded');
        return null;
    }
    
    const canvas = document.querySelector(selector);
    if (!canvas) {
        console.error(`Canvas element not found: ${selector}`);
        return null;
    }
    
    // Merge default options with provided options
    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
    };
    
    const chartOptions = { ...defaultOptions, ...options };
    
    return new Chart(canvas.getContext('2d'), {
        type,
        data,
        options: chartOptions
    });
}

/**
 * Update a chart with new data
 * 
 * @param {Chart} chart - The Chart.js instance
 * @param {object} newData - The new chart data
 * @param {boolean} animate - Whether to animate the update
 */
function updateChart(chart, newData, animate = true) {
    if (!chart) {
        console.error('Chart instance is required');
        return;
    }
    
    // Update datasets
    chart.data.labels = newData.labels || chart.data.labels;
    
    if (newData.datasets) {
        chart.data.datasets.forEach((dataset, i) => {
            const newDataset = newData.datasets[i];
            if (newDataset) {
                Object.keys(newDataset).forEach(key => {
                    dataset[key] = newDataset[key];
                });
            }
        });
    }
    
    // Update the chart
    chart.update(animate ? undefined : 0);
}

// Export functions for use in other files
window.createChart = createChart;
window.updateChart = updateChart;
