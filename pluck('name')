
   InvalidArgumentException 

  Unexpected end of input

  at vendor\psy\psysh\src\Shell.php:891
    887▕ 
    888▕         if (!$this->hasValidCode()) {
    889▕             $this->popCodeStack();
    890▕ 
  ➜ 891▕             throw new \InvalidArgumentException('Unexpected end of input');
    892▕         }
    893▕     }
    894▕ 
    895▕     /**

  1   vendor\psy\psysh\src\Shell.php:1390
      Psy\Shell::setCode("Spatie\Permission\Models\Role::all()-")

  2   vendor\laravel\tinker\src\Console\TinkerCommand.php:76
      Psy\Shell::execute("Spatie\Permission\Models\Role::all()-")

