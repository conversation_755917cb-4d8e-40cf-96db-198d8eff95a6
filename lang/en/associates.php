<?php

return [
    // General
    'title' => 'Associates',
    'title_description' => 'Manage associates in the application',
    'search_placeholder' => 'Search by ID, name, document, email...',
    'no_associates_found' => 'No associates found',

    // Statistics
    'statistics' => 'Statistics',
    'total_associates' => 'Total Associates',
    'active_associates' => 'Active Associates',
    'inactive_associates' => 'Inactive Associates',
    'new_associates' => 'New Associates',

    // Table headers
    'id' => 'ID',
    'document' => 'Document',
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'status' => 'Status',
    'actions' => 'Actions',

    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'all' => 'All',

    // Actions
    'view' => 'View',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'create' => 'Create Associate',
    'edit_associate' => 'Edit Associate',
    'delete_associate' => 'Delete Associate',
    'delete_confirmation' => 'Are you sure you want to delete this associate?',
    'delete_warning' => 'This action cannot be undone.',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'update' => 'Update',

    // Tabs
    'personal_data' => 'Personal Data',
    'work_data' => 'Work Data',
    'bank_data' => 'Bank Data',
    'family_group' => 'Family Group',

    // Personal data
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'personal_phone' => 'Personal Phone',
    'office_phone' => 'Office Phone',
    'contact_phone' => 'Contact Phone',
    'gender' => 'Gender',
    'address' => 'Address',
    'marital_status' => 'Marital Status',
    'male' => 'Male',
    'female' => 'Female',
    'single' => 'Single',
    'married' => 'Married',
    'divorced' => 'Divorced',
    'widowed' => 'Widowed',

    // Work data
    'file_number' => 'File Number',
    'payroll_code' => 'Payroll Code',
    'admission_date' => 'Admission Date',
    'base_salary' => 'Base Salary',
    'additional_income' => 'Additional Income',
    'person_type' => 'Person Type',
    'department' => 'Department',

    // Bank data
    'bank' => 'Bank',
    'account_number' => 'Account Number',
    'select_bank' => 'Select a bank',

    // Family
    'add_family_member' => 'Add Family Member',
    'edit_family_member' => 'Edit Family Member',
    'delete_family_member' => 'Delete Family Member',
    'complete_family_data' => 'Complete the family member data',
    'modify_family_data' => 'Modify the family member data',
    'document_type' => 'Document Type',
    'occupation' => 'Occupation',
    'relationship' => 'Relationship',
    'family_type' => 'Family Type',
    'home_phone' => 'Home Phone',
    'mobile_phone' => 'Mobile Phone',

    // Contact information
    'contact_information' => 'Contact Information',
    'not_specified' => 'Not specified',
    'phones' => 'Phones',

    // Messages
    'personal_data_updated' => 'Personal data updated successfully',
    'work_data_updated' => 'Work data updated successfully',
    'bank_data_updated' => 'Bank data updated successfully',
    'family_member_added' => 'Family member added successfully',
    'family_member_updated' => 'Family member updated successfully',
    'family_member_deleted' => 'Family member deleted successfully',
    'associate_deleted' => 'Associate deleted successfully',

    // Validation
    'first_name_required' => 'First name is required',
    'last_name_required' => 'Last name is required',
    'document_required' => 'Document is required',
    'email_required' => 'Email is required',
    'email_invalid' => 'Email is invalid',
    'phone_required' => 'Phone is required',
    'bank_required' => 'Bank is required',
    'account_number_required' => 'Account number is required',
    'account_number_max' => 'Account number cannot have more than 20 characters',
    'account_number_min' => 'Account number must have at least 20 characters',
];
