<?php

return [
    // Titles and descriptions
    'title' => 'Location Settings',
    'subtitle' => 'Manage states, municipalities and parishes',
    'home' => 'Home',
    'settings' => 'Settings',
    'locations' => 'Locations',

    // Statistics
    'states' => 'States',
    'municipalities' => 'Municipalities',
    'parishes' => 'Parishes',
    'featured' => 'Featured',
    'featured_municipality' => 'Featured Municipality',
    'average_per_state' => 'Average per state',
    'average_per_municipality' => 'Average per municipality',

    // Charts
    'states_with_most_municipalities' => 'States with Most Municipalities',
    'municipalities_with_most_parishes' => 'Municipalities with Most Parishes',
    'location_distribution' => 'Location Distribution',
    'location_summary' => 'Location Summary',
    'location_summary_description' => 'The system manages the complete geographical structure of the country, organized into states, municipalities and parishes.',

    // Actions
    'search_locations' => 'Search locations...',
    'filter_by_state' => 'Filter by state',
    'filter_by_municipality' => 'Filter by municipality',
    'all_states' => 'All states',
    'all_municipalities' => 'All municipalities',
    'export' => 'Export',
    'new_state' => 'New State',
    'new_municipality' => 'New Municipality',
    'new_parish' => 'New Parish',

    // Tables
    'name' => 'Name',
    'code' => 'Code',
    'state' => 'State',
    'municipality' => 'Municipality',
    'actions' => 'Actions',
    'no_states_found' => 'No states found.',
    'no_municipalities_found' => 'No municipalities found.',
    'no_parishes_found' => 'No parishes found.',

    // Forms
    'edit_state' => 'Edit State',
    'create_state' => 'New State',
    'update_state_info' => 'Update the state information.',
    'create_state_info' => 'Complete the information to create a new state.',
    'state_name' => 'State name',
    'state_code' => 'State code',
    'code_unique_hint' => 'The code must be unique for each state',

    'edit_municipality' => 'Edit Municipality',
    'create_municipality' => 'New Municipality',
    'update_municipality_info' => 'Update the municipality information.',
    'create_municipality_info' => 'Complete the information to create a new municipality.',
    'select_state' => 'Select a state',
    'municipality_name' => 'Municipality name',
    'municipality_code' => 'Municipality code',

    'edit_parish' => 'Edit Parish',
    'create_parish' => 'New Parish',
    'update_parish_info' => 'Update the parish information.',
    'create_parish_info' => 'Complete the information to create a new parish.',
    'parish_name' => 'Parish name',
    'parish_code' => 'Parish code',

    // Buttons
    'cancel' => 'Cancel',
    'save' => 'Save',
    'saving' => 'Saving...',
    'edit' => 'Edit',
    'delete' => 'Delete',

    // Messages
    'state_created' => 'State created successfully',
    'state_updated' => 'State updated successfully',
    'state_deleted' => 'State deleted successfully',
    'municipality_created' => 'Municipality created successfully',
    'municipality_updated' => 'Municipality updated successfully',
    'municipality_deleted' => 'Municipality deleted successfully',
    'parish_created' => 'Parish created successfully',
    'parish_updated' => 'Parish updated successfully',
    'parish_deleted' => 'Parish deleted successfully',
    'cannot_delete_state' => 'Cannot delete the state because it has associated municipalities.',
    'cannot_delete_municipality' => 'Cannot delete the municipality because it has associated parishes.',
    'confirm_delete_state' => 'Are you sure you want to delete this state?',
    'confirm_delete_municipality' => 'Are you sure you want to delete this municipality?',
    'confirm_delete_parish' => 'Are you sure you want to delete this parish?',
];
