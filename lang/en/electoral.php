<?php

return [
    // General
    'title' => 'Electoral Management',
    'title_description' => 'Manage electoral processes and voting',
    
    // Voting Centers
    'voting_centers' => 'Voting Centers',
    'voting_center' => 'Voting Center',
    'search_voting_centers' => 'Search by name, code, address...',
    'no_voting_centers' => 'No voting centers found',
    'add_voting_center' => 'Add Voting Center',
    'edit_voting_center' => 'Edit Voting Center',
    'delete_voting_center' => 'Delete Voting Center',
    'code' => 'Code',
    'name' => 'Name',
    'address' => 'Address',
    'parish' => 'Parish',
    'municipality' => 'Municipality',
    'state' => 'State',
    'tables' => 'Tables',
    'voters' => 'Voters',
    
    // Mobilization
    'mobilization' => 'Mobilization',
    'search_group' => 'Enter a search term',
    'no_groups_found' => 'No groups found',
    'coordinator' => 'Coordinator',
    'search_coordinator' => 'Search coordinator by name or ID',
    'search_coordinator_placeholder' => 'Search coordinator...',
    
    // Voting Tracker
    'voting_tracker' => 'Voting Tracker',
    'voting_process' => 'Voting Process',
    'participation' => 'Participation',
    'total_voters' => 'Total Voters',
    'voters_participated' => 'Voters Participated',
    'participation_percentage' => 'Participation Percentage',
    'track_voting' => 'Track Voting',
    'add_participation' => 'Add Participation',
    'edit_participation' => 'Edit Participation',
    'delete_participation' => 'Delete Participation',
    'time' => 'Time',
    'voters_count' => 'Voters Count',
    'cumulative' => 'Cumulative',
    'notes' => 'Notes',
    
    // Messages
    'voting_center_created' => 'Voting center created successfully',
    'voting_center_updated' => 'Voting center updated successfully',
    'voting_center_deleted' => 'Voting center deleted successfully',
    'participation_added' => 'Participation added successfully',
    'participation_updated' => 'Participation updated successfully',
    'participation_deleted' => 'Participation deleted successfully',
];
