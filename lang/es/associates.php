<?php

return [
    // General
    'title' => 'Asociados',
    'title_description' => 'Administrar asociados en la aplicación',
    'search_placeholder' => 'Buscar por ID, nombre, documento, email...',
    'no_associates_found' => 'No se encontraron asociados',

    // Statistics
    'statistics' => 'Estadísticas',
    'total_associates' => 'Total de Asociados',
    'active_associates' => 'Asociados Activos',
    'inactive_associates' => 'Asociados Inactivos',
    'new_associates' => 'Nuevos Asociados',

    // Table headers
    'id' => 'ID',
    'document' => 'Documento',
    'name' => 'Nombre',
    'email' => 'Correo',
    'phone' => 'Teléfono',
    'status' => 'Estado',
    'actions' => 'Acciones',

    // Status
    'active' => 'Activo',
    'inactive' => 'Inactivo',
    'all' => 'Todos',

    // Actions
    'view' => 'Ver',
    'edit' => 'Editar',
    'delete' => 'Eliminar',
    'create' => 'Crear <PERSON>ociado',
    'edit_associate' => 'Editar Asociado',
    'delete_associate' => 'Eliminar Asociado',
    'delete_confirmation' => '¿Estás seguro de que deseas eliminar este asociado?',
    'delete_warning' => 'Esta acción no se puede deshacer.',
    'cancel' => 'Cancelar',
    'save' => 'Guardar',
    'update' => 'Actualizar',

    // Tabs
    'personal_data' => 'Datos Personales',
    'work_data' => 'Datos Laborales',
    'bank_data' => 'Datos Bancarios',
    'family_group' => 'Grupo Familiar',

    // Personal data
    'first_name' => 'Nombres',
    'last_name' => 'Apellidos',
    'document_number' => 'Número de Documento',
    'birth_date' => 'Fecha de Nacimiento',
    'personal_phone' => 'Teléfono Personal',
    'office_phone' => 'Teléfono de Oficina',
    'contact_phone' => 'Teléfono de Contacto',
    'gender' => 'Género',
    'address' => 'Dirección',
    'marital_status' => 'Estado Civil',
    'male' => 'Masculino',
    'female' => 'Femenino',
    'single' => 'Soltero/a',
    'married' => 'Casado/a',
    'divorced' => 'Divorciado/a',
    'widowed' => 'Viudo/a',

    // Work data
    'file_number' => 'Número de Expediente',
    'payroll_code' => 'Código de Nómina',
    'admission_date' => 'Fecha de Ingreso',
    'base_salary' => 'Sueldo Base',
    'additional_income' => 'Ingresos Adicionales',
    'person_type' => 'Tipo de Persona',
    'department' => 'Departamento',

    // Bank data
    'bank' => 'Banco',
    'account_number' => 'Número de Cuenta',
    'select_bank' => 'Seleccione un banco',

    // Family
    'add_family_member' => 'Agregar Familiar',
    'edit_family_member' => 'Editar Familiar',
    'delete_family_member' => 'Eliminar Familiar',
    'complete_family_data' => 'Complete los datos del familiar',
    'modify_family_data' => 'Modifique los datos del familiar',
    'document_type' => 'Tipo de Documento',
    'occupation' => 'Ocupación',
    'relationship' => 'Parentesco',
    'family_type' => 'Tipo de Familiar',
    'home_phone' => 'Teléfono de Casa',
    'mobile_phone' => 'Teléfono Móvil',

    // Contact information
    'contact_information' => 'Información de Contacto',
    'not_specified' => 'No especificado',
    'phones' => 'Teléfonos',

    // Messages
    'personal_data_updated' => 'Datos personales actualizados correctamente',
    'work_data_updated' => 'Datos laborales actualizados correctamente',
    'bank_data_updated' => 'Datos bancarios actualizados correctamente',
    'family_member_added' => 'Familiar agregado correctamente',
    'family_member_updated' => 'Familiar actualizado correctamente',
    'family_member_deleted' => 'Familiar eliminado correctamente',
    'associate_deleted' => 'Asociado eliminado con éxito',

    // Validation
    'first_name_required' => 'El nombre es requerido',
    'last_name_required' => 'El apellido es requerido',
    'document_required' => 'El documento es requerido',
    'email_required' => 'El correo es requerido',
    'email_invalid' => 'El correo es inválido',
    'phone_required' => 'El teléfono es requerido',
    'bank_required' => 'El banco es requerido',
    'account_number_required' => 'El número de cuenta es requerido',
    'account_number_max' => 'El número de cuenta no puede tener más de 20 caracteres',
    'account_number_min' => 'El número de cuenta debe tener al menos 20 caracteres',
];
