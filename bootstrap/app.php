<?php

use App\Http\Middleware\Localization;
use App\Http\Middleware\RequireLeader1x10Role;
use App\Http\Middleware\RequireAdminOrLeader1x10Role;
use App\Http\Middleware\SecurityHeaders;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        api: __DIR__.'/../routes/api.php',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web([
            Localization::class,
            SecurityHeaders::class,
        ]);

        // Register the middleware aliases
        $middleware->alias([
            'leader1x10' => RequireLeader1x10Role::class,
            'admin_or_leader1x10' => RequireAdminOrLeader1x10Role::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
