{"$schema": "https://getcomposer.org/schema.json", "name": "mortenebak/tallstarter", "type": "project", "description": "An oppinionated Laravel Starter Kit based on the Official Laravel Livewire Starter Kit", "keywords": ["laravel", "framework", "starter kit", "tall stack", "livewire"], "license": "MIT", "require": {"php": "^8.2|^8.3", "barryvdh/laravel-dompdf": "^3.1", "jantinnerezo/livewire-alert": "^3.0", "laravel/framework": "^12.0", "laravel/pint": "^1.21", "laravel/tinker": "^2.10.1", "livewire/flux": "^2.0", "livewire/flux-pro": "^2.1", "livewire/volt": "^1.6.7", "maatwebsite/excel": "^3.1", "owen-it/laravel-auditing": "^14.0", "rappasoft/laravel-livewire-tables": "^3.7", "spatie/laravel-permission": "^6.15", "wire-elements/modal": "^2.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.1", "laravel-lang/common": "^6.7", "laravel/pail": "^1.2.2", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.7", "pestphp/pest-plugin-drift": "^3.0", "pestphp/pest-plugin-laravel": "^3.1", "pestphp/pest-plugin-livewire": "^3.0", "rector/rector": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi", "@php -r \"file_exists('.env') || copy('.env.example', '.env');\"", "@php artisan key:generate", "@php artisan db:seed", "@php artisan app:create-super-admin"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test:pest": "./vendor/bin/pest -p", "test:pint": "./vendor/bin/pint", "test:rector": "./vendor/bin/rector --dry-run", "test:static": "./vendor/bin/phpstan analyse --memory-limit=2G", "review": ["@test:pint", "@test:static", "@test:rector", "@test:pest"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}