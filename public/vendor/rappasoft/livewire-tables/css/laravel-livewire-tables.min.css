.laravel-livewire-tables-highlight{border-style:solid!important;border-top-width:2px!important;border-bottom-width:2px!important;border-color:var(--rappasoft-table-highlight-color,rgb(255 255 255))!important}.laravel-livewire-tables-cursor{cursor:pointer}.laravel-livewire-tables-btn-tiny{width:.5em;height:.5em}.laravel-livewire-tables-btn-smaller{width:1em;height:1em}.laravel-livewire-tables-btn-small{width:1.2em;height:1.2em}.laravel-livewire-tables-btn-md{width:1.3em;height:1.3em}.laravel-livewire-tables-btn-lg{width:1.4em;height:1.4em}.laravel-livewire-tables-highlight-top{border-style:solid!important;border-top-width:2px!important;border-bottom-width:0!important;border-color:var(--rappasoft-table-top-highlight-color,var(--rappasoft-table-highlight-color,rgb(255 255 255)))!important}.laravel-livewire-tables-highlight-bottom{border-style:solid!important;border-top-width:0!important;border-bottom-width:2px!important;border-color:var(--rappasoft-table-bottom-highlight-color,var(--rappasoft-table-highlight-color,rgb(255 255 255)))!important}.laravel-livewire-tables-reorderingMinimised{width:0}.laravel-livewire-table-dragging{opacity:.5!important}.range-slider.grad{--progress-shadow:2px 2px 4px rgba(0, 0, 0, 0.2) inset;--progress-flll-shadow:var(--progress-shadow);--fill-color:linear-gradient(to right, LightCyan, var(--primary-color, #0366d6));--thumb-shadow:0 0 4px rgba(0, 0, 0, 0.3),-3px 9px 9px rgba(255, 241, 241, 0.33) inset,-1px 3px 2px rgba(255, 255, 255, 0.33) inset,0 0 0 99px var(--primary-color, #0366d6) inset}.range-slider.grad input:hover{--thumb-transform:scale(1.2)}.range-slider.grad input:active{--thumb-shadow:inherit;--thumb-transform:scale(1)}.range-slider.flat{--thumb-size:25px;--track-height:calc(var(--thumb-size) / 3);--progress-shadow:none;--progress-flll-shadow:none;--thumb-shadow:0 0 0 7px var(--primary-color, #0366d6) inset,0 0 0 99px white inset;--thumb-shadow-hover:0 0 0 9px var(--primary-color, #0366d6) inset,0 0 0 99px white inset;--thumb-shadow-active:0 0 0 13px var(--primary-color, #0366d6) inset}.range-slider{--value-offset-y:var(--ticks-gap);--value-background:transparent;--value-font:700 12px/1 Arial;--progress-radius:20px;--track-height:calc(var(--thumb-size) / 2);--min-max-opacity:1;--min-max-x-offset:10%;--thumb-size:22px;--thumb-shadow:0 0 3px rgba(0, 0, 0, 0.4),0 0 1px rgba(0, 0, 0, 0.5) inset,0 0 0 99px var(--thumb-color, white) inset;--thumb-shadow-active:0 0 0 calc(var(--thumb-size) / 4) inset var(--thumb-color, white),0 0 0 99px var(--primary-color, #0366d6) inset,0 0 3px rgba(0, 0, 0, 0.4);--thumb-shadow-hover:var(--thumb-shadow);--ticks-thickness:1px;--ticks-height:5px;--ticks-gap:var(--ticks-height, 0);--step:1;--ticks-count:Calc(var(--max) - var(--min))/var(--step);--maxTicksAllowed:30;--too-many-ticks:Min(1, Max(var(--ticks-count) - var(--maxTicksAllowed), 0));--x-step:Max(var(--step), var(--too-many-ticks) * (var(--max) - var(--min)));--tickInterval:100/((var(--max) - var(--min)) / var(--step)) * var(--tickEvery, 1);--tickIntervalPerc:calc((100% - var(--thumb-size)) / ((var(--max) - var(--min)) / var(--x-step)) * var(--tickEvery, 1));--value-a:Clamp(var(--min), var(--value, 0), var(--max));--value-b:var(--value, 0);--text-value-a:var(--text-value, "");--completed-a:calc((var(--value-a) - var(--min)) / (var(--max) - var(--min)) * 100);--completed-b:calc((var(--value-b) - var(--min)) / (var(--max) - var(--min)) * 100);--ca:Min(var(--completed-a), var(--completed-b));--cb:Max(var(--completed-a), var(--completed-b));--thumbs-too-close:Clamp(-1, 1000 * (Min(1, Max(var(--cb) - var(--ca) - 5, -1)) + 0.001), 1);--thumb-close-to-min:Min(1, Max(var(--ca) - 2, 0));--thumb-close-to-max:Min(1, Max(98 - var(--cb), 0));display:inline-block;height:max(var(--track-height),var(--thumb-size));background:linear-gradient(to right,var(--ticks-color,silver) var(--ticks-thickness),transparent 1px) repeat-x;background-size:var(--tickIntervalPerc) var(--ticks-height);background-position-x:calc(var(--thumb-size)/ 2 - var(--ticks-thickness)/ 2);background-position-y:var(--flip-y,bottom);padding-bottom:var(--flip-y,var(--ticks-gap));padding-top:calc(var(--flip-y) * var(--ticks-gap));position:relative;z-index:1}.range-slider::after,.range-slider::before{--offset:calc(var(--thumb-size) / 2);content:counter(x);display:var(--show-min-max,block);font:var(--min-max-font, 12px Arial);position:absolute;bottom:var(--flip-y,-2.5ch);top:calc(-2.5ch * var(--flip-y));opacity:clamp(0, var(--at-edge), var(--min-max-opacity));transform:translateX(calc(var(--min-max-x-offset) * var(--before,-1) * -1)) scale(var(--at-edge));pointer-events:none}.dark .range-slider::after,.dark .range-slider::before,.dark .range-slider>input+output::after,.dark .range-slider>input:first-of-type+output::after{color:#fff}.range-slider::before{--before:1;counter-reset:x var(--min);left:var(--offset)}.range-slider::after{counter-reset:x var(--max);right:var(--offset)}.range-slider__progress::after,.range-slider__progress::before{content:"";top:0;right:0;bottom:0;border-radius:inherit;left:0}.range-slider__values{position:relative;top:50%;line-height:0;text-align:justify;width:100%;pointer-events:none;margin:0 auto;z-index:5}.range-slider__values::after{content:"";width:100%;display:inline-block;height:0;background:red}.range-slider__progress{--start-end:calc(var(--thumb-size) / 2);--clip-end:calc(100% - (var(--cb)) * 1%);--clip-start:calc(var(--ca) * 1%);--clip:inset(-20px var(--clip-end) -20px var(--clip-start));position:absolute;left:var(--start-end);right:var(--start-end);top:calc(var(--ticks-gap) * var(--flip-y,0) + var(--thumb-size)/ 2 - var(--track-height)/ 2);height:calc(var(--track-height));background:var(--progress-background,#eee);pointer-events:none;z-index:-1;border-radius:var(--progress-radius)}.range-slider__progress::before{position:absolute;-webkit-clip-path:var(--clip);clip-path:var(--clip);background:var(--fill-color,#0366d6);box-shadow:var(--progress-flll-shadow);z-index:1}.range-slider__progress::after{position:absolute;box-shadow:var(--progress-shadow);pointer-events:none}.range-slider>input{-webkit-appearance:none;width:100%;height:var(--thumb-size);margin:0;position:absolute;left:0;top:calc(50% - Max(var(--track-height),var(--thumb-size))/ 2 + calc(var(--ticks-gap)/ 2 * var(--flip-y,-1)));cursor:-webkit-grab;cursor:grab;outline:0;background:0 0}.range-slider>input:not(:only-of-type){pointer-events:none}.range-slider>input::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;height:var(--thumb-size);width:var(--thumb-size);transform:var(--thumb-transform);border-radius:var(--thumb-radius,50%);background:var(--thumb-color,#fff);box-shadow:var(--thumb-shadow);border:none;pointer-events:auto;-webkit-transition:.1s;transition:.1s}.range-slider>input::-moz-range-thumb{-moz-appearance:none;appearance:none;height:var(--thumb-size);width:var(--thumb-size);transform:var(--thumb-transform);border-radius:var(--thumb-radius,50%);background:var(--thumb-color,#fff);box-shadow:var(--thumb-shadow);border:none;pointer-events:auto;-moz-transition:.1s;transition:.1s}.range-slider>input::-ms-thumb{appearance:none;height:var(--thumb-size);width:var(--thumb-size);transform:var(--thumb-transform);border-radius:var(--thumb-radius,50%);background:var(--thumb-color,#fff);box-shadow:var(--thumb-shadow);border:none;pointer-events:auto;-ms-transition:.1s;transition:.1s}.range-slider>input:hover{--thumb-shadow:var(--thumb-shadow-hover)}.range-slider>input:hover+output{--value-background:var(--value-background-hover, #0366d6);--y-offset:-5px;color:var(--value-active-color,#fff);box-shadow:0 0 0 3px var(--value-background)}.range-slider>input:active{--thumb-shadow:var(--thumb-shadow-active);cursor:-webkit-grabbing;cursor:grabbing;z-index:2}.range-slider>input:active+output{transition:none}.range-slider>input:first-of-type{--is-left-most:Clamp(0, (var(--value-a) - var(--value-b)) * 99999, 1)}.range-slider>input:first-of-type+output{--value:var(--value-a);--x-offset:calc(var(--completed-a) * -1%)}.range-slider>input:first-of-type+output:not(:only-of-type){--flip:calc(var(--thumbs-too-close) * -1)}.range-slider>input:first-of-type+output::after{content:var(--prefix, "") var(--text-value-a) var(--suffix, "")}.range-slider>input:nth-of-type(2){--is-left-most:Clamp(0, (var(--value-b) - var(--value-a)) * 99999, 1)}.range-slider>input:nth-of-type(2)+output{--value:var(--value-b)}.range-slider>input:only-of-type~.range-slider__progress{--clip-start:0}.range-slider>input+output{--flip:-1;--x-offset:calc(var(--completed-b) * -1%);--pos:calc(((var(--value) - var(--min)) / (var(--max) - var(--min))) * 100%);pointer-events:none;position:absolute;z-index:5;background:var(--value-background);border-radius:10px;padding:2px 4px;left:var(--pos);transform:translate(var(--x-offset),calc(150% * var(--flip) - (var(--y-offset,0px) + var(--value-offset-y)) * var(--flip)));transition:.12s ease-out,left}.range-slider>input+output::after{content:var(--prefix, "") var(--text-value-b) var(--suffix, "");font:var(--value-font)}body>.range-slider,label[dir=rtl] .range-slider{width:clamp(300px,50vw,800px);min-width:200px}.superhide{display:none}.lds-hourglass{display:inline-block;position:relative;width:80px;height:80px}.lds-hourglass:after{content:" ";display:block;border-radius:50%;width:0;height:0;margin:8px;box-sizing:border-box;border:32px solid #000;border-color:#fff transparent;animation:1.2s infinite lds-hourglass}.dark .lds-hourglass:after{border:32px solid #fff;border-color:#000 transparent}@keyframes lds-hourglass{0%{transform:rotate(0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}50%{transform:rotate(900deg);animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}100%{transform:rotate(1800deg)}}