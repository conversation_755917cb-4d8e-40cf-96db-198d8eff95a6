document.addEventListener("alpine:init",()=>{Alpine.data("laravellivewiretable",e=>({tableId:"",showBulkActionsAlpine:!1,primaryKeyName:"",shouldBeDisplayed:e.entangle("shouldBeDisplayed"),tableName:e.entangle("tableName"),dataTableFingerprint:e.entangle("dataTableFingerprint"),listeners:[],childElementOpen:!1,filtersOpen:e.entangle("filterSlideDownDefaultVisible"),paginationCurrentCount:e.entangle("paginationCurrentCount"),paginationTotalItemCount:e.entangle("paginationTotalItemCount"),paginationCurrentItems:e.entangle("paginationCurrentItems"),selectedItems:e.entangle("selected"),selectAllStatus:e.entangle("selectAll"),delaySelectAll:e.entangle("delaySelectAll"),hideBulkActionsWhenEmpty:e.entangle("hideBulkActionsWhenEmpty"),dragging:!1,reorderEnabled:!1,sourceID:"",targetID:"",evenRowClasses:"",oddRowClasses:"",currentlyHighlightedElement:"",evenRowClassArray:{},oddRowClassArray:{},evenNotInOdd:{},oddNotInEven:{},orderedRows:[],defaultReorderColumn:e.entangle("defaultReorderColumn"),reorderStatus:e.entangle("reorderStatus"),currentlyReorderingStatus:e.entangle("currentlyReorderingStatus"),hideReorderColumnUnlessReorderingStatus:e.entangle("hideReorderColumnUnlessReorderingStatus"),reorderDisplayColumn:e.entangle("reorderDisplayColumn"),externalFilterPillsVals:e.entangle("externalFilterPillsValues"),internalFilterPillsVals:e.entangle("internalFilterPillsVals"),showFilterPillLabel:[],filterPillsSeparator:", ",showFilterPillsSection:!0,removeHTMLTags(e){let t=new DOMParser;return(t.parseFromString(e,"text/html").body.innerText||"").trim()},resetSpecificFilter(t){this.externalFilterPillsVals[t]=[],e.call("resetFilter",t)},resetAllFilters(){this.externalFilterPillsVals=[],e.call("setFilterDefaults")},setInternalFilterPillVal(e,t){void 0!==t&&(this.internalFilterPillsVals[e]=t)},syncExternalFilterPillsValues(e,t){this.externalFilterPillsVals[e]=t,this.showFilterPillLabel[e]=this.getFilterPillsLength(e)},getFilterPillsLength(e){return Object.keys(this.externalFilterPillsVals[e]).length??0},showFilterPillsValue(e,t){void 0!==t?this.externalFilterPillsVals[e]=t:this.externalFilterPillsVals[e]=null},setFilterPillsLength(e){let t=0;return void 0!==e?Object.keys(e).length??0:0},showFilterPillsLabel(e){return this.getFilterPillsLength(e),this.getFilterPillsLength(e)>0},getFilterPillImplodedValues(e,t){let l=this.externalFilterPillsVals[e];return"undefined"!==l?l.join(t):""},showFilterPillsSeparator(e,t){return t+1<this.getFilterPillsLength(e)},dragStart(e){this.$nextTick(()=>{this.setupEvenOddClasses()}),this.sourceID=e.target.id,e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",e.target.id),e.target.classList.add("laravel-livewire-tables-dragging")},dragOverEvent(e){"object"==typeof this.currentlyHighlightedElement&&this.currentlyHighlightedElement.classList.remove("laravel-livewire-tables-highlight-bottom","laravel-livewire-tables-highlight-top");let t=e.target.closest("tr");this.currentlyHighlightedElement=t,e.offsetY<t.getBoundingClientRect().height/2?(t.classList.add("laravel-livewire-tables-highlight-top"),t.classList.remove("laravel-livewire-tables-highlight-bottom")):(t.classList.remove("laravel-livewire-tables-highlight-top"),t.classList.add("laravel-livewire-tables-highlight-bottom"))},dragLeaveEvent(e){e.target.closest("tr").classList.remove("laravel-livewire-tables-highlight-bottom","laravel-livewire-tables-highlight-top")},dropEvent(e){"object"==typeof this.currentlyHighlightedElement&&this.currentlyHighlightedElement.classList.remove("laravel-livewire-tables-highlight-bottom","laravel-livewire-tables-highlight-top");let t=e.target.closest("tr"),l=e.target.closest("tr").parentNode,i=document.getElementById(this.sourceID).closest("tr");i.classList.remove("laravel-livewire-table-dragging");let s=i.rowIndex,a=t.rowIndex,r=document.getElementById(this.tableId),n=s;e.offsetY>t.getBoundingClientRect().height/2?l.insertBefore(i,t.nextSibling):l.insertBefore(i,t),a<s&&(n=a);let o="even";for(let h=1,d;d=r.rows[h];h++)d.classList.contains("hidden")||d.classList.contains("md:hidden")||("even"===o?(d.classList.remove(...this.oddNotInEven),d.classList.add(...this.evenNotInOdd),o="odd"):(d.classList.remove(...this.evenNotInOdd),d.classList.add(...this.oddNotInEven),o="even"))},reorderToggle(){this.currentlyReorderingStatus?e.disableReordering():(this.hideReorderColumnUnlessReorderingStatus&&(this.reorderDisplayColumn=!0),this.setupEvenOddClasses(),e.enableReordering()),this.$nextTick(()=>{this.setupEvenOddClasses()})},cancelReorder(){this.hideReorderColumnUnlessReorderingStatus&&(this.reorderDisplayColumn=!1),e.disableReordering()},updateOrderedItems(){let t=document.getElementById(this.tableId),l=[];for(let i=1,s;s=t.rows[i];i++)l.push({[this.primaryKeyName]:s.getAttribute("rowpk"),[this.defaultReorderColumn]:i});e.storeReorder(l)},setupEvenOddClasses(){if(void 0===this.evenNotInOdd.length||0==this.evenNotInOdd.length||void 0===this.oddNotInEven.length||0==this.oddNotInEven.length){let e=document.getElementById(this.tableId).getElementsByTagName("tbody")[0],t=[],l=[];void 0!==e.rows[0]&&void 0!==e.rows[1]&&(t=Array.from(e.rows[0].classList),l=Array.from(e.rows[1].classList),this.evenNotInOdd=t.filter(e=>!l.includes(e)),this.oddNotInEven=l.filter(e=>!t.includes(e)),t=[],l=[])}},toggleSelectAll(){this.showBulkActionsAlpine&&(this.paginationTotalItemCount===this.selectedItems.length?(this.clearSelected(),this.selectAllStatus=!1):this.delaySelectAll?this.setAllItemsSelected():this.setAllSelected())},setAllItemsSelected(){this.showBulkActionsAlpine&&(this.selectAllStatus=!0,this.selectAllOnPage())},setAllSelected(){this.showBulkActionsAlpine&&(this.delaySelectAll?(this.selectAllStatus=!0,this.selectAllOnPage()):e.setAllSelected())},clearSelected(){this.showBulkActionsAlpine&&(this.selectAllStatus=!1,e.clearSelected())},selectAllOnPage(){if(!this.showBulkActionsAlpine)return;let e=this.selectedItems,t=this.paginationCurrentItems.values();for(let l of t)e.push(l.toString());this.selectedItems=[...new Set(e)]},setTableId(e){this.tableId=e},setAlpineBulkActions(e){this.showBulkActionsAlpine=e},setPrimaryKeyName(e){this.primaryKeyName=e},showTable(e){let t=e.detail.tableName??"",l=e.detail.tableFingerpint??"";((t??"")!=""&&t===this.tableName||""!=l&&eventTableFingerpint===this.dataTableFingerprint)&&(this.shouldBeDisplayed=!0)},hideTable(e){let t=e.detail.tableName??"",l=e.detail.tableFingerpint??"";(""!=t&&t===this.tableName||""!=l&&eventTableFingerpint===this.dataTableFingerprint)&&(this.shouldBeDisplayed=!1)},destroy(){this.listeners.forEach(e=>{e()})}})),Alpine.data("booleanFilter",(e,t,l,i)=>({localListeners:[],switchOn:!1,value:e.entangle("filterComponents."+t).live,init(){this.switchOn=!1,void 0!==this.value&&(this.switchOn=Boolean(Number(this.value))),this.localListeners.push(Livewire.on("filter-was-set",e=>{e.tableName==this.tableName&&e.filterKey==t&&(this.switchOn=e.value??i)}))},destroy(){this.localListeners.forEach(e=>{e()})}})),Alpine.data("newBooleanFilter",(e,t,l)=>({booleanFilterKey:e,switchOn:!1,value:!1,toggleStatus(){let e=!Boolean(Number(this.$wire.get("filterComponents."+this.booleanFilterKey)??this.value));return this.switchOn=this.value=e,Number(e)},toggleStatusWithUpdate(){let e=this.toggleStatus();this.$wire.set("filterComponents."+this.booleanFilterKey,e)},toggleStatusWithReset(){this.toggleStatus(),this.$wire.call("resetFilter",this.booleanFilterKey)},setSwitchOn(e){let t=Number(e??0);this.switchOn=Boolean(t)},init(){this.$nextTick(()=>{this.value=this.$wire.get("filterComponents."+this.booleanFilterKey)??l,this.setSwitchOn(this.value??0)}),this.listeners.push(Livewire.on("filter-was-set",e=>{e.tableName==this.tableName&&e.filterKey==this.booleanFilterKey&&(this.switchOn=e.value??l)}))}})),Alpine.data("booleanFilterLatest",e=>({booleanFilterKey:e.filterKey,booleanFilterDefaultValue:e.defaultValue,switchOn:!1,value:!1,toggleStatus(){let e=!Boolean(Number(this.$wire.get("filterComponents."+this.booleanFilterKey)??this.value));return this.switchOn=this.value=e,Number(e)},toggleStatusWithUpdate(){let e=this.toggleStatus();this.$wire.set("filterComponents."+this.booleanFilterKey,e)},toggleStatusWithReset(){this.toggleStatus(),this.$wire.call("resetFilter",this.booleanFilterKey)},setSwitchOn(e){let t=Number(e??0);this.switchOn=Boolean(t)},init(){this.$nextTick(()=>{this.value=this.$wire.get("filterComponents."+this.booleanFilterKey)??this.booleanFilterDefaultValue,this.setSwitchOn(this.value??0)}),this.listeners.push(Livewire.on("filter-was-set",e=>{e.tableName==this.tableName&&e.filterKey==this.booleanFilterKey&&(this.switchOn=e.value??this.booleanFilterDefaultValue)}))}})),Alpine.data("numberRangeFilter",(e,t,l,i,s)=>({allFilters:e.entangle("filterComponents",!1),originalMin:0,originalMax:100,filterMin:0,filterMax:100,currentMin:0,currentMax:100,hasUpdate:!1,wireValues:e.entangle("filterComponents."+t,!1),defaultMin:i.minRange,defaultMax:i.maxRange,restrictUpdates:!1,initialiseStyles(){let e=document.getElementById(l);e.style.setProperty("--value-a",this.wireValues.min??this.filterMin),e.style.setProperty("--text-value-a",JSON.stringify(this.wireValues.min??this.filterMin)),e.style.setProperty("--value-b",this.wireValues.max??this.filterMax),e.style.setProperty("--text-value-b",JSON.stringify(this.wireValues.max??this.filterMax))},updateStyles(e,t){let i=document.getElementById(l);i.style.setProperty("--value-a",e),i.style.setProperty("--text-value-a",JSON.stringify(e)),i.style.setProperty("--value-b",t),i.style.setProperty("--text-value-b",JSON.stringify(t))},setupWire(){void 0!==this.wireValues?(this.filterMin=this.originalMin=void 0!==this.wireValues.min?this.wireValues.min:this.defaultMin,this.filterMax=this.originalMax=void 0!==this.wireValues.max?this.wireValues.max:this.defaultMax):(this.filterMin=this.originalMin=this.defaultMin,this.filterMax=this.originalMax=this.defaultMax),this.updateStyles(this.filterMin,this.filterMax)},allowUpdates(){this.updateWire()},updateWire(){let e=parseInt(this.filterMin),t=parseInt(this.filterMax);(e!=this.originalMin||t!=this.originalMax)&&(t<e&&(this.filterMin=t,this.filterMax=e),this.hasUpdate=!0,this.originalMin=e,this.originalMax=t),this.updateStyles(this.filterMin,this.filterMax)},updateWireable(){this.hasUpdate&&(this.hasUpdate=!1,this.wireValues={min:this.filterMin,max:this.filterMax},e.set("filterComponents."+t,this.wireValues))},init(){this.initialiseStyles(),this.setupWire(),this.$watch("allFilters",e=>this.setupWire())}})),Alpine.data("flatpickrFilter",(e,t,l,i,s)=>({wireValues:e.entangle("filterComponents."+t),flatpickrInstance:flatpickr(i,{mode:"range",altFormat:l.altFormat??"F j, Y",altInput:l.altInput??!1,allowInput:l.allowInput??!1,allowInvalidPreload:l.allowInvalidPreload??!0,ariaDateFormat:l.ariaDateFormat??"F j, Y",clickOpens:!0,dateFormat:l.dateFormat??"Y-m-d",defaultDate:l.defaultDate??null,defaultHour:l.defaultHour??12,defaultMinute:l.defaultMinute??0,enableTime:l.enableTime??!1,enableSeconds:l.enableSeconds??!1,hourIncrement:l.hourIncrement??1,locale:l.locale??"en",minDate:l.earliestDate??null,maxDate:l.latestDate??null,minuteIncrement:l.minuteIncrement??5,shorthandCurrentMonth:l.shorthandCurrentMonth??!1,time_24hr:l.time_24hr??!1,weekNumbers:l.weekNumbers??!1,onOpen:function(){window.childElementOpen=!0},onChange:function(l,i,s){if(l.length>1){var a=i.split(" "),r={};window.childElementOpen=!1,window.filterPopoverOpen=!1,r={minDate:a[0],maxDate:void 0===a[2]?a[0]:a[2]},e.set("filterComponents."+t,r)}}}),changedValue:function(l){l.length<5&&(this.flatpickrInstance.setDate([]),e.set("filterComponents."+t,{}))},setupWire(){if(void 0!==this.wireValues){if(void 0!==this.wireValues.minDate&&void 0!==this.wireValues.maxDate){let e=[this.wireValues.minDate,this.wireValues.maxDate];this.flatpickrInstance.setDate(e)}else this.flatpickrInstance.setDate([])}else this.flatpickrInstance.setDate([])},init(){this.setupWire(),this.$watch("wireValues",e=>this.setupWire())}})),Alpine.data("tableWrapper",(e,t)=>({shouldBeDisplayed:e.entangle("shouldBeDisplayed"),listeners:[],childElementOpen:!1,filtersOpen:e.entangle("filterSlideDownDefaultVisible"),paginationCurrentCount:e.entangle("paginationCurrentCount"),paginationTotalItemCount:e.entangle("paginationTotalItemCount"),paginationCurrentItems:e.entangle("paginationCurrentItems"),selectedItems:e.entangle("selected"),selectAllStatus:e.entangle("selectAll"),delaySelectAll:e.entangle("delaySelectAll"),hideBulkActionsWhenEmpty:e.entangle("hideBulkActionsWhenEmpty"),toggleSelectAll(){t&&(this.paginationTotalItemCount===this.selectedItems.length?(this.clearSelected(),this.selectAllStatus=!1):this.delaySelectAll?this.setAllItemsSelected():this.setAllSelected())},setAllItemsSelected(){t&&(this.selectAllStatus=!0,this.selectAllOnPage())},setAllSelected(){t&&(this.delaySelectAll?(this.selectAllStatus=!0,this.selectAllOnPage()):e.setAllSelected())},clearSelected(){t&&(this.selectAllStatus=!1,e.clearSelected())},selectAllOnPage(){if(!t)return;let e=this.selectedItems,l=this.paginationCurrentItems.values();for(let i of l)e.push(i.toString());this.selectedItems=[...new Set(e)]},destroy(){this.listeners.forEach(e=>{e()})}})),Alpine.data("reorderFunction",(e,t,l)=>({dragging:!1,reorderEnabled:!1,sourceID:"",targetID:"",evenRowClasses:"",oddRowClasses:"",currentlyHighlightedElement:"",evenRowClassArray:{},oddRowClassArray:{},evenNotInOdd:{},oddNotInEven:{},orderedRows:[],defaultReorderColumn:e.get("defaultReorderColumn"),reorderStatus:e.get("reorderStatus"),currentlyReorderingStatus:e.entangle("currentlyReorderingStatus"),hideReorderColumnUnlessReorderingStatus:e.entangle("hideReorderColumnUnlessReorderingStatus"),reorderDisplayColumn:e.entangle("reorderDisplayColumn"),dragStart(e){this.$nextTick(()=>{this.setupEvenOddClasses()}),this.sourceID=e.target.id,e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",e.target.id),e.target.classList.add("laravel-livewire-tables-dragging")},dragOverEvent(e){"object"==typeof this.currentlyHighlightedElement&&this.currentlyHighlightedElement.classList.remove("laravel-livewire-tables-highlight-bottom","laravel-livewire-tables-highlight-top");let t=e.target.closest("tr");this.currentlyHighlightedElement=t,e.offsetY<t.getBoundingClientRect().height/2?(t.classList.add("laravel-livewire-tables-highlight-top"),t.classList.remove("laravel-livewire-tables-highlight-bottom")):(t.classList.remove("laravel-livewire-tables-highlight-top"),t.classList.add("laravel-livewire-tables-highlight-bottom"))},dragLeaveEvent(e){e.target.closest("tr").classList.remove("laravel-livewire-tables-highlight-bottom","laravel-livewire-tables-highlight-top")},dropEvent(e){"object"==typeof this.currentlyHighlightedElement&&this.currentlyHighlightedElement.classList.remove("laravel-livewire-tables-highlight-bottom","laravel-livewire-tables-highlight-top");let l=e.target.closest("tr"),i=e.target.closest("tr").parentNode,s=document.getElementById(this.sourceID).closest("tr");s.classList.remove("laravel-livewire-table-dragging");let a=s.rowIndex,r=l.rowIndex,n=document.getElementById(t),o=a;e.offsetY>l.getBoundingClientRect().height/2?i.insertBefore(s,l.nextSibling):i.insertBefore(s,l),r<a&&(o=r);let h="even";for(let d=1,u;u=n.rows[d];d++)u.classList.contains("hidden")||u.classList.contains("md:hidden")||("even"===h?(u.classList.remove(...this.oddNotInEven),u.classList.add(...this.evenNotInOdd),h="odd"):(u.classList.remove(...this.evenNotInOdd),u.classList.add(...this.oddNotInEven),h="even"))},reorderToggle(){this.$nextTick(()=>{this.setupEvenOddClasses()}),this.currentlyReorderingStatus?e.disableReordering():(this.setupEvenOddClasses(),this.hideReorderColumnUnlessReorderingStatus&&(this.reorderDisplayColumn=!0),e.enableReordering())},cancelReorder(){this.hideReorderColumnUnlessReorderingStatus&&(this.reorderDisplayColumn=!1),e.disableReordering()},updateOrderedItems(){let i=document.getElementById(t),s=[];for(let a=1,r;r=i.rows[a];a++)s.push({[l]:r.getAttribute("rowpk"),[this.defaultReorderColumn]:a});e.storeReorder(s)},setupEvenOddClasses(){if(void 0===this.evenNotInOdd.length||0==this.evenNotInOdd.length||void 0===this.oddNotInEven.length||0==this.oddNotInEven.length){let e=document.getElementById(t).getElementsByTagName("tbody")[0],l=[],i=[];void 0!==e.rows[0]&&void 0!==e.rows[1]&&(l=Array.from(e.rows[0].classList),i=Array.from(e.rows[1].classList),this.evenNotInOdd=l.filter(e=>!i.includes(e)),this.oddNotInEven=i.filter(e=>!l.includes(e)),l=[],i=[])}},init(){}})),Alpine.data("filterPillsHandler",e=>({localData:e,localFilterKey:"",localFilterTitle:"",isExternalFilter:!1,shouldRenderAsHTML:!1,shouldWatchPillValues:!1,pillsSeparator:",",pillValues:null,pillHasValues:!1,displayString:"",generateLocalFilterPillImplodedValues(e){if(void 0!==e){var t,l="---tablepillsseparator---",i=RegExp(l,"g");if(t=Array.isArray(e)?e.join(l):e,this.shouldRenderAsHTML||(t=this.removeHTMLTags(t)),null!==t)return t.replace(i,this.pillsSeparator)}return""},clearExternalFilterPill(){this.isExternalFilter&&(this.externalFilterPillsVals[this.localFilterKey]=[],this.displayString=this.generateLocalFilterPillImplodedValues(this.externalFilterPillsVals[this.localFilterKey]),this.updatePillHasValues(),this.resetSpecificFilter(this.localFilterKey))},trigger:{"@filterpillupdate.window"(e){this.watchForUpdateEvent(e)}},checkEventIsValid(e,t){return this.tableName===e&&this.localFilterKey===t},watchForUpdateEvent(e){if(this.checkEventIsValid(e.detail.tableName??"",e.detail.filterKey??"")){let t=e.detail.pillItem??"";if(this.shouldRenderAsHTML||(t=this.removeHTMLTags(t)),""!=t){if(this.isExternalFilter){let l=this.externalFilterPillsVals[this.localFilterKey];l.push(t),this.updatePillValues(l)}else this.updatePillValues(t)}}},updatePillValues(e){return this.pillValues=e,this.displayString=this.generateLocalFilterPillImplodedValues(e),this.updatePillHasValues(),this.displayString},updatePillHasValues(){this.pillHasValues=this.displayString.length>0},init(){this.localFilterKey=this.localData.filterKey??"unknown",this.localFilterTitle=this.localData.filterPillTitle??"Unknown",this.pillsSeparator=this.localData.separator??",",this.shouldWatchPillValues=Boolean(this.localData.watchForEvents??0),this.isExternalFilter=Boolean(this.localData.isAnExternalLivewireFilter??0),this.shouldRenderAsHTML=Boolean(this.localData.renderPillsAsHtml??0),this.pillValues=this.localData.pillValues??null,this.$nextTick(()=>{this.isExternalFilter?this.updatePillValues(this.externalFilterPillsVals[this.localFilterKey]):this.updatePillValues(this.pillValues)}),this.isExternalFilter&&this.shouldWatchPillValues&&this.$watch("externalFilterPillsVals."+this.localFilterKey,e=>{this.updatePillValues(e)})}})),Alpine.data("tablesExternalFilter",(e,t)=>({externalFilterKey:t,pillValues:[],optionsAvailable:e.entangle("optionsAvailable"),optionsSelected:e.entangle("optionsSelected").live,selectedItems:e.entangle("selectedItems"),sendValueToPill(e){let t=this.removeHTMLTags(e);this.$dispatch("filterpillupdate",{tableName:this.tableName,filterKey:this.externalFilterKey,pillItem:t})},overridePill(e){let t=this.removeHTMLTags(e);this.$dispatch("filterpillupdate",{tableName:this.tableName,filterKey:this.externalFilterKey,pillItem:t})},syncItems(t){this.pillValues=[],t.forEach(e=>{this.pillValues.push(this.optionsAvailable[e])}),this.pillValues.length>0&&(this.pillValues.sort(),this.syncExternalFilterPillsValues(this.externalFilterKey,this.pillValues)),this.optionsSelected=this.selectedItems,e.set("value",this.selectedItems)},init(){this.selectedItems=this.optionsSelected,this.syncItems(this.selectedItems),this.$watch("selectedItems",e=>this.syncItems(e))}}))});