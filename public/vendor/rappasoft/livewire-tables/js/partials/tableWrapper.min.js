function a(){Alpine.data('tableWrapper',(wire,showBulkActionsAlpine)=>({listeners:[],childElementOpen:!1,filtersOpen:wire.entangle('filterSlideDownDefaultVisible'),paginationCurrentCount:wire.entangle('paginationCurrentCount'),paginationTotalItemCount:wire.entangle('paginationTotalItemCount'),paginationCurrentItems:wire.entangle('paginationCurrentItems'),selectedItems:wire.entangle('selected'),selectAllStatus:wire.entangle('selectAll'),delaySelectAll:wire.entangle('delaySelectAll'),hideBulkActionsWhenEmpty:wire.entangle('hideBulkActionsWhenEmpty'),toggleSelectAll(){if(!showBulkActionsAlpine)return;if(this.paginationTotalItemCount===this.selectedItems.length){this.clearSelected();this.selectAllStatus=!1}else this.delaySelectAll?this.setAllItemsSelected():this.setAllSelected()},setAllItemsSelected(){if(!showBulkActionsAlpine)return;this.selectAllStatus=!0;this.selectAllOnPage()},setAllSelected(){if(!showBulkActionsAlpine)return;this.delaySelectAll?(this.selectAllStatus=!0,this.selectAllOnPage()):(wire.setAllSelected())},clearSelected(){if(!showBulkActionsAlpine)return;this.selectAllStatus=!1;wire.clearSelected()},selectAllOnPage(){if(!showBulkActionsAlpine)return;let A=this.selectedItems;var b=this.paginationCurrentItems.values();for(const _ of b)A.push(`${_}`);this.selectedItems=[...new Set(A)]},destroy(){for(const B of this.listeners)B()}}))}export default a;
