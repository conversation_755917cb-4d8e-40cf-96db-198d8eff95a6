function a(){Alpine.data('numberRangeFilter',(A,b,c,d,e)=>({allFilters:<PERSON><PERSON>entangle('filterComponents',!1),originalMin:0,originalMax:100,filterMin:0,filterMax:100,currentMin:0,currentMax:100,hasUpdate:!1,wireValues:A.entangle(`filterComponents.${b}`,!1),defaultMin:d['minRange'],defaultMax:d['maxRange'],restrictUpdates:!1,initialiseStyles(){let _=document.getElementById(c);_.style.setProperty('--value-a',this.wireValues['min']??this.filterMin);_.style.setProperty('--text-value-a',JSON.stringify(this.wireValues['min']??this.filterMin));_.style.setProperty('--value-b',this.wireValues['max']??this.filterMax);_.style.setProperty('--text-value-b',JSON.stringify(this.wireValues['max']??this.filterMax))},updateStyles(B,C){let _c=document.getElementById(c);_c.style.setProperty('--value-a',B);_c.style.setProperty('--text-value-a',JSON.stringify(B));_c.style.setProperty('--value-b',C);_c.style.setProperty('--text-value-b',JSON.stringify(C))},setupWire(){this.wireValues!==void 0?(this.filterMin=this.originalMin=(this.wireValues['min']!==void 0)?this.wireValues['min']:this.defaultMin,this.filterMax=this.originalMax=(this.wireValues['max']!==void 0)?this.wireValues['max']:this.defaultMax):(this.filterMin=this.originalMin=this.defaultMin,this.filterMax=this.originalMax=this.defaultMax);this.updateStyles(this.filterMin,this.filterMax)},allowUpdates(){this.updateWire()},updateWire(){let _a=parseInt(this.filterMin);let _b=parseInt(this.filterMax);if(_a!=this.originalMin||_b!=this.originalMax){_b<_a&&(this.filterMin=_b,this.filterMax=_a);this.hasUpdate=!0;this.originalMin=_a;this.originalMax=_b}this.updateStyles(this.filterMin,this.filterMax)},updateWireable(){this.hasUpdate&&(this.hasUpdate=!1,this.wireValues={'min':this.filterMin,'max':this.filterMax},A.set(`filterComponents.${b}`,this.wireValues))},init(){this.initialiseStyles();this.setupWire();this.$watch('allFilters',value=>this.setupWire())}}))}export{a as nrf};export default a;
