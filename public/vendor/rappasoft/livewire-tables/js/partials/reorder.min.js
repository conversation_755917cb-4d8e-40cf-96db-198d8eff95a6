function a(){Alpine.data('reorderFunction',(wire,tableID,primaryKeyName)=>({dragging:!1,reorderEnabled:!1,sourceID:'',targetID:'',evenRowClasses:'',oddRowClasses:'',currentlyHighlightedElement:'',evenRowClassArray:{},oddRowClassArray:{},evenNotInOdd:{},oddNotInEven:{},orderedRows:[],defaultReorderColumn:wire.get('defaultReorderColumn'),reorderStatus:wire.get('reorderStatus'),currentlyReorderingStatus:wire.entangle('currentlyReorderingStatus'),hideReorderColumnUnlessReorderingStatus:wire.entangle('hideReorderColumnUnlessReorderingStatus'),reorderDisplayColumn:wire.entangle('reorderDisplayColumn'),dragStart(A){this.$nextTick(()=>this.setupEvenOddClasses());this.sourceID=A.target.id;A.dataTransfer.effectAllowed='move';A.dataTransfer.setData('text/plain',A.target.id);A.target.classList.add('laravel-livewire-tables-dragging')},dragOverEvent(_){typeof this.currentlyHighlightedElement=='object'&&this.currentlyHighlightedElement.classList.remove('laravel-livewire-tables-highlight-bottom','laravel-livewire-tables-highlight-top');let b=_.target.closest('tr');this.currentlyHighlightedElement=b;_.offsetY<(b.getBoundingClientRect().height/2)?(b.classList.add('laravel-livewire-tables-highlight-top'),b.classList.remove('laravel-livewire-tables-highlight-bottom')):(b.classList.remove('laravel-livewire-tables-highlight-top'),b.classList.add('laravel-livewire-tables-highlight-bottom'))},dragLeaveEvent(B){B.target.closest('tr').classList.remove('laravel-livewire-tables-highlight-bottom','laravel-livewire-tables-highlight-top')},dropEvent(c){typeof this.currentlyHighlightedElement=='object'&&this.currentlyHighlightedElement.classList.remove('laravel-livewire-tables-highlight-bottom','laravel-livewire-tables-highlight-top');let C=c.target.closest('tr');let _c=c.target.closest('tr').parentNode;let d=document.getElementById(this.sourceID).closest('tr');d.classList.remove('laravel-livewire-table-dragging');let e=d.rowIndex;let f=C.rowIndex;let g=document.getElementById(tableID);c.offsetY>(C.getBoundingClientRect().height/2)?_c.insertBefore(d,C.nextSibling):_c.insertBefore(d,C);f<e;let h='even';for(let i=1,_b;_b=g.rows[i];i++)if(!_b.classList.contains('hidden')&&!_b.classList.contains('md:hidden'))h==='even'?(_b.classList.remove(...this.oddNotInEven),_b.classList.add(...this.evenNotInOdd),h='odd'):(_b.classList.remove(...this.evenNotInOdd),_b.classList.add(...this.oddNotInEven),h='even')},reorderToggle(){this.$nextTick(()=>this.setupEvenOddClasses());if(this.currentlyReorderingStatus)wire.disableReordering();else{this.setupEvenOddClasses();this.hideReorderColumnUnlessReorderingStatus&&(this.reorderDisplayColumn=!0);wire.enableReordering()}},cancelReorder(){this.hideReorderColumnUnlessReorderingStatus&&(this.reorderDisplayColumn=!1);wire.disableReordering()},updateOrderedItems(){let _a=document.getElementById(tableID);let D=[];for(let i=1,_B;_B=_a.rows[i];i++)D.push({[primaryKeyName]:_B.getAttribute('rowpk'),[this.defaultReorderColumn]:i});wire.storeReorder(D)},setupEvenOddClasses(){if(this.evenNotInOdd.length===void 0||this.evenNotInOdd.length==0||this.oddNotInEven.length===void 0||this.oddNotInEven.length==0){let _A=document.getElementById(tableID).getElementsByTagName('tbody')[0];let E=[];let _C=[];(_A.rows[0]!==void 0&&_A.rows[1]!==void 0)&&(E=[..._A.rows[0].classList],_C=[..._A.rows[1].classList],this.evenNotInOdd=E.filter(aA=>!_C.includes(aA)),this.oddNotInEven=_C.filter(aB=>!E.includes(aB)),E=[],_C=[])}},init(){}}))}export default a;
