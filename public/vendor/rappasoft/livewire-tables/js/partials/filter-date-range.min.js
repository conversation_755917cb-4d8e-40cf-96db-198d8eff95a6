function fpf(){Alpine.data("flatpickrFilter",(e,t,a,n,l)=>({wireValues:e.entangle("filterComponents."+t),flatpickrInstance:flatpickr(n,{mode:"range",altFormat:a.altFormat??"F j, Y",altInput:a.altInput??!1,allowInput:a.allowInput??!1,allowInvalidPreload:a.allowInvalidPreload??!0,ariaDateFormat:a.ariaDateFormat??"F j, Y",clickOpens:!0,dateFormat:a.dateFormat??"Y-m-d",defaultDate:a.defaultDate??null,defaultHour:a.defaultHour??12,defaultMinute:a.defaultMinute??0,enableTime:a.enableTime??!1,enableSeconds:a.enableSeconds??!1,hourIncrement:a.hourIncrement??1,locale:a.locale??"en",minDate:a.earliestDate??null,maxDate:a.latestDate??null,minuteIncrement:a.minuteIncrement??5,shorthandCurrentMonth:a.shorthandCurrentMonth??!1,time_24hr:a.time_24hr??!1,weekNumbers:a.weekNumbers??!1,onOpen:function(){window.childElementOpen=!0},onChange:function(a,n,l){if(a.length>1){var i=n.split(" "),r={};window.childElementOpen=!1,window.filterPopoverOpen=!1,r={minDate:i[0],maxDate:void 0===i[2]?i[0]:i[2]},e.set("filterComponents."+t,r)}}}),setupWire(){if(void 0!==this.wireValues){if(void 0!==this.wireValues.minDate&&void 0!==this.wireValues.maxDate){let e=[this.wireValues.minDate,this.wireValues.maxDate];this.flatpickrInstance.setDate(e)}else this.flatpickrInstance.setDate([])}else this.flatpickrInstance.setDate([])},init(){this.setupWire(),this.$watch("wireValues",e=>this.setupWire())}}))}export default fpf;