<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'ultramsg' => [
        'token' => env('ULTRAMSG_TOKEN', 'l6lew42p17fr8gqi'),
        'base_url' => env('ULTRAMSG_BASE_URL', 'https://api.ultramsg.com/instance104101'),
    ],

    'cedula' => [
        'app_id' => env('CEDULA_APP_ID', '927'),
        'token' => env('CEDULA_TOKEN', '03c3d8eaef3387f27c55077591e77001'),
        'base_url' => 'https://api.cedula.com.ve/',
    ],

];
