APP_NAME=1x10
APP_ENV=production
APP_KEY=base64:EUAzMLUIiSN3+tnjZsQoCj4eL924Rq8vJ6MjuYOLi9Q=
APP_DEBUG=false
APP_URL=https://alianzaparauncambio.org

APP_LOCALE=es
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
 DB_HOST=127.0.0.1
 DB_PORT=3306
 DB_DATABASE=alianza_alianza
 DB_USERNAME=alianza_alianza
 DB_PASSWORD=Yt?iFY}pCX_6(+nj

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

ULTRAMSG_TOKEN=eliminar-guion-l6lew42p17fr8gqi
ULTRAMSG_BASE_URL=https://api.ultramsg.com/instance104101

# Cedula.com.ve API credentials
CEDULA_APP_ID=927
CEDULA_TOKEN=03c3d8eaef3387f27c55077591e77001TOKEN-AQUI
