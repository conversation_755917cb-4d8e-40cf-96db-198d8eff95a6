<?php

namespace App\Livewire\Notifications;

use App\Services\NotificationService;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $filter = 'all'; // all, unread, read

    public $search = '';

    /**
     * Get the notifications based on the current filter.
     */
    public function getNotifications()
    {
        app(NotificationService::class);
        $query = \App\Models\Notification::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc');

        if ($this->filter === 'unread') {
            $query->whereNull('read_at');
        } elseif ($this->filter === 'read') {
            $query->whereNotNull('read_at');
        }

        if ($this->search) {
            $query->where(function ($q): void {
                $q->where('title', 'like', '%'.$this->search.'%')
                    ->orWhere('message', 'like', '%'.$this->search.'%');
            });
        }

        return $query->paginate(20);
    }

    /**
     * Mark a notification as read.
     */
    public function markAsRead(int $notificationId): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->markAsRead($notificationId);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->markAllAsRead();
    }

    /**
     * Delete a notification.
     */
    public function deleteNotification(int $notificationId): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->deleteNotification($notificationId);
    }

    /**
     * Delete all notifications.
     */
    public function deleteAllNotifications(): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->deleteAllNotifications();
    }

    /**
     * Update the filter.
     */
    public function updatedFilter(): void
    {
        $this->resetPage();
    }

    /**
     * Update the search.
     */
    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Layout('components.layouts.app')]
    public function render(): View
    {
        return view('livewire.notifications.index', [
            'notifications' => $this->getNotifications(),
        ]);
    }
}
