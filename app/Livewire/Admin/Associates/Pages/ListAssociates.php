<?php

namespace App\Livewire\Admin\Associates\Pages;

use App\Exports\AssociatesExport;
use App\Exports\AssociatesPdfExport;
use App\Models\Asociado;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ListAssociates extends Component
{
    use WithPagination;

    protected $listeners = [
        'associate-deleted' => '$refresh',
    ];

    public $search = '';

    public $perPage = 10;

    public array $perPageOptions = [10, 25, 50, 100];

    public $status = '';

    public $sortBy = 'idasociado';

    public $sortDirection = 'desc';

    public $associateStatus = [];

    public $selectedAssociates = [];

    public $markAsImportant = [];

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function statistics(): array
    {
        return [
            'total' => Asociado::count(),
            'active' => Asociado::where('condicion_soc', 1)->count(),
            'inactive' => Asociado::where('condicion_soc', '!=', 1)->count(),
        ];
    }

    public function updatedAssociateStatus($value, $key): void
    {
        $associateId = explode('.', (string) $key)[1];
        $isActive = $value === 'active';

        Asociado::where('idasociado', $associateId)
            ->update(['condicion_soc' => $isActive]);

        \Flux::toast(text: 'Estado actualizado correctamente', variant: 'success', heading: 'Actualizado');
    }

    public function sort($field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    #[Computed]
    public function associates()
    {
        return Asociado::query()
            ->when($this->search, function (Builder $query) {
                $search = '%'.trim((string) $this->search).'%';

                return $query->where(function (Builder $query) use ($search): void {
                    $query->where('cedula_soc', 'like', '%'.$search.'%')
                        ->orWhere('idasociado', 'like', '%'.$search.'%')
                        ->orWhere('apellidos', 'like', '%'.$search.'%')
                        ->orWhere('nombres', 'like', '%'.$search.'%')
                        ->orWhere('numero_telefpers', 'like', '%'.$search.'%');
                });
            })
            ->when($this->status, function (Builder $query) {
                if ($this->status === 'active') {
                    return $query->where('condicion_soc', 1);
                }

                return $query->where('condicion_soc', '!=', 1);
            })
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate($this->perPage);
    }

    public function exportToExcel()
    {
        \Flux::toast(text: 'Preparando exportación a Excel...', variant: 'info', heading: 'Exportando');

        return Excel::download(
            new AssociatesExport($this->search, $this->status),
            'asociados-'.now()->format('Y-m-d').'.xlsx'
        );
    }

    public function exportToPdf(): \Illuminate\Http\Response
    {
        \Flux::toast(variant: 'info', heading: 'Exportando', text: 'Preparando exportación a PDF...');

        return (new AssociatesPdfExport($this->search, $this->status))
            ->download('asociados-'.now()->format('Y-m-d').'.pdf');
    }

    public function render()
    {
        return view('livewire.admin.associates.pages.list-associates');

    }
}
