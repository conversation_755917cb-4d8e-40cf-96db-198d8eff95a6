<?php

namespace App\Livewire\Admin\Associates\Pages;

use App\Models\Asociado;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class EditAssociate extends Component
{
    public Asociado $associate;

    public string $tab = 'personal';

    public function mount(Asociado $associate): void
    {
        $this->authorize('update associates');
        $this->associate = $associate;
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.associates.pages.edit-associate');
    }
}
