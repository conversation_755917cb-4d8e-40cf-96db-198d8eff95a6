<?php

namespace App\Livewire\Admin\Associates\Pages;

use App\Services\CedulaService;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class IndexAssociate extends Component
{
    public function mount(): void
    {
        //
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        $cedulaService = new CedulaService;
        $test = $cedulaService->testQuery(19430853);

        return view('livewire.admin.associates.pages.index-associate',
            [
                'lol' => $test,
            ]);
    }
}
