<?php

namespace App\Livewire\Admin\Associates\Pages;

use App\Models\Asociado;
use Flux;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class DestroyAssociate extends Component
{
    public Asociado $associate;

    public $showConfirmModal = false;

    protected $listeners = [
        'deleteAssociate' => 'delete',
    ];

    public function delete(Asociado $associate): void
    {
        $this->associate = $associate;
        Flux::modal('showConfirmModal')->show();

    }

    public function destroy(): void
    {
        $this->authorize('create associates', auth()->user());

        $this->associate->delete();

        Flux::modal('showConfirmModal')->close();
        Flux::toast(
            variant: 'success',
            heading: __('associates.delete_associate'),
            text: __('associates.associate_deleted')
        );
        $this->dispatch('associate-deleted');
    }

    public function render(): View
    {
        return view('livewire.admin.associates.pages.destroy-associate');
    }
}
