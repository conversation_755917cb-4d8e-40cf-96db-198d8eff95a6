<?php

namespace App\Livewire\Admin\Associates\Pages;

use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class CreateAssociate extends Component
{
    public function mount(): void
    {
        //
    }

    #[Layout('components.layouts.app')]
    public function render(): View
    {
        return view('livewire.admin.associates.pages.create-associate');
    }
}
