<?php

namespace App\Livewire\Admin\Associates\Tabs;

use App\Models\Asociado;
use <PERSON><PERSON><PERSON>ez<PERSON>\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class FamilyData extends Component
{
    use LivewireAlert, WithPagination;

    public Asociado $associate;

    public $sortBy = 'id';

    public $sortDirection = 'desc';

    protected $listeners = [
        'familyMemberAdded' => '$refresh',
        'familyMemberUpdated' => '$refresh',
        'familyMemberDeleted' => '$refresh',
    ];

    public function sort($column): void
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function confirmDeleteFamilyMember(int $id): void
    {
        $this->dispatch('confirmDeleteFamilyMember', $id);
    }

    public function getFamilyMembersProperty()
    {
        return $this->associate->familyMembers()
            ->with(['family.familyGender', 'relationship', 'familyType'])
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate(10);
    }

    public function editFamilyMember($familyMemberId): void
    {
        $this->dispatch('editFamilyMember', $familyMemberId);
    }

    public function render()
    {
        return view('livewire.admin.associates.tabs.family-data');
    }
}
