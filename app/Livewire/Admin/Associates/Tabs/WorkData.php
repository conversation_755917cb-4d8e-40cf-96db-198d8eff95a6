<?php

namespace App\Livewire\Admin\Associates\Tabs;

use App\Models\Asociado;
use Flux;
use Jan<PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class WorkData extends Component
{
    use LivewireAlert;

    public Asociado $associate;

    public $numero_exp;

    public $codigo_nomina;

    public $fecha_ingreso;

    public $sueldo_base;

    public $ingresos_adic;

    public $tipo_pers;

    public $codigo_dep;

    public $departments;

    public $person_types;

    public function mount(Asociado $associate): void
    {
        // obtengo los departamentos
        $this->departments = \DB::table('codigos_varios')->select('codigo_v', 'descri_v')->where('ref_v', 'D')->get();
        // obtengo el tipo de personal
        $this->person_types = \DB::table('codigos_varios')->select('codigo_v', 'descri_v')->where('ref_v', 'P')->get();

        $this->associate = $associate;
        $this->numero_exp = $associate->numero_exp;
        $this->codigo_nomina = $associate->codigo_nomina;
        $this->fecha_ingreso = $associate->fecha_ingreso;
        $this->sueldo_base = $associate->sueldo_base;
        $this->ingresos_adic = $associate->ingresos_adic;
        $this->tipo_pers = $associate->tipo_pers;
        $this->codigo_dep = $associate->codigo_dep;
    }

    protected array $messages = [
        'numero_exp.required' => 'El campo número de expediente es requerido',
        'codigo_nomina.required' => 'El campo código de nómina es requerido',
        'fecha_ingreso.required' => 'El campo fecha de ingreso es requerido',
        'sueldo_base.required' => 'El campo sueldo base es requerido',
        'ingresos_adic.required' => 'El campo ingresos adicionales es requerido',
        'tipo_pers.required' => 'El campo tipo de persona es requerido',
        'codigo_dep.required' => 'El campo código de departamento es requerido',
    ];

    protected array $rules = [
        'numero_exp' => 'required|string|max:20',
        'codigo_nomina' => 'required|string|max:20',
        'fecha_ingreso' => 'required|date',
        'sueldo_base' => 'required|numeric|min:0',
        'ingresos_adic' => 'required|numeric|min:0',
        'tipo_pers' => 'required',
        'codigo_dep' => 'required',
    ];

    public function saveLaboralData(): void
    {
        $this->validate();

        $this->associate->update(
            [
                'numero_exp' => strtoupper((string) $this->numero_exp),
                'codigo_nomina' => strtoupper((string) $this->codigo_nomina),
                'fecha_ingreso' => $this->fecha_ingreso,
                'sueldo_base' => $this->sueldo_base,
                'ingresos_adic' => $this->ingresos_adic,
                'tipo_pers' => $this->tipo_pers,
                'codigo_dep' => $this->codigo_dep,
            ]
        );

        Flux::toast(variant: 'success', heading: 'Asociado actualizado', text: 'Datos laborales actualizados correctamente.');
    }

    public function render()
    {
        return view('livewire.admin.associates.tabs.work-data');
    }
}
