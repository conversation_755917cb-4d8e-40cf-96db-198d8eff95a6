<?php

namespace App\Livewire\Admin\Associates\Tabs;

use App\Models\Asociado;
use App\Models\Bank;
use Livewire\Component;

class BankData extends Component
{
    public Asociado $associate;

    public $codigo_banco;

    public $numero_cuenta;

    public $banks;

    public function mount(Asociado $associate): void
    {
        // obtengo los bancos
        $this->banks = Bank::where('condicion', 1)->get();
        $this->associate = $associate;
        $this->codigo_banco = $associate->codigo_banco;
        $this->numero_cuenta = $associate->numero_cuenta;
    }

    protected array $messages = [
        'codigo_banco.required' => 'El codigo de banco es requerido.',
        'numero_cuenta.required' => 'El numero de cuenta es requerido.',
        'numero_cuenta.max' => 'El numero de cuenta debe tener un maximo de 20 caracteres.',
        'numero_cuenta.min' => 'El numero de cuenta debe tener un minimo de 20 caracteres.',
    ];

    protected array $rules = [
        'codigo_banco' => 'required|string',
        'numero_cuenta' => 'required|string|max:20|min:20',
    ];

    public function saveBankData(): void
    {
        $this->validate();

        $this->associate->update(
            [
                'codigo_banco' => strtoupper((string) $this->codigo_banco),
                'numero_cuenta' => strtoupper((string) $this->numero_cuenta),
            ]
        );

        \Flux::toast(variant: 'success', heading: 'Asociado actualizado', text: 'Datos bancarios actualizados correctamente.');
    }

    public function render()
    {
        return view('livewire.admin.associates.tabs.bank-data');
    }
}
