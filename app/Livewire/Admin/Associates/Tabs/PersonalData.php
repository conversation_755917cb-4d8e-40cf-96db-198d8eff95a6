<?php

namespace App\Livewire\Admin\Associates\Tabs;

use App\Models\Asociado;
use Jan<PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class PersonalData extends Component
{
    use LivewireAlert;

    public Asociado $associate;

    public ?string $nombres = null;

    public ?string $apellidos = null;

    public string $cedula_soc;

    public ?string $fecha_nac = null;

    public string $numero_telefpers;

    public ?string $numero_telefofi = null;

    public ?string $numero_telefcont = null;

    public string $email;

    public string $sexo;

    public string $direccion;

    public int $estado_civil;

    public $generos;

    public $estados_civiles;

    protected array $rules = [
        'nombres' => 'required|string|max:100',
        'apellidos' => 'required|string|max:100',
        'cedula_soc' => 'required|string|max:20',
        'fecha_nac' => 'nullable|date',
        'numero_telefpers' => 'required|string|max:20',
        'numero_telefofi' => 'nullable|string|max:20',
        'numero_telefcont' => 'nullable|string|max:20',
        'email' => 'required|email|max:100',
        'direccion' => 'required|string|max:100',
        'sexo' => 'required|numeric|max:2',
        'estado_civil' => 'required|numeric',
    ];

    protected array $messages = [
        'nombres.required' => 'El campo nombres es requerido',
        'apellidos.required' => 'El campo apellidos es requerido',
        'cedula_soc.required' => 'El campo cédula es requerido',
        'numero_telefpers.required' => 'El campo teléfono es requerido',
        'numero_telefofi.required' => 'El campo teléfono de oficina es requerido',
        'numero_telefcont.required' => 'El campo teléfono de contacto es requerido',
        'email.required' => 'El campo email es requerido',
        'direccion.required' => 'El campo dirección es requerido',
        'email.email' => 'El campo email debe ser un email válido',
        'fecha_nac.date' => 'El campo fecha de nacimiento debe ser una fecha válida',
        'fecha_nac.nullable' => 'El campo fecha de nacimiento es opcional',
        'sexo.required' => 'El campo género es requerido',
        'sexo.numeric' => 'El campo género debe ser un número',
        'estado_civil.required' => 'El campo estado civil es requerido',
        'estado_civil.numeric' => 'El campo estado civil debe ser un número',

    ];

    public function mount(): void
    {
        // obtengo los generos
        $this->generos = \DB::table('codigos_varios')->select('codigo_v', 'descri_v')->where('ref_v', 'A')->get();
        // obtner estatos civiles
        $this->estados_civiles = \DB::table('codigos_varios')->select('codigo_v', 'descri_v')->where('ref_v', 'AA')->get();

        $this->nombres = $this->associate->nombres;
        $this->apellidos = $this->associate->apellidos;
        $this->cedula_soc = $this->associate->cedula_soc;
        $this->fecha_nac = $this->associate->fecha_nac;
        $this->numero_telefpers = $this->associate->numero_telefpers;
        $this->numero_telefofi = $this->associate->numero_telefofi;
        $this->numero_telefcont = $this->associate->numero_telefcont;
        $this->email = $this->associate->email;
        $this->direccion = $this->associate->direccion;
        $this->sexo = $this->associate->sexo;
        $this->estado_civil = $this->associate->estado_civil;
    }

    public function savePersonalData(): void
    {
        $this->validate();

        $this->associate->update(
            [
                'nombres' => strtoupper((string) $this->nombres),
                'apellidos' => strtoupper((string) $this->apellidos),
                'cedula_soc' => strtoupper($this->cedula_soc),
                'fecha_nac' => $this->fecha_nac,
                'numero_telefpers' => strtoupper($this->numero_telefpers),
                'numero_telefofi' => strtoupper((string) $this->numero_telefofi),
                'numero_telefcont' => strtoupper((string) $this->numero_telefcont),
                'email' => $this->email,
                'direccion' => strtoupper($this->direccion),
                'sexo' => $this->sexo,
                'estado_civil' => $this->estado_civil,
            ]
        );
        \Flux::toast(variant: 'success', heading: 'Asociado actualizado', text: 'Datos personales actualizados correctamente.');
    }

    public function render(): \Illuminate\Contracts\View\View
    {
        return view('livewire.admin.associates.tabs.personal-data');
    }
}
