<?php

namespace App\Livewire\Admin\Associates\Components\Modals;

use App\Models\AssociateFamily;
use App\Models\CodigosVarios;
use App\Models\Family;
use App\Models\Relationship;
use Illuminate\Contracts\View\View;
use <PERSON>tinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class CreateFamilyModal extends Component
{
    use LivewireAlert;

    public $associate;

    public $relationships;

    public $genders;

    public $marital_status;

    public $family_types;

    public $form = [
        'document_type' => 'V',
        'document_number' => '',
        'first_name' => '',
        'last_name' => '',
        'gender_code' => '',
        'address' => '',
        'birth_date' => '',
        'home_phone' => '',
        'mobile_phone' => '',
        'occupation' => '',
        'marital_status' => '',
        'relationship_id' => '',
        'status' => true,
    ];

    protected $rules = [
        'form.document_number' => 'required|string|max:20',
        'form.first_name' => 'required|string|max:100',
        'form.last_name' => 'required|string|max:100',
        'form.gender_code' => 'required|in:1,2',
        'form.address' => 'required|string',
        'form.birth_date' => 'required|date',
        'form.home_phone' => 'nullable|string|max:20',
        'form.mobile_phone' => 'nullable|string|max:20',
        'form.occupation' => 'nullable|string|max:100',
        'form.marital_status' => 'required',
        'form.relationship_id' => 'required|exists:relationships,id',
    ];

    protected function messages(): array
    {
        return [
            'form.document_number.required' => __('associates.document_required'),
            'form.first_name.required' => __('associates.first_name_required'),
            'form.last_name.required' => __('associates.last_name_required'),
            'form.gender_code.required' => __('validation.required', ['attribute' => __('associates.gender')]),
            'form.address.required' => __('validation.required', ['attribute' => __('associates.address')]),
            'form.birth_date.required' => __('validation.required', ['attribute' => __('associates.birth_date')]),
            'form.marital_status.required' => __('validation.required', ['attribute' => __('associates.marital_status')]),
            'form.relationship_id.required' => __('validation.required', ['attribute' => __('associates.relationship')]),
        ];
    }

    public function mount($associate): void
    {
        $this->associate = $associate;
        $this->relationships = Relationship::all();
        // generos
        $this->genders = \DB::table('codigos_varios')->select('codigo_v', 'descri_v')->where('ref_v', 'A')->get();
        // marital_status
        $this->marital_status = CodigosVarios::where('ref_v', 'AA')->get();
        // tipo de carga
        $this->family_types = \DB::table('family_types')->select('id', 'description')->get();
    }

    public function save(): void
    {
        $this->validate();

        // Crear el familiar
        $family = Family::create([
            'document_type' => 'V',
            'document_number' => $this->form['document_number'],
            'first_name' => $this->form['first_name'],
            'last_name' => $this->form['last_name'],
            'gender_code' => $this->form['gender_code'],
            'address' => $this->form['address'],
            'birth_date' => $this->form['birth_date'],
            'home_phone' => $this->form['home_phone'],
            'mobile_phone' => $this->form['mobile_phone'],
            'occupation' => $this->form['occupation'],
            'marital_status' => $this->form['marital_status'],
            'status' => true,
        ]);

        // Crear la relación entre el asociado y el familiar
        AssociateFamily::create([
            'associate_id' => $this->associate->idasociado,
            'family_id' => $family->id,
            'family_type_id' => $this->form['family_type_id'],
            'relationship_id' => $this->form['relationship_id'],
        ]);

        // Limpiar el formulario
        $this->resetForm();

        \Flux::toast(variant: 'success', heading: __('associates.add_family_member'), text: __('associates.family_member_added'));
        \Flux::modals()->close();
        $this->dispatch('familyMemberAdded');
    }

    /**
     * Reset form fields to their default values
     */
    private function resetForm(): void
    {
        $this->form = [
            'document_type' => 'V',
            'document_number' => '',
            'first_name' => '',
            'last_name' => '',
            'gender_code' => '',
            'address' => '',
            'birth_date' => '',
            'home_phone' => '',
            'mobile_phone' => '',
            'occupation' => '',
            'marital_status' => '',
            'relationship_id' => '',
            'family_type_id' => '',
            'status' => true,
        ];

        // Reset validation errors
        $this->resetValidation();
    }

    public function render(): View
    {
        return view('livewire.admin.associates.components.modals.create-family-modal');
    }
}
