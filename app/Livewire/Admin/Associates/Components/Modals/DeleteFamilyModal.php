<?php

namespace App\Livewire\Admin\Associates\Components\Modals;

use App\Models\AssociateFamily;
use Flux;
use Illuminate\Contracts\View\View;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class DeleteFamilyModal extends Component
{
    use LivewireAlert;

    public $familyMemberId;

    public $showConfirmModal = false;

    protected $listeners = ['confirmDeleteFamilyMember' => 'confirmDelete'];

    /**
     * Show the confirmation modal
     */
    public function confirmDelete($familyMemberId): void
    {
        $this->familyMemberId = $familyMemberId;
        Flux::modal('delete-family-member')->show();
    }

    /**
     * Delete the family member
     */
    public function deleteFamilyMember(): void
    {
        // Find the family member
        $familyMember = AssociateFamily::find($this->familyMemberId);

        if (! $familyMember) {
            Flux::toast(
                variant: 'danger',
                heading: __('Error'),
                text: __('No se encontró el familiar')
            );

            return;
        }

        // Delete the family member
        $familyMember->delete();

        // Close the modal
        Flux::modal('delete-family-member')->close();

        // Show success message
        Flux::toast(
            variant: 'success',
            heading: __('associates.delete_family_member'),
            text: __('associates.family_member_deleted')
        );

        // Refresh the family members list
        $this->dispatch('familyMemberDeleted');
    }

    public function render(): View
    {
        return view('livewire.admin.associates.components.modals.delete-family-modal');
    }
}
