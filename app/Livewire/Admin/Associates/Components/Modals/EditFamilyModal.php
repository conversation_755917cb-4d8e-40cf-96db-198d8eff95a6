<?php

namespace App\Livewire\Admin\Associates\Components\Modals;

use App\Models\AssociateFamily;
use App\Models\Relationship;
use DB;
use Flux;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class EditFamilyModal extends Component
{
    use LivewireAlert;

    public $associateFamily;

    public $relationships;

    public $genders;

    public $marital_status;

    protected $listeners = ['editFamilyMember' => 'editFamilyMember'];

    public $form = [
        'document_type' => 'V',
        'document_number' => '',
        'first_name' => '',
        'last_name' => '',
        'gender_code' => '',
        'address' => '',
        'birth_date' => '',
        'home_phone' => '',
        'mobile_phone' => '',
        'occupation' => '',
        'marital_status' => '',
        'relationship_id' => '',
    ];

    protected $rules = [
        'form.document_number' => 'required|numeric',
        'form.first_name' => 'required|string|max:100',
        'form.last_name' => 'required|string|max:100',
        'form.gender_code' => 'required',
        'form.address' => 'required|string',
        'form.birth_date' => 'required|date',
        'form.home_phone' => 'nullable|string|max:20',
        'form.mobile_phone' => 'nullable|string|max:20',
        'form.occupation' => 'nullable|string|max:100',
        'form.marital_status' => 'required',
        'form.relationship_id' => 'required|exists:relationships,id',
    ];

    protected function messages(): array
    {
        return [
            'form.document_number.required' => __('associates.document_required'),
            'form.document_number.numeric' => __('validation.numeric', ['attribute' => __('associates.document_number')]),
            'form.first_name.required' => __('associates.first_name_required'),
            'form.last_name.required' => __('associates.last_name_required'),
            'form.gender_code.required' => __('validation.required', ['attribute' => __('associates.gender')]),
            'form.address.required' => __('validation.required', ['attribute' => __('associates.address')]),
            'form.birth_date.required' => __('validation.required', ['attribute' => __('associates.birth_date')]),
            'form.marital_status.required' => __('validation.required', ['attribute' => __('associates.marital_status')]),
            'form.relationship_id.required' => __('validation.required', ['attribute' => __('associates.relationship')]),
        ];
    }

    public function mount(): void
    {
        $this->relationships = Relationship::all();
        $this->genders = DB::table('codigos_varios')->where('ref_v', 'A')->get();
        $this->marital_status = DB::table('codigos_varios')->where('ref_v', 'AA')->get();

    }

    public function editFamilyMember(AssociateFamily $associateFamily): void
    {
        Flux::modal('edit-family-member')->show();

        $this->associateFamily = $associateFamily;
        $family = $this->associateFamily->family;

        $this->form = [
            'document_type' => $family->document_type ?: 'V',
            'document_number' => $family->document_number,
            'first_name' => $family->first_name,
            'last_name' => $family->last_name,
            'gender_code' => $family->gender_code,
            'address' => $family->address,
            'birth_date' => $family->birth_date,
            'home_phone' => $family->home_phone,
            'mobile_phone' => $family->mobile_phone,
            'occupation' => $family->occupation,
            'marital_status' => $family->marital_status,
            'relationship_id' => $associateFamily->relationship_id,
        ];
    }

    public function save(): void
    {
        $this->validate();

        // Actualizar el familiar
        $this->associateFamily->family->update([
            'document_type' => $this->form['document_type'],
            'document_number' => $this->form['document_number'],
            'first_name' => $this->form['first_name'],
            'last_name' => $this->form['last_name'],
            'gender_code' => $this->form['gender_code'],
            'address' => $this->form['address'],
            'birth_date' => $this->form['birth_date'],
            'home_phone' => $this->form['home_phone'],
            'mobile_phone' => $this->form['mobile_phone'],
            'occupation' => $this->form['occupation'],
            'marital_status' => $this->form['marital_status'],
        ]);

        // Actualizar la relación
        $this->associateFamily->update([
            'relationship_id' => $this->form['relationship_id'],
        ]);

        // Limpiar el formulario
        $this->resetForm();

        Flux::toast(variant: 'success', heading: __('associates.edit_family_member'), text: __('associates.family_member_updated'));
        Flux::modals()->close();
        $this->dispatch('familyMemberUpdated');
    }

    /**
     * Reset form fields to their default values
     */
    private function resetForm(): void
    {
        $this->form = [
            'document_type' => 'V',
            'document_number' => '',
            'first_name' => '',
            'last_name' => '',
            'gender_code' => '',
            'address' => '',
            'birth_date' => '',
            'home_phone' => '',
            'mobile_phone' => '',
            'occupation' => '',
            'marital_status' => '',
            'relationship_id' => '',
        ];

        // Reset validation errors
        $this->resetValidation();
    }

    public function render(): View
    {
        return view('livewire.admin.associates.components.modals.edit-family-modal');
    }
}
