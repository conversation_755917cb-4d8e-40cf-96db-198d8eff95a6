<?php

namespace App\Livewire\Admin;

use App\Models\ElectoralEvent;
use App\Models\Estate;
use App\Models\Municipality;
use App\Models\Person;
use App\Models\Tracking;
use App\Services\DashboardService;
use App\Services\DataIntegrationService;
use Livewire\Attributes\Layout;
use Livewire\Component;

class Dashboard extends Component
{
    /**
     * The data integration service instance.
     */
    protected \App\Services\DataIntegrationService $dataIntegrationService;

    /**
     * The dashboard service instance.
     */
    protected \App\Services\DashboardService $dashboardService;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        $this->dataIntegrationService = new DataIntegrationService;
        $this->dashboardService = new DashboardService;
    }

    /**
     * Get the statistics for the dashboard.
     */
    public function getStats(): array
    {
        return $this->dashboardService->getDashboardStats();
    }

    /**
     * Get persons statistics by location.
     */
    public function getPersonsByLocation(): array
    {
        return $this->dashboardService->getPersonsByLocation();
    }

    /**
     * Get persons statistics by role.
     */
    public function getPersonsByRole(): array
    {
        return $this->dashboardService->getPersonsByRole();
    }

    /**
     * Get electoral events statistics.
     */
    public function getElectoralEventsStats(): array
    {
        return $this->dashboardService->getElectoralEventsStats();
    }

    /**
     * Get tracking statistics.
     */
    public function getTrackingStats(): array
    {
        return $this->dashboardService->getTrackingStats();
    }

    /**
     * Get patrol statistics.
     */
    public function getPatrolStats(): array
    {
        return $this->dashboardService->getPatrolStats();
    }

    /**
     * Get upcoming events.
     */
    public function getUpcomingEvents()
    {
        return ElectoralEvent::upcoming()
            ->with(['estate', 'municipality', 'parish', 'organizer'])
            ->limit(5)
            ->get();
    }

    /**
     * Get recent trackings.
     */
    public function getRecentTrackings()
    {
        return Tracking::with(['person', 'user'])
            ->orderBy('tracking_date', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Get top estates by person count.
     */
    public function getTopEstates()
    {
        return Estate::withCount('persons')
            ->orderBy('persons_count', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Get top municipalities by person count.
     */
    public function getTopMunicipalities()
    {
        return Municipality::withCount('persons')
            ->orderBy('persons_count', 'desc')
            ->limit(5)
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render()
    {
        return view('livewire.admin.dashboard', [
            'stats' => $this->getStats(),
            'upcomingEvents' => $this->getUpcomingEvents(),
            'recentTrackings' => $this->getRecentTrackings(),
            'topEstates' => $this->getTopEstates(),
            'topMunicipalities' => $this->getTopMunicipalities(),
            'personsByLocation' => $this->getPersonsByLocation(),
            'personsByRole' => $this->getPersonsByRole(),
            'electoralEventsStats' => $this->getElectoralEventsStats(),
            'trackingStats' => $this->getTrackingStats(),
            'patrolStats' => $this->getPatrolStats(),
        ]);
    }
}
