<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\ElectoralEventAttendance;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralMobilizationDetail;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;

class ElectoralReports extends Component
{
    public $reportType = 'attendance';

    public $dateRange = [
        'start' => '',
        'end' => '',
    ];

    public $estateFilter;

    public $municipalityFilter;

    public function mount(): void
    {
        // Establecer fechas predeterminadas (último mes)
        $this->dateRange['end'] = now()->format('Y-m-d');
        $this->dateRange['start'] = now()->subMonth()->format('Y-m-d');
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function municipalities()
    {
        if (! $this->estateFilter) {
            return collect();
        }

        return Estate::find($this->estateFilter)->municipalities()->orderBy('name')->get();
    }

    #[Computed]
    public function attendanceStats(): array
    {
        $query = ElectoralEventAttendance::query()
            ->select(DB::raw('DATE(check_in_time) as date'), DB::raw('count(*) as count'))
            ->whereNotNull('check_in_time')
            ->when($this->dateRange['start'], fn ($q) => $q->where('check_in_time', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('check_in_time', '<=', $this->dateRange['end'].' 23:59:59'))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->groupBy(DB::raw('DATE(check_in_time)'))
            ->orderBy('date');

        $data = $query->get();

        // Preparar datos para el gráfico
        $labels = $data->pluck('date')->map(fn ($date): string => date('d/m/Y', strtotime((string) $date)))->toArray();
        $values = $data->pluck('count')->toArray();

        // Calcular totales
        $totalEvents = ElectoralEvent::when($this->estateFilter, fn ($q) => $q->where('estate_id', $this->estateFilter))
            ->when($this->municipalityFilter, fn ($q) => $q->where('municipality_id', $this->municipalityFilter))
            ->count();

        $totalAttendees = ElectoralEventAttendance::when($this->dateRange['start'], fn ($q) => $q->where('check_in_time', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('check_in_time', '<=', $this->dateRange['end'].' 23:59:59'))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->count();

        $confirmedAttendees = ElectoralEventAttendance::where('status', 'confirmed')
            ->when($this->dateRange['start'], fn ($q) => $q->where('check_in_time', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('check_in_time', '<=', $this->dateRange['end'].' 23:59:59'))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->count();

        $attendedAttendees = ElectoralEventAttendance::where('status', 'attended')
            ->when($this->dateRange['start'], fn ($q) => $q->where('check_in_time', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('check_in_time', '<=', $this->dateRange['end'].' 23:59:59'))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('event', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->count();

        return [
            'chart' => [
                'labels' => $labels,
                'values' => $values,
            ],
            'totals' => [
                'events' => $totalEvents,
                'attendees' => $totalAttendees,
                'confirmed' => $confirmedAttendees,
                'attended' => $attendedAttendees,
            ],
        ];
    }

    #[Computed]
    public function mobilizationStats(): array
    {
        $query = ElectoralMobilizationDetail::query()
            ->select('status', DB::raw('count(*) as count'))
            ->when($this->dateRange['start'], function ($query): void {
                $query->whereHas('mobilization', function ($q): void {
                    $q->where('mobilization_date', '>=', $this->dateRange['start']);
                });
            })
            ->when($this->dateRange['end'], function ($query): void {
                $query->whereHas('mobilization', function ($q): void {
                    $q->where('mobilization_date', '<=', $this->dateRange['end']);
                });
            })
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('mobilization.votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('mobilization.votingCenter', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->groupBy('status');

        $statusData = $query->get();

        // Preparar datos para el gráfico de estado
        $statusLabels = ['Pendiente', 'Contactado', 'Confirmado', 'Movilizado', 'Votó', 'No Asistió'];
        $statusValues = [
            $statusData->where('status', 'pending')->first()?->count ?? 0,
            $statusData->where('status', 'contacted')->first()?->count ?? 0,
            $statusData->where('status', 'confirmed')->first()?->count ?? 0,
            $statusData->where('status', 'mobilized')->first()?->count ?? 0,
            $statusData->where('status', 'voted')->first()?->count ?? 0,
            $statusData->where('status', 'no_show')->first()?->count ?? 0,
        ];

        // Calcular totales
        $totalMobilizations = ElectoralMobilization::when($this->dateRange['start'], fn ($q) => $q->where('mobilization_date', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('mobilization_date', '<=', $this->dateRange['end']))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->count();

        $targetVoters = ElectoralMobilization::when($this->dateRange['start'], fn ($q) => $q->where('mobilization_date', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('mobilization_date', '<=', $this->dateRange['end']))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->sum('target_voters');

        $mobilizedVoters = ElectoralMobilization::when($this->dateRange['start'], fn ($q) => $q->where('mobilization_date', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('mobilization_date', '<=', $this->dateRange['end']))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->sum('mobilized_voters');

        $confirmedVotes = ElectoralMobilization::when($this->dateRange['start'], fn ($q) => $q->where('mobilization_date', '>=', $this->dateRange['start']))
            ->when($this->dateRange['end'], fn ($q) => $q->where('mobilization_date', '<=', $this->dateRange['end']))
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->sum('confirmed_votes');

        return [
            'status_chart' => [
                'labels' => $statusLabels,
                'values' => $statusValues,
            ],
            'totals' => [
                'mobilizations' => $totalMobilizations,
                'target_voters' => $targetVoters,
                'mobilized_voters' => $mobilizedVoters,
                'confirmed_votes' => $confirmedVotes,
            ],
        ];
    }

    #[Computed]
    public function votingStats(): array
    {
        // Obtener centros de votación con estadísticas
        $votingCenters = ElectoralVotingCenter::with(['estate', 'municipality'])
            ->when($this->estateFilter, fn ($q) => $q->where('estate_id', $this->estateFilter))
            ->when($this->municipalityFilter, fn ($q) => $q->where('municipality_id', $this->municipalityFilter))
            ->where('status', 'active')
            ->get()
            ->map(function ($center): array {
                // Obtener movilizaciones para este centro en el rango de fechas
                $mobilizations = ElectoralMobilization::where('voting_center_id', $center->id)
                    ->when($this->dateRange['start'], fn ($q) => $q->where('mobilization_date', '>=', $this->dateRange['start']))
                    ->when($this->dateRange['end'], fn ($q) => $q->where('mobilization_date', '<=', $this->dateRange['end']))
                    ->get();

                $targetVoters = $mobilizations->sum('target_voters');
                $mobilizedVoters = $mobilizations->sum('mobilized_voters');
                $confirmedVotes = $mobilizations->sum('confirmed_votes');

                $mobilizationProgress = $targetVoters > 0 ? min(100, round(($mobilizedVoters / $targetVoters) * 100)) : 0;
                $votingProgress = $targetVoters > 0 ? min(100, round(($confirmedVotes / $targetVoters) * 100)) : 0;

                return [
                    'id' => $center->id,
                    'name' => $center->name,
                    'code' => $center->code,
                    'estate' => $center->estate->name ?? 'N/A',
                    'municipality' => $center->municipality->name ?? 'N/A',
                    'total_voters' => $center->total_voters,
                    'target_voters' => $targetVoters,
                    'mobilized_voters' => $mobilizedVoters,
                    'confirmed_votes' => $confirmedVotes,
                    'mobilization_progress' => $mobilizationProgress,
                    'voting_progress' => $votingProgress,
                ];
            })
            ->sortByDesc('voting_progress')
            ->values();

        // Calcular totales
        $totalVoters = $votingCenters->sum('total_voters');
        $targetVoters = $votingCenters->sum('target_voters');
        $mobilizedVoters = $votingCenters->sum('mobilized_voters');
        $confirmedVotes = $votingCenters->sum('confirmed_votes');

        $mobilizationProgress = $targetVoters > 0 ? min(100, round(($mobilizedVoters / $targetVoters) * 100)) : 0;
        $votingProgress = $targetVoters > 0 ? min(100, round(($confirmedVotes / $targetVoters) * 100)) : 0;
        $overallProgress = $totalVoters > 0 ? min(100, round(($confirmedVotes / $totalVoters) * 100)) : 0;

        return [
            'centers' => $votingCenters,
            'totals' => [
                'total_voters' => $totalVoters,
                'target_voters' => $targetVoters,
                'mobilized_voters' => $mobilizedVoters,
                'confirmed_votes' => $confirmedVotes,
                'mobilization_progress' => $mobilizationProgress,
                'voting_progress' => $votingProgress,
                'overall_progress' => $overallProgress,
            ],
        ];
    }

    /**
     * Exportar los datos del reporte actual a CSV
     */
    public function exportToCSV()
    {
        $filename = 'reporte_electoral_'.$this->reportType.'_'.date('Y-m-d').'.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ];

        $callback = function (): void {
            $file = fopen('php://output', 'w');

            // Encabezados del CSV según el tipo de reporte
            if ($this->reportType === 'attendance') {
                fputcsv($file, ['Fecha', 'Total Asistentes', 'Confirmados', 'Asistieron']);

                // Datos de asistencia por día
                $data = ElectoralEventAttendance::query()
                    ->select(DB::raw('DATE(check_in_time) as date'), DB::raw('count(*) as count'))
                    ->whereNotNull('check_in_time')
                    ->when($this->dateRange['start'], fn ($q) => $q->where('check_in_time', '>=', $this->dateRange['start']))
                    ->when($this->dateRange['end'], fn ($q) => $q->where('check_in_time', '<=', $this->dateRange['end'].' 23:59:59'))
                    ->when($this->estateFilter, function ($query): void {
                        $query->whereHas('event', function ($q): void {
                            $q->where('estate_id', $this->estateFilter);
                        });
                    })
                    ->when($this->municipalityFilter, function ($query): void {
                        $query->whereHas('event', function ($q): void {
                            $q->where('municipality_id', $this->municipalityFilter);
                        });
                    })
                    ->groupBy(DB::raw('DATE(check_in_time)'))
                    ->orderBy('date')
                    ->get();

                foreach ($data as $row) {
                    $confirmed = ElectoralEventAttendance::where('status', 'confirmed')
                        ->whereDate('check_in_time', $row->date)
                        ->count();

                    $attended = ElectoralEventAttendance::where('status', 'attended')
                        ->whereDate('check_in_time', $row->date)
                        ->count();

                    fputcsv($file, [
                        $row->date,
                        $row->count,
                        $confirmed,
                        $attended,
                    ]);
                }
            } elseif ($this->reportType === 'mobilization') {
                fputcsv($file, ['Estado', 'Total', 'Porcentaje']);

                // Datos de movilización por estado
                $statusData = ElectoralMobilizationDetail::query()
                    ->select('status', DB::raw('count(*) as count'))
                    ->when($this->dateRange['start'], function ($query): void {
                        $query->whereHas('mobilization', function ($q): void {
                            $q->where('mobilization_date', '>=', $this->dateRange['start']);
                        });
                    })
                    ->when($this->dateRange['end'], function ($query): void {
                        $query->whereHas('mobilization', function ($q): void {
                            $q->where('mobilization_date', '<=', $this->dateRange['end']);
                        });
                    })
                    ->when($this->estateFilter, function ($query): void {
                        $query->whereHas('mobilization.votingCenter', function ($q): void {
                            $q->where('estate_id', $this->estateFilter);
                        });
                    })
                    ->when($this->municipalityFilter, function ($query): void {
                        $query->whereHas('mobilization.votingCenter', function ($q): void {
                            $q->where('municipality_id', $this->municipalityFilter);
                        });
                    })
                    ->groupBy('status')
                    ->get();

                $total = $statusData->sum('count');
                $statusLabels = [
                    'pending' => 'Pendiente',
                    'contacted' => 'Contactado',
                    'confirmed' => 'Confirmado',
                    'mobilized' => 'Movilizado',
                    'voted' => 'Votó',
                    'no_show' => 'No Asistió',
                ];

                foreach ($statusData as $row) {
                    $percentage = $total > 0 ? round(($row->count / $total) * 100, 2) : 0;

                    fputcsv($file, [
                        $statusLabels[$row->status] ?? $row->status,
                        $row->count,
                        $percentage.'%',
                    ]);
                }
            } else { // voting
                fputcsv($file, ['Centro de Votación', 'Código', 'Estado', 'Municipio', 'Total Votantes', 'Meta', 'Movilizados', 'Votos Confirmados', '% Movilización', '% Votación']);

                foreach ($this->votingStats['centers'] as $center) {
                    fputcsv($file, [
                        $center['name'],
                        $center['code'],
                        $center['estate'],
                        $center['municipality'],
                        $center['total_voters'],
                        $center['target_voters'],
                        $center['mobilized_voters'],
                        $center['confirmed_votes'],
                        $center['mobilization_progress'].'%',
                        $center['voting_progress'].'%',
                    ]);
                }
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.electoral-reports');
    }
}
