<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class VotingCentersElectoral extends Component
{
    use WithPagination;

    public $search = '';

    public $perPage = 10;

    public $sortField = 'name';

    public $sortDirection = 'asc';

    public $estateFilter = '';

    public $statusFilter = '';

    // Modal para crear centro de votación
    public $showCreateCenterModal = false;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string|max:255')]
    public $code = '';

    #[Rule('nullable|string')]
    public $address = '';

    #[Rule('nullable|exists:estates,id')]
    public $estate_id;

    #[Rule('nullable|exists:municipalities,id')]
    public $municipality_id;

    #[Rule('nullable|exists:parishes,id')]
    public $parish_id;

    #[Rule('nullable|string')]
    public $location_coordinates = '';

    #[Rule('nullable|integer|min:1')]
    public $total_voters;

    #[Rule('required|in:active,inactive')]
    public $status = 'active';

    // Modal para editar centro de votación
    public $showEditCenterModal = false;

    public $editingCenter;

    // Listeners para los eventos del selector de ubicación
    protected function getListeners()
    {
        return [
            'estate-changed' => 'updateEstate',
            'municipality-changed' => 'updateMunicipality',
            'parish-changed' => 'updateParish',
        ];
    }

    public function updateEstate($value): void
    {
        $this->estate_id = $value;
    }

    public function updateMunicipality($value): void
    {
        $this->municipality_id = $value;
    }

    public function updateParish($value): void
    {
        $this->parish_id = $value;
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingEstateFilter(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function votingCenters()
    {
        return ElectoralVotingCenter::query()
            ->with(['estate', 'municipality', 'parish'])
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('code', 'like', "%{$this->search}%")
                        ->orWhere('address', 'like', "%{$this->search}%");
                });
            })
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }

    #[Computed]
    public function centerStats(): array
    {
        return [
            'total' => ElectoralVotingCenter::count(),
            'active' => ElectoralVotingCenter::where('status', 'active')->count(),
            'inactive' => ElectoralVotingCenter::where('status', 'inactive')->count(),
            'total_voters' => ElectoralVotingCenter::sum('total_voters'),
        ];
    }

    public function openCreateCenterModal(): void
    {
        $this->reset([
            'name', 'code', 'address', 'estate_id', 'municipality_id', 'parish_id',
            'location_coordinates', 'total_voters', 'status',
        ]);
        $this->status = 'active';
        $this->showCreateCenterModal = true;
    }

    public function closeCreateCenterModal(): void
    {
        $this->showCreateCenterModal = false;
    }

    public function createCenter(): void
    {
        $validated = $this->validate();

        // Asegurarse de que los campos de relaciones sean null cuando están vacíos
        $fieldsToNullify = ['estate_id', 'municipality_id', 'parish_id'];

        foreach ($fieldsToNullify as $field) {
            if (empty($validated[$field]) || $validated[$field] === '') {
                $validated[$field] = null;
            }
        }

        ElectoralVotingCenter::create($validated);

        \Flux::toast(
            text: 'Centro de votación creado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeCreateCenterModal();
    }

    public function openEditCenterModal($centerId): void
    {
        $this->editingCenter = ElectoralVotingCenter::find($centerId);

        if ($this->editingCenter) {
            $this->name = $this->editingCenter->name;
            $this->code = $this->editingCenter->code;
            $this->address = $this->editingCenter->address;
            $this->estate_id = $this->editingCenter->estate_id;
            $this->municipality_id = $this->editingCenter->municipality_id;
            $this->parish_id = $this->editingCenter->parish_id;
            $this->location_coordinates = $this->editingCenter->location_coordinates;
            $this->total_voters = $this->editingCenter->total_voters;
            $this->status = $this->editingCenter->status;

            $this->showEditCenterModal = true;
        }
    }

    public function closeEditCenterModal(): void
    {
        $this->showEditCenterModal = false;
        $this->editingCenter = null;
    }

    public function updateCenter(): void
    {
        if (! $this->editingCenter) {
            return;
        }

        $validated = $this->validate();

        // Asegurarse de que los campos de relaciones sean null cuando están vacíos
        $fieldsToNullify = ['estate_id', 'municipality_id', 'parish_id'];

        foreach ($fieldsToNullify as $field) {
            if (empty($validated[$field]) || $validated[$field] === '') {
                $validated[$field] = null;
            }
        }

        $this->editingCenter->update($validated);

        \Flux::toast(
            text: 'Centro de votación actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeEditCenterModal();
    }

    public function toggleCenterStatus($centerId): void
    {
        $center = ElectoralVotingCenter::find($centerId);

        if (! $center) {
            return;
        }

        $center->status = $center->status === 'active' ? 'inactive' : 'active';
        $center->save();

        \Flux::toast(
            text: 'Estado del centro de votación actualizado',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.voting-centers-electoral');
    }
}
