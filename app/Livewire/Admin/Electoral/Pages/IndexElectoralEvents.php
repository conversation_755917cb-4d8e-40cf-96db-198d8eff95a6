<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\Estate;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

class IndexElectoralEvents extends Component
{
    use WithPagination;

    public $search = '';

    public $perPage = 10;

    public $sortField = 'start_date';

    public $sortDirection = 'desc';

    public $typeFilter = '';

    public $statusFilter = '';

    public $estateFilter = '';

    public $dateFilter = '';

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingTypeFilter(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    public function updatingEstateFilter(): void
    {
        $this->resetPage();
    }

    public function updatingDateFilter(): void
    {
        $this->resetPage();
    }

    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function clearFilters(): void
    {
        $this->reset(['search', 'typeFilter', 'statusFilter', 'estateFilter', 'dateFilter']);
        $this->resetPage();
    }

    #[Computed]
    public function events()
    {
        return ElectoralEvent::query()
            ->with(['estate', 'municipality', 'parish', 'organizer'])
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('description', 'like', "%{$this->search}%")
                        ->orWhere('location', 'like', "%{$this->search}%");
                });
            })
            ->when($this->typeFilter, function ($query): void {
                $query->where('type', $this->typeFilter);
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->when($this->dateFilter, function ($query): void {
                if ($this->dateFilter === 'upcoming') {
                    $query->upcoming();
                } elseif ($this->dateFilter === 'past') {
                    $query->past();
                } elseif ($this->dateFilter === 'today') {
                    $query->whereDate('start_date', now()->toDateString());
                } elseif ($this->dateFilter === 'week') {
                    $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()]);
                } elseif ($this->dateFilter === 'month') {
                    $query->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()]);
                }
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function eventStats(): array
    {
        return [
            'total' => ElectoralEvent::count(),
            'upcoming' => ElectoralEvent::upcoming()->count(),
            'completed' => ElectoralEvent::withStatus('completed')->count(),
            'in_progress' => ElectoralEvent::withStatus('in_progress')->count(),
            'cancelled' => ElectoralEvent::withStatus('cancelled')->count(),
        ];
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.index-electoral-events');
    }
}
