<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralMobilizationDetail;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

class ShowElectoralMobilization extends Component
{
    use WithPagination;

    public ElectoralMobilization $mobilization;

    // Búsqueda y filtros para personas movilizadas
    public $search = '';

    public $perPage = 10;

    public $statusFilter = '';

    // Modal para agregar personas a movilizar
    public $showAddPersonModal = false;

    public $searchPerson = '';

    public $selectedPersons = [];

    public function mount(ElectoralMobilization $mobilization): void
    {
        $this->mobilization = $mobilization->load(['group', 'votingCenter', 'coordinator']);
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function mobilizationDetails()
    {
        return $this->mobilization->details()
            ->with('person')
            ->when($this->search, function ($query): void {
                $query->whereHas('person', function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('cedula', 'like', "%{$this->search}%");
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($this->perPage);
    }

    #[Computed]
    public function mobilizationStats(): array
    {
        return [
            'total' => $this->mobilization->details()->count(),
            'pending' => $this->mobilization->details()->where('status', 'pending')->count(),
            'contacted' => $this->mobilization->details()->where('status', 'contacted')->count(),
            'confirmed' => $this->mobilization->details()->where('status', 'confirmed')->count(),
            'mobilized' => $this->mobilization->details()->where('status', 'mobilized')->count(),
            'voted' => $this->mobilization->details()->where('status', 'voted')->count(),
            'no_show' => $this->mobilization->details()->where('status', 'no_show')->count(),
        ];
    }

    #[Computed]
    public function potentialPersons()
    {
        if (empty($this->searchPerson)) {
            return collect();
        }

        // Obtener los IDs de las personas que ya están en la movilización
        $existingPersonIds = $this->mobilization->details()->pluck('person_id')->toArray();

        // Si hay un grupo asociado, buscar primero en los miembros del grupo
        if ($this->mobilization->group_id) {
            $groupMembers = $this->mobilization->group->members()
                ->where('status', 'active')
                ->whereHas('person', function ($query): void {
                    $query->where('name', 'like', "%{$this->searchPerson}%")
                        ->orWhere('cedula', 'like', "%{$this->searchPerson}%");
                })
                ->whereNotIn('person_id', $existingPersonIds)
                ->with('person')
                ->limit(10)
                ->get()
                ->map(fn($member) => $member->person);

            if ($groupMembers->isNotEmpty()) {
                return $groupMembers;
            }
        }

        // Si no hay resultados del grupo o no hay grupo, buscar en todas las personas
        return Person::query()
            ->whereNotIn('id', $existingPersonIds)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchPerson}%")
                    ->orWhere('cedula', 'like', "%{$this->searchPerson}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    public function openAddPersonModal(): void
    {
        $this->showAddPersonModal = true;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function closeAddPersonModal(): void
    {
        $this->showAddPersonModal = false;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function togglePersonSelection($personId): void
    {
        if (in_array($personId, $this->selectedPersons)) {
            $this->selectedPersons = array_diff($this->selectedPersons, [$personId]);
        } else {
            $this->selectedPersons[] = $personId;
        }
    }

    public function addPersons(): void
    {
        if (empty($this->selectedPersons)) {
            \Flux::toast(
                text: 'Debes seleccionar al menos una persona',
                variant: 'error',
                heading: 'Error'
            );

            return;
        }

        foreach ($this->selectedPersons as $personId) {
            $this->mobilization->details()->create([
                'person_id' => $personId,
                'status' => 'pending',
                'registered_by' => Auth::id(),
            ]);
        }

        // Actualizar estadísticas de la movilización
        $this->mobilization->updateStatistics();

        \Flux::toast(
            text: count($this->selectedPersons).' personas agregadas exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeAddPersonModal();
    }

    public function updatePersonStatus($detailId, $status): void
    {
        $detail = ElectoralMobilizationDetail::find($detailId);

        if (! $detail) {
            return;
        }

        switch ($status) {
            case 'contacted':
                $detail->markAsContacted();
                break;
            case 'confirmed':
                $detail->markAsConfirmed();
                break;
            case 'mobilized':
                $detail->markAsMobilized();
                break;
            case 'voted':
                $detail->markAsVoted();
                break;
            case 'no_show':
                $detail->markAsNoShow();
                break;
            default:
                $detail->status = $status;
                $detail->save();
                $this->mobilization->updateStatistics();
                break;
        }

        \Flux::toast(
            text: 'Estado actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    public function removePerson($detailId): void
    {
        $detail = ElectoralMobilizationDetail::find($detailId);

        if (! $detail) {
            return;
        }

        $personName = $detail->person->name;

        $detail->delete();

        // Actualizar estadísticas de la movilización
        $this->mobilization->updateStatistics();

        \Flux::toast(
            text: "$personName ha sido eliminado de la movilización",
            variant: 'success',
            heading: 'Éxito'
        );
    }

    public function updateMobilizationStatus($status): void
    {
        $this->mobilization->status = $status;
        $this->mobilization->save();

        \Flux::toast(
            text: 'Estado de movilización actualizado',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.show-electoral-mobilization');
    }
}
