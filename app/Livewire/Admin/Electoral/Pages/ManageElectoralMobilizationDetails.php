<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralMobilizationDetail;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class ManageElectoralMobilizationDetails extends Component
{
    use WithPagination;

    public ElectoralMobilization $mobilization;

    // Filtros y búsqueda
    public $search = '';

    public $perPage = 10;

    public $statusFilter = '';

    // Modal para agregar personas
    public $showAddPersonsModal = false;

    public $searchPerson = '';

    public $selectedPersons = [];

    // Modal para editar detalle
    public $showEditDetailModal = false;

    public $editingDetail;

    #[Rule('required|in:pending,contacted,confirmed,mobilized,voted,cancelled')]
    public $detailStatus = 'pending';

    #[Rule('nullable|string')]
    public $detailNotes = '';

    public function mount(ElectoralMobilization $mobilization): void
    {
        $this->mobilization = $mobilization->load(['group', 'votingCenter', 'coordinator']);
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function details()
    {
        return $this->mobilization->details()
            ->with('person')
            ->when($this->search, function ($query): void {
                $query->whereHas('person', function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('cedula', 'like', "%{$this->search}%");
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->orderBy('status')
            ->paginate($this->perPage);
    }

    #[Computed]
    public function potentialPersons()
    {
        if (empty($this->searchPerson)) {
            return collect();
        }

        // Obtener los IDs de las personas que ya están en la movilización
        $existingPersonIds = $this->mobilization->details()->pluck('person_id')->toArray();

        return Person::query()
            ->whereNotIn('id', $existingPersonIds)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchPerson}%")
                    ->orWhere('cedula', 'like', "%{$this->searchPerson}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function detailStats(): array
    {
        return [
            'total' => $this->mobilization->details()->count(),
            'pending' => $this->mobilization->details()->where('status', 'pending')->count(),
            'contacted' => $this->mobilization->details()->where('status', 'contacted')->count(),
            'confirmed' => $this->mobilization->details()->where('status', 'confirmed')->count(),
            'mobilized' => $this->mobilization->details()->where('status', 'mobilized')->count(),
            'voted' => $this->mobilization->details()->where('status', 'voted')->count(),
            'cancelled' => $this->mobilization->details()->where('status', 'cancelled')->count(),
        ];
    }

    public function openAddPersonsModal(): void
    {
        $this->showAddPersonsModal = true;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function closeAddPersonsModal(): void
    {
        $this->showAddPersonsModal = false;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function togglePersonSelection($personId): void
    {
        if (in_array($personId, $this->selectedPersons)) {
            $this->selectedPersons = array_diff($this->selectedPersons, [$personId]);
        } else {
            $this->selectedPersons[] = $personId;
        }
    }

    public function addPersons(): void
    {
        if (empty($this->selectedPersons)) {
            \Flux::toast(
                text: 'Debes seleccionar al menos una persona',
                variant: 'error',
                heading: 'Error'
            );

            return;
        }

        foreach ($this->selectedPersons as $personId) {
            $this->mobilization->details()->create([
                'person_id' => $personId,
                'status' => 'pending',
                'registered_by' => Auth::id(),
            ]);
        }

        \Flux::toast(
            text: count($this->selectedPersons).' personas agregadas exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeAddPersonsModal();
        $this->updateMobilizationCounters();
    }

    public function openEditDetailModal($detailId): void
    {
        $this->editingDetail = ElectoralMobilizationDetail::with('person')->find($detailId);

        if ($this->editingDetail) {
            $this->detailStatus = $this->editingDetail->status;
            $this->detailNotes = $this->editingDetail->notes;
            $this->showEditDetailModal = true;
        }
    }

    public function closeEditDetailModal(): void
    {
        $this->showEditDetailModal = false;
        $this->editingDetail = null;
        $this->reset(['detailStatus', 'detailNotes']);
    }

    public function updateDetail(): void
    {
        if (! $this->editingDetail) {
            return;
        }

        $this->validate();

        // Actualizar el estado y las fechas correspondientes
        $updateData = [
            'status' => $this->detailStatus,
            'notes' => $this->detailNotes,
        ];

        switch ($this->detailStatus) {
            case 'contacted':
                $updateData['contact_time'] = now();
                break;
            case 'confirmed':
                $updateData['confirmation_time'] = now();
                break;
            case 'mobilized':
                $updateData['mobilization_time'] = now();
                break;
            case 'voted':
                $updateData['voting_time'] = now();
                break;
        }

        $this->editingDetail->update($updateData);

        \Flux::toast(
            text: 'Detalle actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeEditDetailModal();
        $this->updateMobilizationCounters();
    }

    public function removePerson($detailId): void
    {
        $detail = ElectoralMobilizationDetail::with('person')->find($detailId);

        if (! $detail) {
            return;
        }

        $personName = $detail->person->name;

        $detail->delete();

        \Flux::toast(
            text: "$personName ha sido eliminado de la movilización",
            variant: 'success',
            heading: 'Éxito'
        );

        $this->updateMobilizationCounters();
    }

    public function updateDetailStatus($detailId, $status): void
    {
        $detail = ElectoralMobilizationDetail::find($detailId);

        if (! $detail) {
            return;
        }

        // Actualizar el estado y las fechas correspondientes
        $updateData = ['status' => $status];

        switch ($status) {
            case 'contacted':
                $updateData['contact_time'] = now();
                break;
            case 'confirmed':
                $updateData['confirmation_time'] = now();
                break;
            case 'mobilized':
                $updateData['mobilization_time'] = now();
                break;
            case 'voted':
                $updateData['voting_time'] = now();
                break;
        }

        $detail->update($updateData);

        \Flux::toast(
            text: 'Estado actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->updateMobilizationCounters();
    }

    private function updateMobilizationCounters(): void
    {
        $mobilizedCount = $this->mobilization->details()->whereIn('status', ['mobilized', 'voted'])->count();
        $votedCount = $this->mobilization->details()->where('status', 'voted')->count();

        $this->mobilization->update([
            'mobilized_voters' => $mobilizedCount,
            'confirmed_votes' => $votedCount,
        ]);

        // Recargar la movilización para actualizar los datos
        $this->mobilization = $this->mobilization->fresh();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.manage-electoral-mobilization-details');
    }
}
