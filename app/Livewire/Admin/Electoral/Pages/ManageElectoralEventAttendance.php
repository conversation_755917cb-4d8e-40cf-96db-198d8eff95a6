<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\ElectoralEventAttendance;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class ManageElectoralEventAttendance extends Component
{
    use WithPagination;

    public ElectoralEvent $event;

    // Filtros y búsqueda
    public $search = '';

    public $perPage = 10;

    public $statusFilter = '';

    // Modal para agregar asistentes
    public $showAddAttendeesModal = false;

    public $searchPerson = '';

    public $selectedPersons = [];

    // Modal para editar asistente
    public $showEditAttendeeModal = false;

    public $editingAttendee;

    #[Rule('required|in:registered,confirmed,attended,absent')]
    public $attendeeStatus = 'registered';

    #[Rule('nullable|string')]
    public $attendeeNotes = '';

    public function mount(ElectoralEvent $event): void
    {
        $this->event = $event->load(['estate', 'municipality', 'parish', 'organizer']);
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function attendees()
    {
        return $this->event->attendances()
            ->with('person')
            ->when($this->search, function ($query): void {
                $query->whereHas('person', function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('cedula', 'like', "%{$this->search}%");
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->orderBy('check_in_time', 'desc')
            ->paginate($this->perPage);
    }

    #[Computed]
    public function potentialAttendees()
    {
        if (empty($this->searchPerson)) {
            return collect();
        }

        // Obtener los IDs de las personas que ya son asistentes del evento
        $existingAttendeeIds = $this->event->attendances()->pluck('person_id')->toArray();

        return Person::query()
            ->whereNotIn('id', $existingAttendeeIds)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchPerson}%")
                    ->orWhere('cedula', 'like', "%{$this->searchPerson}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function attendanceStats(): array
    {
        return [
            'total' => $this->event->attendances()->count(),
            'registered' => $this->event->attendances()->where('status', 'registered')->count(),
            'confirmed' => $this->event->attendances()->where('status', 'confirmed')->count(),
            'attended' => $this->event->attendances()->where('status', 'attended')->count(),
            'absent' => $this->event->attendances()->where('status', 'absent')->count(),
        ];
    }

    public function openAddAttendeesModal(): void
    {
        $this->showAddAttendeesModal = true;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function closeAddAttendeesModal(): void
    {
        $this->showAddAttendeesModal = false;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function togglePersonSelection($personId): void
    {
        if (in_array($personId, $this->selectedPersons)) {
            $this->selectedPersons = array_diff($this->selectedPersons, [$personId]);
        } else {
            $this->selectedPersons[] = $personId;
        }
    }

    public function addAttendees(): void
    {
        if (empty($this->selectedPersons)) {
            \Flux::toast(
                text: 'Debes seleccionar al menos una persona',
                variant: 'error',
                heading: 'Error'
            );

            return;
        }

        foreach ($this->selectedPersons as $personId) {
            $this->event->attendances()->create([
                'person_id' => $personId,
                'status' => 'registered',
                'confirmation_code' => Str::random(8),
                'qr_code' => Str::uuid()->toString(),
                'registered_by' => Auth::id(),
            ]);
        }

        \Flux::toast(
            text: count($this->selectedPersons).' asistentes agregados exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeAddAttendeesModal();
    }

    public function openEditAttendeeModal($attendeeId): void
    {
        $this->editingAttendee = ElectoralEventAttendance::with('person')->find($attendeeId);

        if ($this->editingAttendee) {
            $this->attendeeStatus = $this->editingAttendee->status;
            $this->attendeeNotes = $this->editingAttendee->notes;
            $this->showEditAttendeeModal = true;
        }
    }

    public function closeEditAttendeeModal(): void
    {
        $this->showEditAttendeeModal = false;
        $this->editingAttendee = null;
        $this->reset(['attendeeStatus', 'attendeeNotes']);
    }

    public function updateAttendee(): void
    {
        if (! $this->editingAttendee) {
            return;
        }

        $this->validate();

        // Si el estado cambia a 'attended', establecer la hora de check-in
        if ($this->attendeeStatus === 'attended' && $this->editingAttendee->status !== 'attended') {
            $this->editingAttendee->check_in_time = now();
        }

        $this->editingAttendee->update([
            'status' => $this->attendeeStatus,
            'notes' => $this->attendeeNotes,
        ]);

        \Flux::toast(
            text: 'Asistente actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeEditAttendeeModal();
    }

    public function removeAttendee($attendeeId): void
    {
        $attendee = ElectoralEventAttendance::with('person')->find($attendeeId);

        if (! $attendee) {
            return;
        }

        $attendeeName = $attendee->person->name;

        $attendee->delete();

        \Flux::toast(
            text: "$attendeeName ha sido eliminado de la lista de asistentes",
            variant: 'success',
            heading: 'Éxito'
        );
    }

    public function markAsAttended($attendeeId): void
    {
        $attendee = ElectoralEventAttendance::find($attendeeId);

        if (! $attendee) {
            return;
        }

        $attendee->update([
            'status' => 'attended',
            'check_in_time' => now(),
        ]);

        \Flux::toast(
            text: 'Asistente marcado como presente',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    public function markAsAbsent($attendeeId): void
    {
        $attendee = ElectoralEventAttendance::find($attendeeId);

        if (! $attendee) {
            return;
        }

        $attendee->update([
            'status' => 'absent',
        ]);

        \Flux::toast(
            text: 'Asistente marcado como ausente',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.manage-electoral-event-attendance');
    }
}
