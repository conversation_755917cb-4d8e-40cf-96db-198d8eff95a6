<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\Estate;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

class EventsElectoral extends Component
{
    use WithPagination;

    public $search = '';

    public $perPage = 10;

    public $sortField = 'start_date';

    public $sortDirection = 'desc';

    public $typeFilter = '';

    public $statusFilter = '';

    public $estateFilter = '';

    public $dateFilter = '';

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingTypeFilter(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    public function updatingEstateFilter(): void
    {
        $this->resetPage();
    }

    public function updatingDateFilter(): void
    {
        $this->resetPage();
    }

    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function events()
    {
        return ElectoralEvent::query()
            ->with(['estate', 'municipality', 'parish', 'organizer'])
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('description', 'like', "%{$this->search}%")
                        ->orWhere('location', 'like', "%{$this->search}%");
                });
            })
            ->when($this->typeFilter, function ($query): void {
                $query->where('type', $this->typeFilter);
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->when($this->dateFilter, function ($query): void {
                switch ($this->dateFilter) {
                    case 'today':
                        $query->whereDate('start_date', today());
                        break;
                    case 'tomorrow':
                        $query->whereDate('start_date', today()->addDay());
                        break;
                    case 'this_week':
                        $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'next_week':
                        $query->whereBetween('start_date', [now()->addWeek()->startOfWeek(), now()->addWeek()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('start_date', now()->month)
                            ->whereYear('start_date', now()->year);
                        break;
                    case 'past':
                        $query->where('end_date', '<', now());
                        break;
                    case 'upcoming':
                        $query->where('start_date', '>', now());
                        break;
                }
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }

    #[Computed]
    public function eventStats(): array
    {
        return [
            'total' => ElectoralEvent::count(),
            'upcoming' => ElectoralEvent::upcoming()->count(),
            'completed' => ElectoralEvent::where('status', 'completed')->count(),
            'in_progress' => ElectoralEvent::where('status', 'in_progress')->count(),
            'cancelled' => ElectoralEvent::where('status', 'cancelled')->count(),
        ];
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.events-electoral');
    }
}
