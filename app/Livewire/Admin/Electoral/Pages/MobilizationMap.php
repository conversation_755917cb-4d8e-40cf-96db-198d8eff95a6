<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;

class MobilizationMap extends Component
{
    public $estateFilter = '';

    public $municipalityFilter = '';

    public $dateFilter = 'today';

    public $statusFilter = '';

    public $selectedVotingCenter;

    public $selectedMobilization;

    public function mount(): void
    {
        //
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function votingCenters()
    {
        return ElectoralVotingCenter::query()
            ->with(['estate', 'municipality', 'parish'])
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->where('municipality_id', $this->municipalityFilter);
            })
            ->whereNotNull('location_coordinates')
            ->get();
    }

    #[Computed]
    public function mobilizations()
    {
        return ElectoralMobilization::query()
            ->with(['group', 'votingCenter', 'coordinator'])
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->municipalityFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('municipality_id', $this->municipalityFilter);
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->dateFilter, function ($query): void {
                if ($this->dateFilter === 'today') {
                    $query->whereDate('mobilization_date', now()->toDateString());
                } elseif ($this->dateFilter === 'tomorrow') {
                    $query->whereDate('mobilization_date', now()->addDay()->toDateString());
                } elseif ($this->dateFilter === 'week') {
                    $query->whereBetween('mobilization_date', [now()->startOfWeek(), now()->endOfWeek()]);
                } elseif ($this->dateFilter === 'month') {
                    $query->whereBetween('mobilization_date', [now()->startOfMonth(), now()->endOfMonth()]);
                }
            })
            ->get();
    }

    #[Computed]
    public function mobilizationStatistics(): array
    {
        $mobilizations = $this->mobilizations;

        $targetVoters = $mobilizations->sum('target_voters');
        $mobilizedVoters = $mobilizations->sum('mobilized_voters');
        $confirmedVotes = $mobilizations->sum('confirmed_votes');

        $mobilizationProgress = $targetVoters > 0 ? min(100, round(($mobilizedVoters / $targetVoters) * 100)) : 0;
        $votingProgress = $targetVoters > 0 ? min(100, round(($confirmedVotes / $targetVoters) * 100)) : 0;

        return [
            'total_mobilizations' => $mobilizations->count(),
            'target_voters' => $targetVoters,
            'mobilized_voters' => $mobilizedVoters,
            'confirmed_votes' => $confirmedVotes,
            'mobilization_progress' => $mobilizationProgress,
            'voting_progress' => $votingProgress,
        ];
    }

    #[Computed]
    public function mapData(): array
    {
        $votingCenters = $this->votingCenters;
        $mobilizations = $this->mobilizations;

        $markers = [];

        foreach ($votingCenters as $center) {
            if (! $center->latitude) {
                continue;
            }
            if (! $center->longitude) {
                continue;
            }
            // Contar movilizaciones para este centro de votación
            $centerMobilizations = $mobilizations->where('voting_center_id', $center->id);
            $mobilizationCount = $centerMobilizations->count();

            // Calcular progreso de movilización
            $targetVoters = $centerMobilizations->sum('target_voters');
            $mobilizedVoters = $centerMobilizations->sum('mobilized_voters');
            $confirmedVotes = $centerMobilizations->sum('confirmed_votes');

            $mobilizationProgress = $targetVoters > 0 ? min(100, round(($mobilizedVoters / $targetVoters) * 100)) : 0;
            $votingProgress = $targetVoters > 0 ? min(100, round(($confirmedVotes / $targetVoters) * 100)) : 0;

            // Determinar color del marcador basado en el progreso
            $color = 'gray';
            if ($mobilizationCount > 0) {
                if ($votingProgress >= 75) {
                    $color = 'green';
                } elseif ($votingProgress >= 50) {
                    $color = 'blue';
                } elseif ($votingProgress >= 25) {
                    $color = 'yellow';
                } else {
                    $color = 'red';
                }
            }

            $markers[] = [
                'id' => $center->id,
                'lat' => $center->latitude,
                'lng' => $center->longitude,
                'title' => $center->name,
                'info' => [
                    'name' => $center->name,
                    'address' => $center->address,
                    'location' => $center->full_location,
                    'total_voters' => $center->total_voters,
                    'total_tables' => $center->total_tables,
                    'mobilization_count' => $mobilizationCount,
                    'target_voters' => $targetVoters,
                    'mobilized_voters' => $mobilizedVoters,
                    'confirmed_votes' => $confirmedVotes,
                    'mobilization_progress' => $mobilizationProgress,
                    'voting_progress' => $votingProgress,
                ],
                'color' => $color,
            ];
        }

        return [
            'markers' => $markers,
            'center' => $this->calculateMapCenter($markers),
        ];
    }

    private function calculateMapCenter($markers): array
    {
        if (empty($markers)) {
            // Default to Venezuela's approximate center
            return ['lat' => 8.0, 'lng' => -66.0];
        }

        $totalLat = 0;
        $totalLng = 0;
        $count = count($markers);

        foreach ($markers as $marker) {
            $totalLat += $marker['lat'];
            $totalLng += $marker['lng'];
        }

        return [
            'lat' => $totalLat / $count,
            'lng' => $totalLng / $count,
        ];
    }

    public function selectVotingCenter($centerId): void
    {
        $this->selectedVotingCenter = ElectoralVotingCenter::with(['estate', 'municipality', 'parish'])->find($centerId);
        $this->selectedMobilization = null;
    }

    public function selectMobilization($mobilizationId): void
    {
        $this->selectedMobilization = ElectoralMobilization::with(['group', 'votingCenter', 'coordinator'])->find($mobilizationId);
    }

    public function clearSelection(): void
    {
        $this->selectedVotingCenter = null;
        $this->selectedMobilization = null;
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.mobilization-map');
    }
}
