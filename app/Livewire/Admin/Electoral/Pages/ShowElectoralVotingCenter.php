<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;

class ShowElectoralVotingCenter extends Component
{
    public ElectoralVotingCenter $center;

    public function mount(ElectoralVotingCenter $center): void
    {
        $this->center = $center->load(['estate', 'municipality', 'parish']);
    }

    #[Computed]
    public function mobilizationStats(): array
    {
        $mobilizations = ElectoralMobilization::where('voting_center_id', $this->center->id)->get();

        $targetVoters = $mobilizations->sum('target_voters');
        $mobilizedVoters = $mobilizations->sum('mobilized_voters');
        $confirmedVotes = $mobilizations->sum('confirmed_votes');

        $mobilizationProgress = $targetVoters > 0 ? min(100, round(($mobilizedVoters / $targetVoters) * 100)) : 0;
        $votingProgress = $targetVoters > 0 ? min(100, round(($confirmedVotes / $targetVoters) * 100)) : 0;
        $overallProgress = $this->center->total_voters > 0 ? min(100, round(($confirmedVotes / $this->center->total_voters) * 100)) : 0;

        return [
            'total_mobilizations' => $mobilizations->count(),
            'scheduled_mobilizations' => $mobilizations->where('status', 'scheduled')->count(),
            'in_progress_mobilizations' => $mobilizations->where('status', 'in_progress')->count(),
            'completed_mobilizations' => $mobilizations->where('status', 'completed')->count(),
            'target_voters' => $targetVoters,
            'mobilized_voters' => $mobilizedVoters,
            'confirmed_votes' => $confirmedVotes,
            'mobilization_progress' => $mobilizationProgress,
            'voting_progress' => $votingProgress,
            'overall_progress' => $overallProgress,
        ];
    }

    #[Computed]
    public function recentMobilizations()
    {
        return ElectoralMobilization::with(['group', 'coordinator'])
            ->where('voting_center_id', $this->center->id)
            ->orderBy('mobilization_date', 'desc')
            ->limit(5)
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.show-electoral-voting-center');
    }
}
