<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\ElectoralEventAttendance;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

class ShowElectoralEvent extends Component
{
    use WithPagination;

    public ElectoralEvent $event;

    // Búsqueda y filtros para asistentes
    public $search = '';

    public $perPage = 10;

    public $statusFilter = '';

    // Modal para registrar asistentes
    public $showRegisterAttendeeModal = false;

    public $searchPerson = '';

    public $selectedPersons = [];

    // Modal para escanear QR
    public $showScanQrModal = false;

    public $qrCode = '';

    public function mount(ElectoralEvent $event): void
    {
        $this->event = $event->load(['estate', 'municipality', 'parish', 'organizer']);
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function attendees()
    {
        return $this->event->attendances()
            ->with('person')
            ->when($this->search, function ($query): void {
                $query->whereHas('person', function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('cedula', 'like', "%{$this->search}%");
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($this->perPage);
    }

    #[Computed]
    public function attendanceStats(): array
    {
        return [
            'total' => $this->event->attendances()->count(),
            'registered' => $this->event->attendances()->where('status', 'registered')->count(),
            'confirmed' => $this->event->attendances()->where('status', 'confirmed')->count(),
            'attended' => $this->event->attendances()->where('status', 'attended')->count(),
            'absent' => $this->event->attendances()->where('status', 'absent')->count(),
        ];
    }

    #[Computed]
    public function potentialAttendees()
    {
        if (empty($this->searchPerson)) {
            return collect();
        }

        // Obtener los IDs de las personas que ya están registradas para el evento
        $existingAttendeeIds = $this->event->attendances()->pluck('person_id')->toArray();

        return Person::query()
            ->whereNotIn('id', $existingAttendeeIds)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchPerson}%")
                    ->orWhere('cedula', 'like', "%{$this->searchPerson}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    public function openRegisterAttendeeModal(): void
    {
        $this->showRegisterAttendeeModal = true;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function closeRegisterAttendeeModal(): void
    {
        $this->showRegisterAttendeeModal = false;
        $this->selectedPersons = [];
        $this->searchPerson = '';
    }

    public function togglePersonSelection($personId): void
    {
        if (in_array($personId, $this->selectedPersons)) {
            $this->selectedPersons = array_diff($this->selectedPersons, [$personId]);
        } else {
            $this->selectedPersons[] = $personId;
        }
    }

    public function registerAttendees(): void
    {
        if (empty($this->selectedPersons)) {
            \Flux::toast(
                text: 'Debes seleccionar al menos una persona',
                variant: 'error',
                heading: 'Error'
            );

            return;
        }

        foreach ($this->selectedPersons as $personId) {
            $attendance = $this->event->attendances()->create([
                'person_id' => $personId,
                'status' => 'registered',
                'registered_by' => Auth::id(),
            ]);

            // Generar código de confirmación y QR
            $attendance->generateConfirmationCode();
            $attendance->generateQrCode();
        }

        \Flux::toast(
            text: count($this->selectedPersons).' asistentes registrados exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeRegisterAttendeeModal();
    }

    public function openScanQrModal(): void
    {
        $this->showScanQrModal = true;
        $this->qrCode = '';
    }

    public function closeScanQrModal(): void
    {
        $this->showScanQrModal = false;
        $this->qrCode = '';
    }

    public function processQrCode(): void
    {
        if (empty($this->qrCode)) {
            \Flux::toast(
                text: 'Código QR inválido',
                variant: 'error',
                heading: 'Error'
            );

            return;
        }

        $attendance = ElectoralEventAttendance::where('qr_code', $this->qrCode)
            ->where('event_id', $this->event->id)
            ->first();

        if (! $attendance) {
            \Flux::toast(
                text: 'Código QR no encontrado o no válido para este evento',
                variant: 'error',
                heading: 'Error'
            );

            return;
        }

        $attendance->checkIn();

        \Flux::toast(
            text: 'Asistente registrado exitosamente: '.$attendance->person->name,
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeScanQrModal();
    }

    public function markAttendance($attendanceId, $status): void
    {
        $attendance = ElectoralEventAttendance::find($attendanceId);

        if (! $attendance) {
            return;
        }

        $attendance->status = $status;

        if ($status === 'attended') {
            $attendance->check_in_time = now();
        }

        $attendance->save();

        \Flux::toast(
            text: 'Estado de asistencia actualizado',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    public function removeAttendee($attendanceId): void
    {
        $attendance = ElectoralEventAttendance::find($attendanceId);

        if (! $attendance) {
            return;
        }

        $personName = $attendance->person->name;

        $attendance->delete();

        \Flux::toast(
            text: "$personName ha sido eliminado de la lista de asistentes",
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.show-electoral-event');
    }
}
