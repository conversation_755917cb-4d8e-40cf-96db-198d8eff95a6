<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class VotingTracker extends Component
{
    use WithPagination;

    // Filtros y búsqueda
    public $search = '';

    public $perPage = 10;

    public $statusFilter = '';

    public $estateFilter = '';

    public $dateFilter = '';

    // Modal para crear movilización
    public $showCreateMobilizationModal = false;

    #[Rule('required|exists:people,id')]
    public $group_id; // This is now a leader_id (person who is a 1x10 leader)

    #[Rule('required|exists:electoral_voting_centers,id')]
    public $voting_center_id;

    #[Rule('required|date')]
    public $mobilization_date = '';

    #[Rule('required|in:scheduled,in_progress,completed,cancelled')]
    public $status = 'scheduled';

    #[Rule('nullable|integer|min:1')]
    public $target_voters;

    #[Rule('nullable|string')]
    public $notes = '';

    #[Rule('required|exists:people,id')]
    public $coordinator_id;

    // Búsqueda de grupos y centros de votación
    public $searchGroup = '';

    public $searchVotingCenter = '';

    public $searchCoordinator = '';

    public function mount(): void
    {
        $this->mobilization_date = now()->format('Y-m-d\TH:i');
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    public function updatingEstateFilter(): void
    {
        $this->resetPage();
    }

    public function updatingDateFilter(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function mobilizations()
    {
        return ElectoralMobilization::query()
            ->with(['group', 'votingCenter', 'coordinator'])
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->whereHas('group', function ($q): void {
                        $q->where('name', 'like', "%{$this->search}%");
                    })
                        ->orWhereHas('votingCenter', function ($q): void {
                            $q->where('name', 'like', "%{$this->search}%");
                        })
                        ->orWhereHas('coordinator', function ($q): void {
                            $q->where('name', 'like', "%{$this->search}%");
                        });
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->dateFilter, function ($query): void {
                if ($this->dateFilter === 'upcoming') {
                    $query->upcoming();
                } elseif ($this->dateFilter === 'past') {
                    $query->past();
                } elseif ($this->dateFilter === 'today') {
                    $query->whereDate('mobilization_date', now()->toDateString());
                } elseif ($this->dateFilter === 'week') {
                    $query->whereBetween('mobilization_date', [now()->startOfWeek(), now()->endOfWeek()]);
                } elseif ($this->dateFilter === 'month') {
                    $query->whereBetween('mobilization_date', [now()->startOfMonth(), now()->endOfMonth()]);
                }
            })
            ->orderBy('mobilization_date', 'desc')
            ->paginate($this->perPage);
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function groups()
    {
        return Person::query()
            ->where('is_1x10', true)
            ->when($this->searchGroup, function ($query): void {
                $query->where('name', 'like', "%{$this->searchGroup}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function votingCenters()
    {
        return ElectoralVotingCenter::query()
            ->when($this->searchVotingCenter, function ($query): void {
                $query->where('name', 'like', "%{$this->searchVotingCenter}%")
                    ->orWhere('code', 'like', "%{$this->searchVotingCenter}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function potentialCoordinators()
    {
        if (! $this->group_id || empty($this->searchCoordinator)) {
            return collect();
        }

        // Find people who are assigned to this 1x10 leader
        return Person::query()
            ->where('responsible_id', $this->group_id)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchCoordinator}%")
                    ->orWhere('identification', 'like', "%{$this->searchCoordinator}%");
            })
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function mobilizationStats(): array
    {
        return [
            'total' => ElectoralMobilization::count(),
            'scheduled' => ElectoralMobilization::where('status', 'scheduled')->count(),
            'in_progress' => ElectoralMobilization::where('status', 'in_progress')->count(),
            'completed' => ElectoralMobilization::where('status', 'completed')->count(),
            'cancelled' => ElectoralMobilization::where('status', 'cancelled')->count(),
            'total_target' => ElectoralMobilization::sum('target_voters'),
            'total_mobilized' => ElectoralMobilization::sum('mobilized_voters'),
            'total_confirmed' => ElectoralMobilization::sum('confirmed_votes'),
        ];
    }

    public function openCreateMobilizationModal(): void
    {
        $this->reset([
            'group_id', 'voting_center_id', 'coordinator_id', 'target_voters', 'notes',
            'searchGroup', 'searchVotingCenter', 'searchCoordinator',
        ]);
        $this->mobilization_date = now()->format('Y-m-d\TH:i');
        $this->status = 'scheduled';
        $this->showCreateMobilizationModal = true;
    }

    public function closeCreateMobilizationModal(): void
    {
        $this->showCreateMobilizationModal = false;
    }

    public function createMobilization()
    {
        $validated = $this->validate();

        // Agregar el usuario que registra
        $validated['registered_by'] = Auth::id();

        // Crear la movilización
        $mobilization = ElectoralMobilization::create($validated);

        // Si hay un líder 1x10 seleccionado, agregar a todas las personas asignadas a él
        if ($this->group_id) {
            $assignedPersons = Person::where('responsible_id', $this->group_id)->get();

            foreach ($assignedPersons as $person) {
                $mobilization->details()->create([
                    'person_id' => $person->id,
                    'status' => 'pending',
                    'registered_by' => Auth::id(),
                ]);
            }
        }

        \Flux::toast(
            text: 'Movilización electoral creada exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeCreateMobilizationModal();

        return redirect()->route('admin.electoral.mobilization.show', $mobilization);
    }

    public function updateMobilizationStatus($mobilizationId, $status): void
    {
        $mobilization = ElectoralMobilization::find($mobilizationId);

        if (! $mobilization) {
            return;
        }

        $mobilization->status = $status;
        $mobilization->save();

        \Flux::toast(
            text: 'Estado de movilización actualizado',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.voting-tracker');
    }
}
