<?php

namespace App\Livewire\Admin\Electoral\Pages;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Cache;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class ElectoralSettings extends Component
{
    #[Rule('required|date')]
    public $election_date = '';

    #[Rule('required|string|max:255')]
    public $election_name = '';

    #[Rule('required|boolean')]
    public $enable_mobilization = true;

    #[Rule('required|boolean')]
    public $enable_voting_tracker = true;

    #[Rule('required|boolean')]
    public $enable_qr_attendance = true;

    #[Rule('required|boolean')]
    public $enable_sms_notifications = false;

    #[Rule('required|boolean')]
    public $enable_email_notifications = false;

    #[Rule('nullable|string')]
    public $sms_api_key = '';

    #[Rule('nullable|string')]
    public $sms_sender_id = '';

    #[Rule('nullable|string')]
    public $email_from_address = '';

    #[Rule('nullable|string')]
    public $email_from_name = '';

    public function mount(): void
    {
        // Cargar configuraciones desde la caché o valores predeterminados
        $this->election_date = Cache::get('electoral.election_date', now()->addMonth()->format('Y-m-d'));
        $this->election_name = Cache::get('electoral.election_name', 'Elecciones Generales');
        $this->enable_mobilization = Cache::get('electoral.enable_mobilization', true);
        $this->enable_voting_tracker = Cache::get('electoral.enable_voting_tracker', true);
        $this->enable_qr_attendance = Cache::get('electoral.enable_qr_attendance', true);
        $this->enable_sms_notifications = Cache::get('electoral.enable_sms_notifications', false);
        $this->enable_email_notifications = Cache::get('electoral.enable_email_notifications', false);
        $this->sms_api_key = Cache::get('electoral.sms_api_key', '');
        $this->sms_sender_id = Cache::get('electoral.sms_sender_id', '');
        $this->email_from_address = Cache::get('electoral.email_from_address', '');
        $this->email_from_name = Cache::get('electoral.email_from_name', '');
    }

    public function save(): void
    {
        $validated = $this->validate();

        // Guardar configuraciones en la caché
        foreach ($validated as $key => $value) {
            Cache::put('electoral.'.$key, $value, now()->addYear());
        }

        \Flux::toast(
            text: 'Configuraciones guardadas exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    public function resetSettings(): void
    {
        // Eliminar todas las configuraciones de la caché
        Cache::forget('electoral.election_date');
        Cache::forget('electoral.election_name');
        Cache::forget('electoral.enable_mobilization');
        Cache::forget('electoral.enable_voting_tracker');
        Cache::forget('electoral.enable_qr_attendance');
        Cache::forget('electoral.enable_sms_notifications');
        Cache::forget('electoral.enable_email_notifications');
        Cache::forget('electoral.sms_api_key');
        Cache::forget('electoral.sms_sender_id');
        Cache::forget('electoral.email_from_address');
        Cache::forget('electoral.email_from_name');

        // Recargar valores predeterminados
        $this->mount();

        \Flux::toast(
            text: 'Configuraciones restablecidas a valores predeterminados',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.electoral-settings');
    }
}
