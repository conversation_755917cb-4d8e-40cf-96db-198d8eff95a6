<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEventAttendance;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralMobilizationDetail;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Response;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;

class ExportElectoralReports extends Component
{
    public $reportType = 'attendance';

    public $dateRange = [
        'start' => '',
        'end' => '',
    ];

    public $estateFilter;

    public $municipalityFilter;

    public $fileFormat = 'csv';

    public function mount(): void
    {
        // Establecer fechas predeterminadas (último mes)
        $this->dateRange['end'] = now()->format('Y-m-d');
        $this->dateRange['start'] = now()->subMonth()->format('Y-m-d');
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function municipalities()
    {
        if (! $this->estateFilter) {
            return collect();
        }

        return Estate::find($this->estateFilter)->municipalities()->orderBy('name')->get();
    }

    public function exportAttendanceReport()
    {
        $filename = 'reporte_asistencia_electoral_'.date('Y-m-d').'.'.$this->fileFormat;

        if ($this->fileFormat === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            ];

            $callback = function (): void {
                $file = fopen('php://output', 'w');

                // Encabezados del CSV
                fputcsv($file, ['ID', 'Evento', 'Fecha del Evento', 'Persona', 'Identificación', 'Estado', 'Fecha de Registro', 'Fecha de Check-in', 'Notas']);

                // Consulta para obtener los datos
                $query = ElectoralEventAttendance::query()
                    ->with(['event', 'person'])
                    ->when($this->dateRange['start'], function ($query): void {
                        $query->whereHas('event', function ($q): void {
                            $q->where('start_date', '>=', $this->dateRange['start']);
                        });
                    })
                    ->when($this->dateRange['end'], function ($query): void {
                        $query->whereHas('event', function ($q): void {
                            $q->where('start_date', '<=', $this->dateRange['end'].' 23:59:59');
                        });
                    })
                    ->when($this->estateFilter, function ($query): void {
                        $query->whereHas('event', function ($q): void {
                            $q->where('estate_id', $this->estateFilter);
                        });
                    })
                    ->when($this->municipalityFilter, function ($query): void {
                        $query->whereHas('event', function ($q): void {
                            $q->where('municipality_id', $this->municipalityFilter);
                        });
                    })
                    ->orderBy('id');

                // Procesar los resultados en lotes para evitar problemas de memoria
                $query->chunk(1000, function ($attendances) use ($file): void {
                    foreach ($attendances as $attendance) {
                        fputcsv($file, [
                            $attendance->id,
                            $attendance->event->name ?? 'N/A',
                            $attendance->event->start_date ?? 'N/A',
                            $attendance->person->name ?? 'N/A',
                            $attendance->person->cedula ?? 'N/A',
                            $attendance->status,
                            $attendance->created_at,
                            $attendance->check_in_time,
                            $attendance->notes,
                        ]);
                    }
                });

                fclose($file);
            };

            return Response::stream($callback, 200, $headers);
        }

        // Implementar exportación a Excel si es necesario

        \Flux::toast(
            text: 'Formato de archivo no soportado',
            variant: 'error',
            heading: 'Error'
        );
        return null;
    }

    public function exportMobilizationReport()
    {
        $filename = 'reporte_movilizacion_electoral_'.date('Y-m-d').'.'.$this->fileFormat;

        if ($this->fileFormat === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            ];

            $callback = function (): void {
                $file = fopen('php://output', 'w');

                // Encabezados del CSV
                fputcsv($file, ['ID', 'Movilización', 'Fecha', 'Centro de Votación', 'Grupo', 'Coordinador', 'Persona', 'Identificación', 'Estado', 'Fecha de Contacto', 'Fecha de Confirmación', 'Fecha de Movilización', 'Fecha de Votación', 'Notas']);

                // Consulta para obtener los datos
                $query = ElectoralMobilizationDetail::query()
                    ->with(['mobilization.votingCenter', 'mobilization.group', 'mobilization.coordinator', 'person'])
                    ->when($this->dateRange['start'], function ($query): void {
                        $query->whereHas('mobilization', function ($q): void {
                            $q->where('mobilization_date', '>=', $this->dateRange['start']);
                        });
                    })
                    ->when($this->dateRange['end'], function ($query): void {
                        $query->whereHas('mobilization', function ($q): void {
                            $q->where('mobilization_date', '<=', $this->dateRange['end']);
                        });
                    })
                    ->when($this->estateFilter, function ($query): void {
                        $query->whereHas('mobilization.votingCenter', function ($q): void {
                            $q->where('estate_id', $this->estateFilter);
                        });
                    })
                    ->when($this->municipalityFilter, function ($query): void {
                        $query->whereHas('mobilization.votingCenter', function ($q): void {
                            $q->where('municipality_id', $this->municipalityFilter);
                        });
                    })
                    ->orderBy('id');

                // Procesar los resultados en lotes para evitar problemas de memoria
                $query->chunk(1000, function ($details) use ($file): void {
                    foreach ($details as $detail) {
                        fputcsv($file, [
                            $detail->id,
                            $detail->mobilization->name ?? 'N/A',
                            $detail->mobilization->mobilization_date ?? 'N/A',
                            $detail->mobilization->votingCenter->name ?? 'N/A',
                            $detail->mobilization->group->name ?? 'N/A',
                            $detail->mobilization->coordinator->name ?? 'N/A',
                            $detail->person->name ?? 'N/A',
                            $detail->person->cedula ?? 'N/A',
                            $detail->status,
                            $detail->contact_time,
                            $detail->confirmation_time,
                            $detail->mobilization_time,
                            $detail->voting_time,
                            $detail->notes,
                        ]);
                    }
                });

                fclose($file);
            };

            return Response::stream($callback, 200, $headers);
        }

        // Implementar exportación a Excel si es necesario

        \Flux::toast(
            text: 'Formato de archivo no soportado',
            variant: 'error',
            heading: 'Error'
        );
        return null;
    }

    public function exportVotingCentersReport()
    {
        $filename = 'reporte_centros_votacion_'.date('Y-m-d').'.'.$this->fileFormat;

        if ($this->fileFormat === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            ];

            $callback = function (): void {
                $file = fopen('php://output', 'w');

                // Encabezados del CSV
                fputcsv($file, ['ID', 'Nombre', 'Código', 'Dirección', 'Estado', 'Municipio', 'Parroquia', 'Coordenadas', 'Total de Votantes', 'Estado', 'Movilizaciones', 'Meta de Votantes', 'Votantes Movilizados', 'Votos Confirmados']);

                // Consulta para obtener los datos
                $query = ElectoralVotingCenter::query()
                    ->with(['estate', 'municipality', 'parish'])
                    ->when($this->estateFilter, function ($query): void {
                        $query->where('estate_id', $this->estateFilter);
                    })
                    ->when($this->municipalityFilter, function ($query): void {
                        $query->where('municipality_id', $this->municipalityFilter);
                    })
                    ->orderBy('name');

                // Procesar los resultados en lotes para evitar problemas de memoria
                $query->chunk(1000, function ($centers) use ($file): void {
                    foreach ($centers as $center) {
                        // Obtener estadísticas de movilización para este centro
                        $mobilizations = ElectoralMobilization::where('voting_center_id', $center->id)
                            ->when($this->dateRange['start'], fn ($q) => $q->where('mobilization_date', '>=', $this->dateRange['start']))
                            ->when($this->dateRange['end'], fn ($q) => $q->where('mobilization_date', '<=', $this->dateRange['end']))
                            ->get();

                        $mobilizationsCount = $mobilizations->count();
                        $targetVoters = $mobilizations->sum('target_voters');
                        $mobilizedVoters = $mobilizations->sum('mobilized_voters');
                        $confirmedVotes = $mobilizations->sum('confirmed_votes');

                        fputcsv($file, [
                            $center->id,
                            $center->name,
                            $center->code,
                            $center->address,
                            $center->estate->name ?? 'N/A',
                            $center->municipality->name ?? 'N/A',
                            $center->parish->name ?? 'N/A',
                            $center->location_coordinates,
                            $center->total_voters,
                            $center->status,
                            $mobilizationsCount,
                            $targetVoters,
                            $mobilizedVoters,
                            $confirmedVotes,
                        ]);
                    }
                });

                fclose($file);
            };

            return Response::stream($callback, 200, $headers);
        }

        // Implementar exportación a Excel si es necesario

        \Flux::toast(
            text: 'Formato de archivo no soportado',
            variant: 'error',
            heading: 'Error'
        );
        return null;
    }

    public function export()
    {
        switch ($this->reportType) {
            case 'attendance':
                return $this->exportAttendanceReport();
            case 'mobilization':
                return $this->exportMobilizationReport();
            case 'voting_centers':
                return $this->exportVotingCentersReport();
            default:
                \Flux::toast(
                    text: 'Tipo de reporte no válido',
                    variant: 'error',
                    heading: 'Error'
                );
                break;
        }
        return null;
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.export-electoral-reports');
    }
}
