<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class EditElectoralEvent extends Component
{
    public ElectoralEvent $event;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string')]
    public $description = '';

    #[Rule('required|string|in:training,mobilization,voting,meeting,other')]
    public $type = 'meeting';

    #[Rule('required|date_format:Y-m-d H:i')]
    public $start_date = '';

    #[Rule('required|date_format:Y-m-d H:i|after:start_date')]
    public $end_date = '';

    #[Rule('nullable|string|max:255')]
    public $location = '';

    #[Rule('nullable|exists:estates,id')]
    public $estate_id;

    #[Rule('nullable|exists:municipalities,id')]
    public $municipality_id;

    #[Rule('nullable|exists:parishes,id')]
    public $parish_id;

    #[Rule('nullable|exists:people,id')]
    public $organizer_id;

    #[Rule('nullable|integer|min:1')]
    public $capacity;

    #[Rule('required|in:scheduled,in_progress,completed,cancelled')]
    public $status = 'scheduled';

    #[Rule('nullable|string')]
    public $notes = '';

    // Búsqueda de organizador
    public $searchOrganizer = '';

    // Listeners para los eventos del selector de ubicación
    protected function getListeners()
    {
        return [
            'estate-changed' => 'updateEstate',
            'municipality-changed' => 'updateMunicipality',
            'parish-changed' => 'updateParish',
        ];
    }

    public function updateEstate($value): void
    {
        $this->estate_id = $value;
    }

    public function updateMunicipality($value): void
    {
        $this->municipality_id = $value;
    }

    public function updateParish($value): void
    {
        $this->parish_id = $value;
    }

    public function mount(ElectoralEvent $event): void
    {
        $this->event = $event->load(['estate', 'municipality', 'parish', 'organizer']);

        $this->name = $event->name;
        $this->description = $event->description;
        $this->type = $event->type;
        $this->start_date = $event->start_date->format('Y-m-d H:i');
        $this->end_date = $event->end_date->format('Y-m-d H:i');
        $this->location = $event->location;
        $this->estate_id = $event->estate_id;
        $this->municipality_id = $event->municipality_id;
        $this->parish_id = $event->parish_id;
        $this->organizer_id = $event->organizer_id;
        $this->capacity = $event->capacity;
        $this->status = $event->status;
        $this->notes = $event->notes;
    }

    #[Computed]
    public function potentialOrganizers()
    {
        if (empty($this->searchOrganizer)) {
            return collect();
        }

        return Person::query()
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchOrganizer}%")
                    ->orWhere('cedula', 'like', "%{$this->searchOrganizer}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    public function save()
    {
        $validated = $this->validate();

        $this->event->update($validated);

        \Flux::toast(
            text: 'Evento electoral actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        return redirect()->route('admin.electoral.events.show', $this->event);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.edit-electoral-event');
    }
}
