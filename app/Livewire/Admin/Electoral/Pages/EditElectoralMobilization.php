<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class EditElectoralMobilization extends Component
{
    public ElectoralMobilization $mobilization;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string')]
    public $description = '';

    #[Rule('required|date')]
    public $mobilization_date = '';

    #[Rule('required|exists:electoral_voting_centers,id')]
    public $voting_center_id;

    #[Rule('nullable|exists:people,id')]
    public $group_id; // This is now a leader_id (person who is a 1x10 leader)

    #[Rule('nullable|exists:people,id')]
    public $coordinator_id;

    #[Rule('required|integer|min:1')]
    public $target_voters = 10;

    #[Rule('required|in:scheduled,in_progress,completed,cancelled')]
    public $status = 'scheduled';

    #[Rule('nullable|string')]
    public $notes = '';

    // Filtros
    public $estateFilter;

    // Búsqueda de coordinador
    public $searchCoordinator = '';

    // Búsqueda de grupo
    public $searchGroup = '';

    public function mount(ElectoralMobilization $mobilization): void
    {
        $this->mobilization = $mobilization->load(['group', 'votingCenter', 'coordinator']);

        $this->name = $mobilization->name;
        $this->description = $mobilization->description;
        $this->mobilization_date = $mobilization->mobilization_date->format('Y-m-d');
        $this->voting_center_id = $mobilization->voting_center_id;
        $this->group_id = $mobilization->group_id;
        $this->coordinator_id = $mobilization->coordinator_id;
        $this->target_voters = $mobilization->target_voters;
        $this->status = $mobilization->status;
        $this->notes = $mobilization->notes;

        if ($mobilization->votingCenter && $mobilization->votingCenter->estate_id) {
            $this->estateFilter = $mobilization->votingCenter->estate_id;
        }
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function votingCenters()
    {
        return ElectoralVotingCenter::query()
            ->with(['estate', 'municipality'])
            ->where('status', 'active')
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->orderBy('name')
            ->get();
    }

    #[Computed]
    public function potentialCoordinators()
    {
        if (empty($this->searchCoordinator) && ! $this->coordinator_id) {
            return collect();
        }

        $query = Person::query();

        if ($this->coordinator_id) {
            $query->where('id', $this->coordinator_id)
                ->orWhere(function ($q): void {
                    $q->where('name', 'like', "%{$this->searchCoordinator}%")
                        ->orWhere('identification', 'like', "%{$this->searchCoordinator}%");
                });
        } else {
            $query->where(function ($q): void {
                $q->where('name', 'like', "%{$this->searchCoordinator}%")
                    ->orWhere('identification', 'like', "%{$this->searchCoordinator}%");
            });
        }

        return $query->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function potentialGroups()
    {
        if (empty($this->searchGroup) && ! $this->group_id) {
            return collect();
        }

        $query = Person::query()
            ->where('is_1x10', true);

        if ($this->group_id) {
            $query->where('id', $this->group_id)
                ->orWhere(function ($q): void {
                    $q->where('name', 'like', "%{$this->searchGroup}%")
                        ->orWhere('identification', 'like', "%{$this->searchGroup}%");
                });
        } else {
            $query->where(function ($q): void {
                $q->where('name', 'like', "%{$this->searchGroup}%")
                    ->orWhere('identification', 'like', "%{$this->searchGroup}%");
            });
        }

        return $query->orderBy('name')
            ->limit(10)
            ->get();
    }

    public function save()
    {
        $validated = $this->validate();

        $this->mobilization->update($validated);

        \Flux::toast(
            text: 'Movilización actualizada exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        return redirect()->route('admin.electoral.mobilization.show', $this->mobilization);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.edit-electoral-mobilization');
    }
}
