<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralVotingCenter;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class EditElectoralVotingCenter extends Component
{
    public ElectoralVotingCenter $center;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string|max:255')]
    public $code = '';

    #[Rule('nullable|string')]
    public $address = '';

    #[Rule('nullable|exists:estates,id')]
    public $estate_id;

    #[Rule('nullable|exists:municipalities,id')]
    public $municipality_id;

    #[Rule('nullable|exists:parishes,id')]
    public $parish_id;

    #[Rule('nullable|string')]
    public $location_coordinates = '';

    #[Rule('nullable|integer|min:1')]
    public $total_voters;

    #[Rule('required|in:active,inactive')]
    public $status = 'active';

    // Listeners para los eventos del selector de ubicación
    protected function getListeners()
    {
        return [
            'estate-changed' => 'updateEstate',
            'municipality-changed' => 'updateMunicipality',
            'parish-changed' => 'updateParish',
        ];
    }

    public function updateEstate($value): void
    {
        $this->estate_id = $value;
    }

    public function updateMunicipality($value): void
    {
        $this->municipality_id = $value;
    }

    public function updateParish($value): void
    {
        $this->parish_id = $value;
    }

    public function mount(ElectoralVotingCenter $center): void
    {
        $this->center = $center->load(['estate', 'municipality', 'parish']);

        $this->name = $center->name;
        $this->code = $center->code;
        $this->address = $center->address;
        $this->estate_id = $center->estate_id;
        $this->municipality_id = $center->municipality_id;
        $this->parish_id = $center->parish_id;
        $this->location_coordinates = $center->location_coordinates;
        $this->total_voters = $center->total_voters;
        $this->status = $center->status;
    }

    public function save()
    {
        $validated = $this->validate();

        $this->center->update($validated);

        \Flux::toast(
            text: 'Centro de votación actualizado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        return redirect()->route('admin.electoral.voting-centers.show', $this->center);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.edit-electoral-voting-center');
    }
}
