<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class CreateElectoralMobilization extends Component
{
    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string')]
    public $description = '';

    #[Rule('required|date')]
    public $mobilization_date = '';

    #[Rule('required|exists:electoral_voting_centers,id')]
    public $voting_center_id;

    #[Rule('nullable|exists:people,id')]
    public $group_id; // This is now a leader_id (person who is a 1x10 leader)

    #[Rule('nullable|exists:people,id')]
    public $coordinator_id;

    #[Rule('required|integer|min:1')]
    public $target_voters = 10;

    #[Rule('required|in:scheduled,in_progress,completed,cancelled')]
    public $status = 'scheduled';

    #[Rule('nullable|string')]
    public $notes = '';

    // Filtros
    public $estateFilter;

    // Búsqueda de coordinador
    public $searchCoordinator = '';

    // Búsqueda de grupo
    public $searchGroup = '';

    public function mount(): void
    {
        $this->mobilization_date = now()->format('Y-m-d');
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function votingCenters()
    {
        return ElectoralVotingCenter::query()
            ->with(['estate', 'municipality'])
            ->where('status', 'active')
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->orderBy('name')
            ->get();
    }

    #[Computed]
    public function potentialCoordinators()
    {
        if (empty($this->searchCoordinator)) {
            return collect();
        }

        return Person::query()
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchCoordinator}%")
                    ->orWhere('cedula', 'like', "%{$this->searchCoordinator}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function potentialGroups()
    {
        if (empty($this->searchGroup)) {
            return collect();
        }

        return Person::query()
            ->where('is_1x10', true)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchGroup}%")
                    ->orWhere('identification', 'like', "%{$this->searchGroup}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    public function save()
    {
        $validated = $this->validate();

        $validated['created_by'] = Auth::id();
        $validated['mobilized_voters'] = 0;
        $validated['confirmed_votes'] = 0;

        $mobilization = ElectoralMobilization::create($validated);

        // Si hay un líder 1x10 seleccionado, agregar a todas las personas asignadas a él
        if ($this->group_id) {
            $assignedPersons = Person::where('responsible_id', $this->group_id)->get();

            foreach ($assignedPersons as $person) {
                $mobilization->details()->create([
                    'person_id' => $person->id,
                    'status' => 'pending',
                    'registered_by' => Auth::id(),
                ]);
            }
        }

        \Flux::toast(
            text: 'Movilización creada exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        return redirect()->route('admin.electoral.mobilization.show', $mobilization);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.create-electoral-mobilization');
    }
}
