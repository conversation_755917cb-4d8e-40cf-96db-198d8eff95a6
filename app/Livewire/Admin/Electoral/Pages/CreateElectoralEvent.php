<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class CreateElectoralEvent extends Component
{
    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string')]
    public $description = '';

    #[Rule('required|date')]
    public $start_date = '';

    #[Rule('nullable|date|after_or_equal:start_date')]
    public $end_date = '';

    #[Rule('nullable|string|max:255')]
    public $location = '';

    #[Rule('nullable|string')]
    public $address = '';

    #[Rule('nullable|exists:estates,id')]
    public $estate_id;

    #[Rule('nullable|exists:municipalities,id')]
    public $municipality_id;

    #[Rule('nullable|exists:parishes,id')]
    public $parish_id;

    #[Rule('required|in:training,mobilization,voting,meeting,other')]
    public $type = 'meeting';

    #[Rule('required|in:scheduled,in_progress,completed,cancelled')]
    public $status = 'scheduled';

    #[Rule('nullable|integer|min:1')]
    public $capacity;

    // Listeners para los eventos del selector de ubicación
    protected function getListeners()
    {
        return [
            'estate-changed' => 'updateEstate',
            'municipality-changed' => 'updateMunicipality',
            'parish-changed' => 'updateParish',
        ];
    }

    public function updateEstate($value): void
    {
        $this->estate_id = $value;
    }

    public function updateMunicipality($value): void
    {
        $this->municipality_id = $value;
    }

    public function updateParish($value): void
    {
        $this->parish_id = $value;
    }

    public function mount(): void
    {
        $this->start_date = now()->format('Y-m-d\TH:i');
        $this->end_date = now()->addHours(2)->format('Y-m-d\TH:i');
    }

    public function save()
    {
        $validated = $this->validate();

        // Agregar el organizador (usuario actual)
        $validated['organizer_id'] = Auth::id();

        $event = ElectoralEvent::create($validated);

        // Generar código QR para el evento
        $event->generateQrCode();

        \Flux::toast(
            text: 'Evento electoral creado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        return redirect()->route('admin.electoral.events.show', $event);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.create-electoral-event');
    }
}
