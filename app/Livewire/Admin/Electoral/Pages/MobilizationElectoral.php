<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Estate;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class MobilizationElectoral extends Component
{
    use WithPagination;

    public $search = '';

    public $perPage = 10;

    public $sortField = 'mobilization_date';

    public $sortDirection = 'desc';

    public $statusFilter = '';

    public $estateFilter = '';

    public $dateFilter = '';

    // Modal para crear movilización
    public $showCreateMobilizationModal = false;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string')]
    public $description = '';

    #[Rule('required|date')]
    public $mobilization_date = '';

    #[Rule('required|exists:electoral_voting_centers,id')]
    public $voting_center_id;

    #[Rule('nullable|exists:people,id')]
    public $group_id; // This is now a leader_id (person who is a 1x10 leader)

    #[Rule('nullable|exists:people,id')]
    public $coordinator_id;

    #[Rule('required|integer|min:1')]
    public $target_voters = 10;

    #[Rule('required|in:scheduled,in_progress,completed,cancelled')]
    public $status = 'scheduled';

    #[Rule('nullable|string')]
    public $notes = '';

    // Búsqueda de coordinador
    public $searchCoordinator = '';

    // Búsqueda de grupo
    public $searchGroup = '';

    public function mount(): void
    {
        $this->mobilization_date = now()->format('Y-m-d');
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    public function updatingEstateFilter(): void
    {
        $this->resetPage();
    }

    public function updatingDateFilter(): void
    {
        $this->resetPage();
    }

    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function votingCenters()
    {
        return ElectoralVotingCenter::query()
            ->with(['estate', 'municipality'])
            ->where('status', 'active')
            ->when($this->estateFilter, function ($query): void {
                $query->where('estate_id', $this->estateFilter);
            })
            ->orderBy('name')
            ->get();
    }

    #[Computed]
    public function potentialCoordinators()
    {
        if (empty($this->searchCoordinator)) {
            return collect();
        }

        return Person::query()
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchCoordinator}%")
                    ->orWhere('cedula', 'like', "%{$this->searchCoordinator}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function potentialGroups()
    {
        if (empty($this->searchGroup)) {
            return collect();
        }

        return Person::query()
            ->where('is_1x10', true)
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchGroup}%")
                    ->orWhere('cedula', 'like', "%{$this->searchGroup}%");
            })
            ->orderBy('name')
            ->limit(10)
            ->get();
    }

    #[Computed]
    public function mobilizations()
    {
        return ElectoralMobilization::query()
            ->with(['group', 'votingCenter', 'coordinator'])
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', "%{$this->search}%")
                        ->orWhere('description', 'like', "%{$this->search}%")
                        ->orWhereHas('group', function ($q): void {
                            $q->where('name', 'like', "%{$this->search}%");
                        })
                        ->orWhereHas('votingCenter', function ($q): void {
                            $q->where('name', 'like', "%{$this->search}%");
                        })
                        ->orWhereHas('coordinator', function ($q): void {
                            $q->where('name', 'like', "%{$this->search}%");
                        });
                });
            })
            ->when($this->statusFilter, function ($query): void {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->estateFilter, function ($query): void {
                $query->whereHas('votingCenter', function ($q): void {
                    $q->where('estate_id', $this->estateFilter);
                });
            })
            ->when($this->dateFilter, function ($query): void {
                switch ($this->dateFilter) {
                    case 'today':
                        $query->whereDate('mobilization_date', today());
                        break;
                    case 'tomorrow':
                        $query->whereDate('mobilization_date', today()->addDay());
                        break;
                    case 'this_week':
                        $query->whereBetween('mobilization_date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'next_week':
                        $query->whereBetween('mobilization_date', [now()->addWeek()->startOfWeek(), now()->addWeek()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('mobilization_date', now()->month)
                            ->whereYear('mobilization_date', now()->year);
                        break;
                    case 'past':
                        $query->where('mobilization_date', '<', now());
                        break;
                    case 'upcoming':
                        $query->where('mobilization_date', '>=', now());
                        break;
                }
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }

    #[Computed]
    public function mobilizationStats(): array
    {
        return [
            'total' => ElectoralMobilization::count(),
            'scheduled' => ElectoralMobilization::where('status', 'scheduled')->count(),
            'in_progress' => ElectoralMobilization::where('status', 'in_progress')->count(),
            'completed' => ElectoralMobilization::where('status', 'completed')->count(),
            'cancelled' => ElectoralMobilization::where('status', 'cancelled')->count(),
            'target_voters' => ElectoralMobilization::sum('target_voters'),
            'mobilized_voters' => ElectoralMobilization::sum('mobilized_voters'),
            'confirmed_votes' => ElectoralMobilization::sum('confirmed_votes'),
        ];
    }

    public function openCreateMobilizationModal(): void
    {
        $this->reset([
            'name', 'description', 'mobilization_date', 'voting_center_id', 'group_id',
            'coordinator_id', 'target_voters', 'status', 'notes',
        ]);
        $this->mobilization_date = now()->format('Y-m-d');
        $this->status = 'scheduled';
        $this->showCreateMobilizationModal = true;
    }

    public function closeCreateMobilizationModal(): void
    {
        $this->showCreateMobilizationModal = false;
    }

    public function createMobilization(): void
    {
        $validated = $this->validate();

        $validated['created_by'] = Auth::id();
        $validated['mobilized_voters'] = 0;
        $validated['confirmed_votes'] = 0;

        $mobilization = ElectoralMobilization::create($validated);

        // Si hay un líder 1x10 seleccionado, agregar a todas las personas asignadas a él
        if ($this->group_id) {
            $assignedPersons = Person::where('responsible_id', $this->group_id)->get();

            foreach ($assignedPersons as $person) {
                $mobilization->details()->create([
                    'person_id' => $person->id,
                    'status' => 'pending',
                    'registered_by' => Auth::id(),
                ]);
            }
        }

        \Flux::toast(
            text: 'Movilización creada exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        $this->closeCreateMobilizationModal();
    }

    public function cancelMobilization($mobilizationId): void
    {
        $mobilization = ElectoralMobilization::find($mobilizationId);

        if (! $mobilization) {
            return;
        }

        $mobilization->status = 'cancelled';
        $mobilization->save();

        \Flux::toast(
            text: 'Movilización cancelada exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.mobilization-electoral');
    }
}
