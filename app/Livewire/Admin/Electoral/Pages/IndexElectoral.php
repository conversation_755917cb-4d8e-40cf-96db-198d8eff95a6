<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralEvent;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Component;

class IndexElectoral extends Component
{
    public function mount(): void
    {
        //
    }

    #[Computed]
    public function statistics(): array
    {
        return [
            'events' => [
                'total' => ElectoralEvent::count(),
                'upcoming' => ElectoralEvent::upcoming()->count(),
                'completed' => ElectoralEvent::where('status', 'completed')->count(),
            ],
            'voting_centers' => [
                'total' => ElectoralVotingCenter::count(),
                'active' => ElectoralVotingCenter::where('status', 'active')->count(),
            ],
            'mobilization' => [
                'total' => ElectoralMobilization::count(),
                'upcoming' => ElectoralMobilization::upcoming()->count(),
                'completed' => ElectoralMobilization::where('status', 'completed')->count(),
                'mobilized_voters' => ElectoralMobilization::sum('mobilized_voters'),
                'confirmed_votes' => ElectoralMobilization::sum('confirmed_votes'),
            ],
            'people' => [
                'total' => Person::count(),
                'voters' => Person::where('role', 'Voter')->count(),
            ],
        ];
    }

    #[Computed]
    public function upcomingEvents()
    {
        return ElectoralEvent::with(['estate', 'municipality', 'parish', 'organizer'])
            ->upcoming()
            ->orderBy('start_date')
            ->limit(5)
            ->get();
    }

    #[Computed]
    public function recentMobilizations()
    {
        return ElectoralMobilization::with(['group', 'votingCenter', 'coordinator'])
            ->orderBy('mobilization_date', 'desc')
            ->limit(5)
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.index-electoral');
    }
}
