<?php

namespace App\Livewire\Admin\Electoral\Pages;

use App\Models\ElectoralVotingCenter;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class CreateElectoralVotingCenter extends Component
{
    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('nullable|string|max:255')]
    public $code = '';

    #[Rule('nullable|string')]
    public $address = '';

    #[Rule('nullable|exists:estates,id')]
    public $estate_id;

    #[Rule('nullable|exists:municipalities,id')]
    public $municipality_id;

    #[Rule('nullable|exists:parishes,id')]
    public $parish_id;

    #[Rule('nullable|string')]
    public $location_coordinates = '';

    #[Rule('nullable|integer|min:1')]
    public $total_voters;

    #[Rule('required|in:active,inactive')]
    public $status = 'active';

    // Listeners para los eventos del selector de ubicación
    protected function getListeners()
    {
        return [
            'estate-changed' => 'updateEstate',
            'municipality-changed' => 'updateMunicipality',
            'parish-changed' => 'updateParish',
        ];
    }

    public function updateEstate($value): void
    {
        $this->estate_id = $value;
    }

    public function updateMunicipality($value): void
    {
        $this->municipality_id = $value;
    }

    public function updateParish($value): void
    {
        $this->parish_id = $value;
    }

    public function save()
    {
        $validated = $this->validate();

        $center = ElectoralVotingCenter::create($validated);

        \Flux::toast(
            text: 'Centro de votación creado exitosamente',
            variant: 'success',
            heading: 'Éxito'
        );

        return redirect()->route('admin.electoral.voting-centers.show', $center);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.electoral.pages.create-electoral-voting-center');
    }
}
