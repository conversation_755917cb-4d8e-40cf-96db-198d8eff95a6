<?php

namespace App\Livewire\Admin\Campaigns\Pages;

use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class DestroyCampaign extends Component
{
    public function mount(): void
    {
        //
    }

    #[Layout('components.layouts.app')]
    public function render(): View
    {
        return view('livewire.admin.campaigns.pages.destroy-campaign');
    }
}
