<?php

namespace App\Livewire\Admin\Campaigns\Pages;

use App\Models\Campaign;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Livewire\Attributes\Layout;
use Livewire\Component;

class CampaignReports extends Component
{
    public $reportType = 'overview';

    public $dateRange = 'all';

    public $startDate = '';

    public $endDate = '';

    public $campaignType = 'all';

    public $campaignStatus = 'all';

    public function mount(): void
    {
        $this->authorize('view campaigns');

        // Inicializar fechas por defecto (último mes)
        $this->startDate = now()->subMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function getCampaignStatsProperty(): array
    {
        $query = Campaign::query();

        // Filtrar por tipo de campaña
        if ($this->campaignType !== 'all') {
            $query->where('type', $this->campaignType);
        }

        // Filtrar por estado de campaña
        if ($this->campaignStatus !== 'all') {
            $query->where('status', $this->campaignStatus);
        }

        // Filtrar por rango de fechas
        if ($this->dateRange === 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween('created_at', [$this->startDate.' 00:00:00', $this->endDate.' 23:59:59']);
        } elseif ($this->dateRange === 'month') {
            $query->where('created_at', '>=', now()->subMonth());
        } elseif ($this->dateRange === 'week') {
            $query->where('created_at', '>=', now()->subWeek());
        } elseif ($this->dateRange === 'day') {
            $query->where('created_at', '>=', now()->subDay());
        }

        // Estadísticas generales
        $totalCampaigns = $query->count();
        $activeCampaigns = $query->where('status', 'active')->count();
        $completedCampaigns = $query->where('status', 'completed')->count();
        $cancelledCampaigns = $query->where('status', 'cancelled')->count();

        // Estadísticas por tipo
        $campaignsByType = DB::table('campaigns')
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type')
            ->toArray();

        // Estadísticas por estado
        $campaignsByStatus = DB::table('campaigns')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        // Estadísticas de mensajes
        $totalMessages = DB::table('campaign_details')->count();
        $sentMessages = DB::table('campaign_details')->where('status', 'sent')->count();
        $deliveredMessages = DB::table('campaign_details')->where('status', 'delivered')->count();
        $readMessages = DB::table('campaign_details')->where('status', 'read')->count();
        $failedMessages = DB::table('campaign_details')->where('status', 'failed')->count();

        // Tasa de entrega
        $deliveryRate = $totalMessages > 0 ? round(($deliveredMessages / $totalMessages) * 100, 2) : 0;

        // Tasa de lectura
        $readRate = $deliveredMessages > 0 ? round(($readMessages / $deliveredMessages) * 100, 2) : 0;

        // Tasa de fallo
        $failureRate = $totalMessages > 0 ? round(($failedMessages / $totalMessages) * 100, 2) : 0;

        return [
            'total_campaigns' => $totalCampaigns,
            'active_campaigns' => $activeCampaigns,
            'completed_campaigns' => $completedCampaigns,
            'cancelled_campaigns' => $cancelledCampaigns,
            'campaigns_by_type' => $campaignsByType,
            'campaigns_by_status' => $campaignsByStatus,
            'total_messages' => $totalMessages,
            'sent_messages' => $sentMessages,
            'delivered_messages' => $deliveredMessages,
            'read_messages' => $readMessages,
            'failed_messages' => $failedMessages,
            'delivery_rate' => $deliveryRate,
            'read_rate' => $readRate,
            'failure_rate' => $failureRate,
        ];
    }

    public function getRecentCampaignsProperty()
    {
        return Campaign::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
    }

    public function getCampaignTypesProperty(): array
    {
        return [
            'all' => 'Todos los tipos',
            'whatsapp' => 'WhatsApp',
            'sms' => 'SMS',
            'email' => 'Email',
            'social_media' => 'Redes Sociales',
            'other' => 'Otro',
        ];
    }

    public function getCampaignStatusesProperty(): array
    {
        return [
            'all' => 'Todos los estados',
            'draft' => 'Borrador',
            'scheduled' => 'Programada',
            'active' => 'Activa',
            'paused' => 'Pausada',
            'completed' => 'Completada',
            'cancelled' => 'Cancelada',
        ];
    }

    public function getDateRangesProperty(): array
    {
        return [
            'all' => 'Todo el tiempo',
            'day' => 'Último día',
            'week' => 'Última semana',
            'month' => 'Último mes',
            'custom' => 'Personalizado',
        ];
    }

    public function exportReport()
    {
        $stats = $this->campaignStats;

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="campaign_report_'.now()->format('Y-m-d').'.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $callback = function () use ($stats): void {
            $file = fopen('php://output', 'w');

            // Encabezados
            fputcsv($file, ['Reporte de Campañas', 'Generado el: '.now()->format('d/m/Y H:i:s')]);
            fputcsv($file, []);

            // Estadísticas generales
            fputcsv($file, ['Estadísticas Generales']);
            fputcsv($file, ['Total de Campañas', $stats['total_campaigns']]);
            fputcsv($file, ['Campañas Activas', $stats['active_campaigns']]);
            fputcsv($file, ['Campañas Completadas', $stats['completed_campaigns']]);
            fputcsv($file, ['Campañas Canceladas', $stats['cancelled_campaigns']]);
            fputcsv($file, []);

            // Estadísticas por tipo
            fputcsv($file, ['Campañas por Tipo']);
            foreach ($stats['campaigns_by_type'] as $type => $count) {
                fputcsv($file, [$type, $count]);
            }
            fputcsv($file, []);

            // Estadísticas por estado
            fputcsv($file, ['Campañas por Estado']);
            foreach ($stats['campaigns_by_status'] as $status => $count) {
                fputcsv($file, [$status, $count]);
            }
            fputcsv($file, []);

            // Estadísticas de mensajes
            fputcsv($file, ['Estadísticas de Mensajes']);
            fputcsv($file, ['Total de Mensajes', $stats['total_messages']]);
            fputcsv($file, ['Mensajes Enviados', $stats['sent_messages']]);
            fputcsv($file, ['Mensajes Entregados', $stats['delivered_messages']]);
            fputcsv($file, ['Mensajes Leídos', $stats['read_messages']]);
            fputcsv($file, ['Mensajes Fallidos', $stats['failed_messages']]);
            fputcsv($file, []);

            // Tasas
            fputcsv($file, ['Tasas']);
            fputcsv($file, ['Tasa de Entrega', $stats['delivery_rate'].'%']);
            fputcsv($file, ['Tasa de Lectura', $stats['read_rate'].'%']);
            fputcsv($file, ['Tasa de Fallo', $stats['failure_rate'].'%']);

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.campaigns.pages.campaign-reports');
    }
}
