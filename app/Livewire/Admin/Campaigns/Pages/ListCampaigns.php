<?php

namespace App\Livewire\Admin\Campaigns\Pages;

use App\Models\Campaign;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class ListCampaigns extends Component
{
    use WithPagination;

    // Propiedades de búsqueda y filtrado
    public string $sortBy = 'created_at';

    public string $sortDirection = 'desc';

    public string $search = '';

    public string $status = '';

    public string $dateRange = '';

    public string $searchField = 'all';

    // Propiedades de paginación
    public int $perPage = 10;

    public array $perPageOptions = [10, 25, 50, 100];

    // Campos disponibles para búsqueda
    public function getSearchableFields(): array
    {
        return [
            'all' => __('global.all_fields'),
            'name' => __('campaigns.name'),
            'description' => __('campaigns.description'),
            'start_date' => __('campaigns.start_date'),
            'end_date' => __('campaigns.end_date'),
            'status' => __('campaigns.status'),
        ];
    }

    // Estados disponibles
    public function getStatusOptions(): array
    {
        return [
            '' => __('global.all_statuses'),
            'draft' => __('campaigns.campaign_status.draft'),
            'scheduled' => __('campaigns.campaign_status.scheduled'),
            'active' => __('campaigns.campaign_status.active'),
            'paused' => __('campaigns.campaign_status.paused'),
            'completed' => __('campaigns.campaign_status.completed'),
            'cancelled' => __('campaigns.campaign_status.cancelled'),
        ];
    }

    protected $listeners = [
        'campaignCreated' => '$refresh',
        'campaignUpdated' => '$refresh',
        'campaignDeleted' => '$refresh',
    ];

    public function mount(): void
    {
        $this->resetPage();
    }

    public function render(): View
    {
        return view('livewire.admin.campaigns.pages.list-campaigns');
    }

    public function editCampaign($campaign): void
    {
        $this->dispatch('editCampaign', $campaign);
    }

    public function sort($column): void
    {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function updatedStatus(): void
    {
        $this->resetPage();
    }

    public function updatedSearchField(): void
    {
        $this->resetPage();
    }

    public function updatedPerPage(): void
    {
        $this->resetPage();
    }

    /**
     * Export campaigns data to Excel
     */
    public function exportCampaigns(): void
    {
        // This would be implemented with a proper export functionality
        // For now, just show a toast message
        \Flux::toast(
            variant: 'info',
            heading: 'Exportación',
            text: 'La funcionalidad de exportación será implementada próximamente.'
        );
    }

    #[Computed]
    public function statistics(): array
    {
        return [
            'total' => Campaign::count(),
            'active' => Campaign::whereIn('status', ['active', 'processing'])->count(),
            'scheduled' => Campaign::where('status', 'scheduled')->count(),
            'completed' => Campaign::where('status', 'completed')->count(),
        ];
    }

    /**
     * Get the color for a campaign status
     */
    public function getStatusColor(string $status): string
    {
        return match ($status) {
            'active' => 'emerald',
            'processing' => 'sky',
            'scheduled' => 'amber',
            'paused' => 'orange',
            'completed' => 'green',
            'cancelled' => 'red',
            'draft' => 'zinc',
            default => 'zinc'
        };
    }

    #[Computed]
    public function campaigns()
    {
        return Campaign::query()
            ->when($this->search, function (Builder $query) {
                $search = '%'.trim($this->search).'%';

                if ($this->searchField !== 'all') {
                    if (in_array($this->searchField, ['start_date', 'end_date'])) {
                        return $query->whereDate($this->searchField, 'like', $search);
                    }

                    return $query->where($this->searchField, 'like', $search);
                }

                return $query->where(function (Builder $query) use ($search): void {
                    $query->where('name', 'like', $search)
                        ->orWhere('description', 'like', $search)
                        ->orWhere('status', 'like', $search)
                        ->orWhereDate('start_date', 'like', $search)
                        ->orWhereDate('end_date', 'like', $search);
                });
            })
            ->when($this->status !== '', function ($query): void {
                $query->where('status', $this->status);
            })
            ->withCount('details')
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate($this->perPage);
    }
}
