<?php

namespace App\Livewire\Admin\Campaigns\Pages;

use App\Livewire\Admin\Campaigns\Forms\FormCreateCampaign;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Component;

class CreateCampaign extends Component
{
    public FormCreateCampaign $form;

    public function save(): void
    {
        $this->form->createCampaign();
        $this->dispatch('campaignCreated');
    }

    public function mount(): void
    {
        //
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.campaigns.pages.create-campaign');
    }
}
