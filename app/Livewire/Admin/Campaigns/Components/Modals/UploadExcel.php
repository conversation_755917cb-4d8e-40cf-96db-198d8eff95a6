<?php

namespace App\Livewire\Admin\Campaigns\Components\Modals;

use App\Exports\CampaignTemplateExport;
use App\Imports\CampaignDetailsImport;
use App\Models\Campaign;
use Flux\Flux;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

class UploadExcel extends Component
{
    use WithFileUploads;

    public $excelFile;

    public Campaign $campaign;

    protected $rules = [
        'excelFile' => 'required|file|mimes:xlsx,xls,csv|max:10240', // max 10MB
    ];

    protected function messages(): array
    {
        return [
            'excelFile.required' => __('validation.required', ['attribute' => __('campaigns.excel_file')]),
            'excelFile.file' => __('validation.file', ['attribute' => __('campaigns.excel_file')]),
            'excelFile.mimes' => __('validation.mimes', ['attribute' => __('campaigns.excel_file'), 'values' => 'xlsx, xls, csv']),
            'excelFile.max' => __('validation.max.file', ['attribute' => __('campaigns.excel_file'), 'max' => 10240]),
        ];
    }

    public function uploadExcel(): void
    {
        $this->validate();

        try {
            // Eliminar detalles existentes
            $this->campaign->details()->delete();

            // Importar nuevos detalles
            Excel::import(
                new CampaignDetailsImport($this->campaign->id),
                $this->excelFile
            );

            $this->dispatch('campaignDetailsUploaded');
            Flux::toast(text: __('campaigns.file_processed_correctly'), variant: 'success');
            Flux::modals()->close();
            $this->reset('excelFile');

        } catch (\Exception $e) {
            Flux::toast(text: __('campaigns.error_processing_file').$e->getMessage(), variant: 'danger');
        }
    }

    public function downloadTemplate()
    {
        return Excel::download(
            new CampaignTemplateExport,
            'plantilla_mensajes.xlsx'
        );
    }

    public function render()
    {
        return view('livewire.admin.campaigns.components.modals.upload-excel');
    }
}
