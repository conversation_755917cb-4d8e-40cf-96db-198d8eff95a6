<?php

namespace App\Livewire\Admin\Campaigns\Components\Modals;

use App\Models\CampaignDetail;
use Flux;
use Livewire\Component;

class ViewMessage extends Component
{
    public ?CampaignDetail $message = null;

    public function getStatusOptions(): array
    {
        return [
            'pending' => __('campaigns.message_status.pending'),
            'processing' => __('campaigns.message_status.processing'),
            'sent' => __('campaigns.message_status.sent'),
            'delivered' => __('campaigns.message_status.delivered'),
            'failed' => __('campaigns.message_status.failed'),
        ];
    }

    protected $listeners = ['viewMessage'];

    public function viewMessage(int $messageId): void
    {
        $this->message = CampaignDetail::findOrFail($messageId);
        Flux::modal('view-message')->show();
    }

    public function getStatusColor(string $status): string
    {
        return match ($status) {
            'pending' => 'gray',
            'processing' => 'blue',
            'sent' => 'yellow',
            'delivered' => 'green',
            'failed' => 'red',
            default => 'gray',
        };
    }

    public function render()
    {
        return view('livewire.admin.campaigns.components.modals.view-message');
    }
}
