<?php

namespace App\Livewire\Components;

use App\Services\CedulaApiService;
use Livewire\Component;

class CedulaSearch extends Component
{
    public $cedula = '';
    public $personData;
    public $loading = false;
    public $error;
    
    public function search(): void
    {
        $this->validate([
            'cedula' => 'required|numeric|digits_between:5,10',
        ]);
        
        $this->loading = true;
        $this->error = null;
        $this->personData = null;
        
        try {
            $cedulaService = app(CedulaApiService::class);
            $result = $cedulaService->getPersonData($this->cedula);
            
            if ($result) {
                $this->personData = $result;
            } else {
                $this->error = 'No se encontraron datos para esta cédula.';
            }
        } catch (\Exception $e) {
            $this->error = 'Error al consultar la API: ' . $e->getMessage();
        } finally {
            $this->loading = false;
        }
    }
    
    public function fillForm(): void
    {
        if (!$this->personData) {
            return;
        }
        
        // Emitir evento con los datos de la persona
        $this->dispatch('cedula-data-found', [
            'name' => $this->personData['nombres'] . ' ' . $this->personData['apellidos'],
            'cedula' => $this->cedula,
            'data' => $this->personData,
        ]);
        
        // Limpiar datos
        $this->reset(['cedula', 'personData', 'error']);
    }
    
    public function render()
    {
        return view('livewire.components.cedula-search');
    }
}
