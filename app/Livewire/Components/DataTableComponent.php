<?php

namespace App\Livewire\Components;

use Livewire\Component;
use Livewire\WithPagination;

abstract class DataTableComponent extends Component
{
    use WithPagination;

    public $perPage = 10;

    public $sortField = 'created_at';

    public $sortDirection = 'desc';

    public $search = '';

    public $filters = [];

    public $selectedItems = [];

    public $selectAll = false;

    /**
     * Reset pagination when search or filters change.
     */
    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    /**
     * Reset pagination when filters change.
     */
    public function updatedFilters(): void
    {
        $this->resetPage();
    }

    /**
     * Reset pagination when per page changes.
     */
    public function updatedPerPage(): void
    {
        $this->resetPage();
    }

    /**
     * Sort by the given field.
     *
     * @param  string  $field
     */
    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    /**
     * Update the selectAll property.
     *
     * @param  bool  $value
     */
    public function updatedSelectAll($value): void
    {
        if ($value) {
            $this->selectedItems = $this->getQuery()
                ->pluck($this->getKeyName())
                ->map(fn ($id): string => (string) $id)
                ->toArray();
        } else {
            $this->selectedItems = [];
        }
    }

    /**
     * Get the query for the data table.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    abstract protected function getQuery();

    /**
     * Get the key name for the model.
     *
     * @return string
     */
    protected function getKeyName()
    {
        return 'id';
    }

    /**
     * Get the data for the data table.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    protected function getData()
    {
        return $this->getQuery()
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }
}
