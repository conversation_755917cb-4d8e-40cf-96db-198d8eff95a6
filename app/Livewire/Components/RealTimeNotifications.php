<?php

namespace App\Livewire\Components;

use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\Component;

class RealTimeNotifications extends Component
{
    public $notifications = [];

    public $unreadCount = 0;

    public $showAll = false;

    public function mount(): void
    {
        $this->loadNotifications();
    }

    public function loadNotifications(): void
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();

        // Cargar las notificaciones
        $query = Notification::where('user_id', $user->id);

        if (!$this->showAll) {
            $query->whereNull('read_at');
        }

        $this->notifications = $query->latest()
            ->take(5)
            ->get()
            ->map(fn($notification): array => [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'link' => $notification->link,
                'data' => $notification->data,
                'created_at' => $notification->created_at->diffForHumans(),
                'read_at' => $notification->read_at,
                'icon' => $notification->icon,
                'color' => $notification->color,
            ])
            ->toArray();

        // Contar notificaciones no leídas
        $this->unreadCount = Notification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->count();
    }

    #[On('notification-received')]
    public function handleNewNotification(): void
    {
        $this->loadNotifications();

        // Mostrar una alerta o sonido
        $this->dispatch('notification-alert');
    }

    #[On('echo:user.{userId},notification.created')]
    public function handleBroadcastedNotification($notification): void
    {
        $this->loadNotifications();

        // Mostrar una alerta o sonido
        $this->dispatch('notification-alert');
    }

    public function toggleShowAll(): void
    {
        $this->showAll = !$this->showAll;
        $this->loadNotifications();
    }

    public function markAsRead($notificationId): void
    {
        $notification = Notification::where('id', $notificationId)
            ->where('user_id', Auth::id())
            ->first();

        if ($notification) {
            $notification->markAsRead();
            $this->loadNotifications();
        }
    }

    public function markAllAsRead(): void
    {
        Notification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        $this->loadNotifications();
    }

    public function render()
    {
        return view('livewire.components.real-time-notifications');
    }
}
