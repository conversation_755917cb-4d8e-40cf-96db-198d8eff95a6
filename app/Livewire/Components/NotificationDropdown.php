<?php

namespace App\Livewire\Components;

use App\Services\NotificationService;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\On;
use Livewire\Component;

class NotificationDropdown extends Component
{
    public $unreadCount = 0;

    public $notifications = [];

    public $showAll = false;

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $this->loadNotifications();
    }

    /**
     * Load notifications.
     */
    public function loadNotifications(): void
    {
        $notificationService = app(NotificationService::class);

        $this->unreadCount = $notificationService->countUnreadNotifications();

        if ($this->showAll) {
            $this->notifications = $notificationService->getAllNotifications(null, 20)->toArray();
        } else {
            $this->notifications = $notificationService->getUnreadNotifications(null, 10)->toArray();
        }
    }

    /**
     * Toggle between showing all notifications or only unread ones.
     */
    public function toggleShowAll(): void
    {
        $this->showAll = ! $this->showAll;
        $this->loadNotifications();
    }

    /**
     * Mark a notification as read.
     */
    public function markAsRead(int $notificationId): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->markAsRead($notificationId);

        $this->loadNotifications();
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->markAllAsRead();

        $this->loadNotifications();
    }

    /**
     * Delete a notification.
     */
    public function deleteNotification(int $notificationId): void
    {
        $notificationService = app(NotificationService::class);
        $notificationService->deleteNotification($notificationId);

        $this->loadNotifications();
    }

    /**
     * Listen for the refresh-notifications event.
     */
    #[On('refresh-notifications')]
    public function refreshNotifications(): void
    {
        $this->loadNotifications();
    }

    /**
     * Render the component.
     */
    public function render(): View
    {
        return view('livewire.components.notification-dropdown');
    }
}
