<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\Municipality;
use App\Models\Parish;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Computed;
use Livewire\Component;

class LocationSelector extends Component
{
    public $estateId;

    public $municipalityId;

    public $parishId;

    public function mount($estateId = null, $municipalityId = null, $parishId = null): void
    {
        $this->estateId = $estateId;

        // Verificar que el municipio pertenezca al estado seleccionado
        if ($estateId && $municipalityId) {
            $validMunicipality = Municipality::where('id', $municipalityId)
                ->where('estate_id', $estateId)
                ->exists();

            $this->municipalityId = $validMunicipality ? $municipalityId : null;
        } else {
            $this->municipalityId = null;
        }

        // Verificar que la parroquia pertenezca al municipio seleccionado
        if ($municipalityId && $parishId) {
            $validParish = Parish::where('id', $parishId)
                ->where('municipality_id', $municipalityId)
                ->exists();

            $this->parishId = $validParish ? $parishId : null;
        } else {
            $this->parishId = null;
        }
    }

    // Eventos para notificar a los componentes padres sobre cambios
    public function updatedEstateId($value): void
    {
        try {
            // Resetear municipio y parroquia
            $this->municipalityId = null;
            $this->parishId = null;

            // Verificar que el estado exista
            if ($value) {
                $estateExists = Estate::where('id', $value)->exists();
                if (!$estateExists) {
                    $this->estateId = null;
                    $value = null;
                }
            }

            // Notificar al componente padre
            $this->dispatch('estate-changed', $value);
        } catch (\Exception) {
            // En caso de error, resetear todo
            $this->estateId = null;
            $this->municipalityId = null;
            $this->parishId = null;
            $this->dispatch('estate-changed', null);
        }
    }

    public function updatedMunicipalityId($value): void
    {
        try {
            // Resetear parroquia
            $this->parishId = null;

            // Verificar que el municipio pertenezca al estado seleccionado
            if ($value && $this->estateId) {
                $validMunicipality = Municipality::where('id', $value)
                    ->where('estate_id', $this->estateId)
                    ->exists();

                if (!$validMunicipality) {
                    $this->municipalityId = null;
                    $value = null;
                }
            } else {
                $this->municipalityId = null;
                $value = null;
            }

            // Notificar al componente padre
            $this->dispatch('municipality-changed', $value);
        } catch (\Exception) {
            // En caso de error, resetear municipio y parroquia
            $this->municipalityId = null;
            $this->parishId = null;
            $this->dispatch('municipality-changed', null);
        }
    }

    public function updatedParishId($value): void
    {
        try {
            // Verificar que la parroquia pertenezca al municipio seleccionado
            if ($value && $this->municipalityId) {
                $validParish = Parish::where('id', $value)
                    ->where('municipality_id', $this->municipalityId)
                    ->exists();

                if (!$validParish) {
                    $this->parishId = null;
                    $value = null;
                }
            } else {
                $this->parishId = null;
                $value = null;
            }

            // Notificar al componente padre
            $this->dispatch('parish-changed', $value);
        } catch (\Exception) {
            // En caso de error, resetear parroquia
            $this->parishId = null;
            $this->dispatch('parish-changed', null);
        }
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function municipalities()
    {
        if (! $this->estateId) {
            return collect();
        }

        return Municipality::where('estate_id', $this->estateId)
            ->orderBy('name')
            ->get();
    }

    #[Computed]
    public function parishes()
    {
        if (! $this->municipalityId) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipalityId)
            ->orderBy('name')
            ->get();
    }

    public function render(): View
    {
        return view('livewire.location-selector');
    }
}
