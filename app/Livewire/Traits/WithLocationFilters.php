<?php

namespace App\Livewire\Traits;

use App\Models\Estate;
use App\Models\Municipality;
use App\Models\Parish;
use Livewire\Attributes\Computed;

trait WithLocationFilters
{
    public $estate_id = '';

    public $municipality_id = '';

    public $parish_id = '';

    public function updatedEstateId(): void
    {
        // Resetear el municipio y parroquia seleccionados cuando cambia el estado
        $this->municipality_id = '';
        $this->parish_id = '';
        $this->resetPage();
    }

    public function updatedMunicipalityId(): void
    {
        // Resetear la parroquia seleccionada cuando cambia el municipio
        $this->parish_id = '';
        $this->resetPage();
    }

    public function updatedParishId(): void
    {
        $this->resetPage();
    }

    #[Computed]
    public function estates()
    {
        return Estate::orderBy('name')->get();
    }

    #[Computed]
    public function municipalities()
    {
        if (! $this->estate_id) {
            return collect();
        }

        return Municipality::where('estate_id', $this->estate_id)
            ->orderBy('name')
            ->get();
    }

    #[Computed]
    public function parishes()
    {
        if (! $this->municipality_id) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipality_id)
            ->orderBy('name')
            ->get();
    }

    /**
     * Apply location filters to a query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyLocationFilters($query)
    {
        return $query
            ->when($this->estate_id, fn ($query) => $query->where('estate_id', $this->estate_id))
            ->when($this->municipality_id, fn ($query) => $query->where('municipality_id', $this->municipality_id))
            ->when($this->parish_id, fn ($query) => $query->where('parish_id', $this->parish_id));
    }
}
