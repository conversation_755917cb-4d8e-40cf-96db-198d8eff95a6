<?php

namespace App\Livewire\Traits;

use App\Models\Person;
use Livewire\Attributes\Computed;

trait WithPersonSearch
{
    public $searchTerm = '';

    public $searchResults = [];

    public $selectedPerson;

    public $excludeIds = [];

    public $onlyLeaders = false;

    public $onlyNonLeaders = false;

    public $maxResults = 10;

    public function updatedSearchTerm(): void
    {
        if (strlen($this->searchTerm) < 3) {
            $this->searchResults = [];

            return;
        }

        $this->searchResults = $this->getSearchResults();
    }

    public function selectPerson($personId): void
    {
        $this->selectedPerson = Person::find($personId);
        $this->searchTerm = '';
        $this->searchResults = [];
    }

    public function clearSelectedPerson(): void
    {
        $this->selectedPerson = null;
    }

    #[Computed]
    public function getSearchResults()
    {
        if (strlen($this->searchTerm) < 3) {
            return [];
        }

        return Person::query()
            ->withCommonRelations()
            ->when($this->excludeIds, function ($query): void {
                $query->whereNotIn('id', $this->excludeIds);
            })
            ->when($this->onlyLeaders, function ($query): void {
                $query->where('is_1x10', true);
            })
            ->when($this->onlyNonLeaders, function ($query): void {
                $query->where('is_1x10', false);
            })
            ->where(function ($query): void {
                $query->where('name', 'like', "%{$this->searchTerm}%")
                    ->orWhere('cedula', 'like', "%{$this->searchTerm}%")
                    ->orWhere('email', 'like', "%{$this->searchTerm}%")
                    ->orWhere('phone', 'like', "%{$this->searchTerm}%");
            })
            ->orderBy('name')
            ->limit($this->maxResults)
            ->get();
    }
}
