<?php

namespace App\Livewire\Frontend\Person;

use App\Events\PersonAssignedTo1x10Leader;
use App\Models\Person;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class ManageAssignedPersons extends Component
{
    use WithPagination;

    // Propiedades para búsqueda y filtrado
    public $search = '';
    public $perPage = 10;

    // Propiedades para crear persona
    public $showCreatePersonModal = false;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('required|integer')]
    public $cedula = '';

    #[Rule('required|string|max:20')]
    public $phone = '';

    #[Rule('nullable|email|max:255')]
    public $email = '';

    #[Rule('nullable|string')]
    public $address = '';

    #[Rule('nullable|string|max:255')]
    public $polling_center = '';

    #[Rule('nullable|exists:estates,id')]
    public $estate_id;

    #[Rule('nullable|exists:municipalities,id')]
    public $municipality_id;

    #[Rule('nullable|exists:parishes,id')]
    public $parish_id;

    #[Rule('required|in:Militante,Votante')]
    public $role = 'Votante';

    #[Rule('required|string|max:255')]
    public $party = 'PSUV';

    #[Rule('required|in:Activo,Inactivo')]
    public $activity_status = 'Activo';

    #[Rule('nullable|string')]
    public $observations = '';

    // Propiedades para editar persona
    public $editingPersonId;
    public $showEditPersonModal = false;

    // Propiedades para confirmar eliminación
    public $personToDelete;
    public $showDeleteConfirmation = false;

    public function mount()
    {
        try {
            // Verificar que el usuario autenticado tenga una persona asociada
            $user = Auth::user();
            if (!$user) {
                session()->flash('error', __('auth.not_logged_in'));
                return redirect()->route('login');
            }

            if (!$user->person) {
                \Flux::toast(
                    text: __('person.no_person_profile'),
                    variant: 'error',
                    heading: __('global.error')
                );
                return redirect()->route('dashboard');
            }

            // Verificar que la persona sea líder 1x10
            if (!$user->person->is_1x10) {
                \Flux::toast(
                    text: __('person.not_1x10_leader'),
                    variant: 'error',
                    heading: __('global.error')
                );
                return redirect()->route('dashboard');
            }
        } catch (\Exception $e) {
            \Flux::toast(
                text: 'Error al cargar el componente: ' . $e->getMessage(),
                variant: 'error',
                heading: __('global.error')
            );
            return redirect()->route('dashboard');
        }
        return null;
    }

    public function openCreatePersonModal(): void
    {
        $this->resetValidation();
        $this->resetCreatePersonForm();
        $this->showCreatePersonModal = true;
    }

    public function resetCreatePersonForm(): void
    {
        $this->name = '';
        $this->cedula = '';
        $this->phone = '';
        $this->email = '';
        $this->address = '';
        $this->polling_center = '';
        $this->estate_id = null;
        $this->municipality_id = null;
        $this->parish_id = null;
        $this->role = 'Votante';
        $this->party = 'PSUV';
        $this->activity_status = 'Activo';
        $this->observations = '';
    }

    /**
     * Verifica si una persona existe por su número de cédula y si está disponible para asignar
     *
     * @param int $cedula Número de cédula a verificar
     * @return array [existe, disponible, persona]
     */
    protected function checkPersonByCedula($cedula): array
    {
        if (empty($cedula)) {
            return [false, true, null];
        }

        // Buscar la persona por cédula
        $person = Person::where('cedula', $cedula)->first();

        // Si no existe, retornar [false, true, null]
        if (!$person) {
            return [false, true, null];
        }

        // Si existe, verificar si ya está asignada a otro líder 1x10
        $isAvailable = $person->responsible_id === null;

        // Si la persona ya está asignada al líder actual, considerarla disponible
        if (!$isAvailable && $person->responsible_id === Auth::user()->person->id) {
            $isAvailable = true;
        }

        // Retornar [true, disponible, persona]
        return [true, $isAvailable, $person];
    }

    public function createPerson(): void
    {
        // Validar solo los campos básicos primero (sin validar unicidad de cédula)
        $this->validate([
            'name' => 'required|string|max:255',
            'cedula' => 'required|integer|digits_between:5,20',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'polling_center' => 'nullable|string|max:255',
            'estate_id' => 'nullable|exists:estates,id',
            'municipality_id' => 'nullable|exists:municipalities,id|required_with:estate_id',
            'parish_id' => 'nullable|exists:parishes,id|required_with:municipality_id',
            'role' => 'required|in:Militante,Votante',
            'party' => 'required|string|max:255',
            'activity_status' => 'required|in:Activo,Inactivo',
            'observations' => 'nullable|string',
        ]);

        try {
            // Verificar si la persona ya existe por cédula
            [$exists, $isAvailable, $existingPerson] = $this->checkPersonByCedula($this->cedula);

            // Obtener el usuario autenticado y su persona asociada
            $user = Auth::user();
            $leader = $user->person;

            DB::beginTransaction();

            if ($exists) {
                // Si la persona existe pero ya está asignada a otro líder
                if (!$isAvailable) {
                    DB::rollBack();
                    \Flux::toast(
                        text: __('person.already_assigned_to_another_leader'),
                        variant: 'error',
                        heading: __('global.error')
                    );
                    return;
                }

                // Si la persona existe y está disponible, asignarla al líder actual
                $existingPerson->update([
                    'responsible_id' => $leader->id
                ]);

                // Disparar evento de asignación
                event(new PersonAssignedTo1x10Leader($existingPerson, $leader));

                $successMessage = __('person.existing_person_assigned');
            } else {
                // Si la persona no existe, crearla
                $person = Person::create([
                    'name' => $this->name,
                    'cedula' => $this->cedula,
                    'phone' => $this->phone,
                    'email' => $this->email,
                    'address' => $this->address,
                    'polling_center' => $this->polling_center,
                    'estate_id' => $this->estate_id,
                    'municipality_id' => $this->municipality_id,
                    'parish_id' => $this->parish_id,
                    'role' => $this->role,
                    'party' => $this->party,
                    'activity_status' => $this->activity_status,
                    'observations' => $this->observations,
                    'responsible_id' => $leader->id, // Asignar directamente al líder 1x10
                ]);

                // Disparar evento de asignación
                event(new PersonAssignedTo1x10Leader($person, $leader));

                $successMessage = __('person.person_created');
            }

            DB::commit();

            $this->showCreatePersonModal = false;
            $this->resetCreatePersonForm();

            \Flux::toast(
                text: $successMessage,
                variant: 'success',
                heading: __('global.success')
            );
        } catch (\Exception $e) {
            DB::rollBack();

            \Flux::toast(
                text: __('person.error_creating_person', ['message' => $e->getMessage()]),
                variant: 'error',
                heading: __('global.error')
            );
        }
    }

    public function openEditPersonModal($personId): void
    {
        $this->resetValidation();

        $person = Person::findOrFail($personId);

        // Verificar que la persona pertenezca al líder actual
        if ($person->responsible_id !== Auth::user()->person->id) {
            \Flux::toast(
                text: __('person.no_permission_edit'),
                variant: 'error',
                heading: __('global.error')
            );
            return;
        }

        $this->editingPersonId = $person->id;
        $this->name = $person->name;
        $this->cedula = $person->cedula;
        $this->phone = $person->phone;
        $this->email = $person->email;
        $this->address = $person->address;
        $this->polling_center = $person->polling_center;
        $this->estate_id = $person->estate_id;
        $this->municipality_id = $person->municipality_id;
        $this->parish_id = $person->parish_id;
        $this->role = $person->role;
        $this->party = $person->party;
        $this->activity_status = $person->activity_status;
        $this->observations = $person->observations;

        $this->showEditPersonModal = true;
    }

    public function updatePerson(): void
    {
        // Validar los campos, pero permitir que la cédula sea la misma para la persona que se está editando
        $this->validate([
            'name' => 'required|string|max:255',
            'cedula' => 'required|integer|digits_between:5,20|unique:people,cedula,' . $this->editingPersonId,
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'polling_center' => 'nullable|string|max:255',
            'estate_id' => 'nullable|exists:estates,id',
            'municipality_id' => 'nullable|exists:municipalities,id|required_with:estate_id',
            'parish_id' => 'nullable|exists:parishes,id|required_with:municipality_id',
            'role' => 'required|in:Militante,Votante',
            'party' => 'required|string|max:255',
            'activity_status' => 'required|in:Activo,Inactivo',
            'observations' => 'nullable|string',
        ]);

        try {
            $person = Person::findOrFail($this->editingPersonId);

            // Verificar que la persona pertenezca al líder actual
            if ($person->responsible_id !== Auth::user()->person->id) {
                \Flux::toast(
                    text: __('person.no_permission_edit'),
                    variant: 'error',
                    heading: __('global.error')
                );
                return;
            }

            $person->update([
                'name' => $this->name,
                'cedula' => $this->cedula,
                'phone' => $this->phone,
                'email' => $this->email,
                'address' => $this->address,
                'polling_center' => $this->polling_center,
                'estate_id' => $this->estate_id,
                'municipality_id' => $this->municipality_id,
                'parish_id' => $this->parish_id,
                'role' => $this->role,
                'party' => $this->party,
                'activity_status' => $this->activity_status,
                'observations' => $this->observations,
            ]);

            $this->showEditPersonModal = false;

            \Flux::toast(
                text: __('person.person_updated'),
                variant: 'success',
                heading: __('global.success')
            );
        } catch (\Exception $e) {
            \Flux::toast(
                text: __('person.error_updating_person', ['message' => $e->getMessage()]),
                variant: 'error',
                heading: __('global.error')
            );
        }
    }

    public function confirmDelete($personId): void
    {
        $this->personToDelete = Person::findOrFail($personId);

        // Verificar que la persona pertenezca al líder actual
        if ($this->personToDelete->responsible_id !== Auth::user()->person->id) {
            \Flux::toast(
                text: __('person.no_permission_unassign'),
                variant: 'error',
                heading: __('global.error')
            );
            return;
        }

        $this->showDeleteConfirmation = true;
    }

    public function deletePerson(): void
    {
        try {
            // Verificar que la persona pertenezca al líder actual
            if ($this->personToDelete->responsible_id !== Auth::user()->person->id) {
                \Flux::toast(
                    text: __('person.no_permission_unassign'),
                    variant: 'error',
                    heading: __('global.error')
                );
                return;
            }

            // Desasignar la persona del líder (no eliminarla completamente)
            $this->personToDelete->update([
                'responsible_id' => null
            ]);

            $this->showDeleteConfirmation = false;
            $this->personToDelete = null;

            \Flux::toast(
                text: __('person.person_unassigned'),
                variant: 'success',
                heading: __('global.success')
            );
        } catch (\Exception $e) {
            \Flux::toast(
                text: __('person.error_unassigning_person', ['message' => $e->getMessage()]),
                variant: 'error',
                heading: __('global.error')
            );
        }
    }

    public function getAssignedPersonsProperty()
    {
        $leader = Auth::user()->person;

        return Person::where('responsible_id', $leader->id)
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('cedula', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy('name')
            ->paginate($this->perPage);
    }

    // Listeners para los eventos del selector de ubicación
    protected function getListeners()
    {
        return [
            'estate-changed' => 'updateEstate',
            'municipality-changed' => 'updateMunicipality',
            'parish-changed' => 'updateParish',
        ];
    }

    public function updateEstate($value): void
    {
        $this->estate_id = $value;
    }

    public function updateMunicipality($value): void
    {
        $this->municipality_id = $value;
    }

    public function updateParish($value): void
    {
        $this->parish_id = $value;
    }

    public function render()
    {
        return view('livewire.frontend.person.manage-assigned-persons', [
            'assignedPersons' => $this->assignedPersons,
        ])->layout('components.layouts.app.frontend');
    }
}
