<?php

namespace App\Livewire;

use App\Models\ElectoralEvent;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Person;
use App\Models\Tracking;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;

class Dashboard extends Component
{
    public $dateRange = 'month';

    #[Computed]
    public function statistics()
    {
        $user = Auth::user();
        $leaderId = $user->person->id ?? null;

        // Usar caché para mejorar rendimiento
        $cacheKey = "dashboard_stats_{$user->id}_{$this->dateRange}";
        $cacheDuration = 5; // minutos

        return Cache::remember($cacheKey, $cacheDuration * 60, function () use ($leaderId, $user): array {
            $stats = [
                'assigned_persons' => $leaderId ? Person::where('responsible_id', $leaderId)->count() : 0,
                'recent_trackings' => $leaderId ? Tracking::where('user_id', Auth::id())
                    ->whereDate('created_at', '>=', now()->subDays(7))
                    ->count() : 0,
                'upcoming_events' => ElectoralEvent::where('status', 'scheduled')
                    ->where('start_date', '>=', now())
                    ->where('start_date', '<=', now()->addDays(30))
                    ->count(),
                'mobilizations' => ElectoralMobilization::where('status', 'scheduled')
                    ->where('mobilization_date', '>=', now())
                    ->count(),
                'voting_centers' => ElectoralVotingCenter::count(),
                'completion_rate' => $this->calculateCompletionRate($user),
            ];

            // Datos simplificados para evitar errores
            $stats['chart_data'] = [];
            $stats['map_data'] = [];

            return $stats;
        });
    }

    protected function calculateCompletionRate($user): int|float
    {
        if (!$user->person || !$user->hasRole('1x10_leader')) {
            return 0;
        }

        $targetCount = 10; // El objetivo es tener 10 personas asignadas
        $actualCount = Person::where('responsible_id', $user->person->id)->count();

        return min(100, round(($actualCount / $targetCount) * 100));
    }

    protected function getChartData(): array
    {
        // Datos simplificados para evitar errores
        return [];
    }

    protected function getMapData(): array
    {
        // Datos simplificados para evitar errores
        return [];
    }

    #[Computed]
    public function assignedPersons()
    {
        $user = Auth::user();
        $leaderId = $user->person->id ?? null;

        if (!$leaderId) {
            return collect();
        }

        return Person::where('responsible_id', $leaderId)
            ->orderBy('name')
            ->limit(5)
            ->get(['id', 'name', 'cedula', 'phone', 'created_at']);
    }

    #[Computed]
    public function upcomingEvents()
    {
        return ElectoralEvent::where('status', 'scheduled')
            ->where('start_date', '>=', now())
            ->orderBy('start_date')
            ->limit(3)
            ->get(['id', 'name', 'start_date', 'location', 'description']);
    }

    #[Computed]
    public function upcomingMobilizations()
    {
        return ElectoralMobilization::where('status', 'scheduled')
            ->where('mobilization_date', '>=', now())
            ->orderBy('mobilization_date')
            ->limit(3)
            ->with('votingCenter:id,name')
            ->get(['id', 'name', 'mobilization_date', 'voting_center_id', 'target_voters']);
    }

    #[Computed]
    public function recentTrackings()
    {
        return Tracking::where('user_id', Auth::id())
            ->with('person:id,name,cedula')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get(['id', 'person_id', 'tracking_type', 'notes', 'created_at']);
    }

    public function updateDateRange($range): void
    {
        $this->dateRange = $range;

        // Invalidar la caché para obtener nuevos datos
        $user = Auth::user();
        $cacheKey = "dashboard_stats_{$user->id}_{$this->dateRange}";
        Cache::forget($cacheKey);
    }

    public function render(): View
    {
        return view('livewire.dashboard')
            ->layout('components.layouts.app.frontend');
    }
}
