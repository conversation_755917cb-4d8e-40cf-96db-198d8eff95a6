<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AuthenticationService
{
    /**
     * Maximum number of failed login attempts before lockout.
     */
    protected int $maxAttempts = 5;

    /**
     * Lockout duration in minutes.
     */
    protected int $lockoutDuration = 15;

    /**
     * Whether to use two-factor authentication.
     */
    protected bool $use2FA = false;

    /**
     * Two-factor authentication code expiry in minutes.
     */
    protected int $twoFactorExpiry = 10;

    /**
     * Create a new service instance.
     */
    public function __construct(/**
     * Logging service instance.
     */
    protected LoggingService $loggingService)
    {
        $this->use2FA = config('auth.two_factor_enabled', false);
    }

    /**
     * Attempt to authenticate a user.
     */
    public function attemptLogin(string $username, string $password, bool $remember = false): array
    {
        // Check if the user is locked out
        if ($this->isLockedOut($username)) {
            $this->loggingService->warning("Login attempt for locked out user: {$username}", [
                'username' => $username,
                'ip' => request()->ip(),
            ]);

            return [
                'success' => false,
                'message' => 'Too many failed login attempts. Please try again later.',
                'locked_out' => true,
                'requires_2fa' => false,
            ];
        }

        // Attempt to authenticate
        if (Auth::attempt(['username' => $username, 'password' => $password], $remember)) {
            $user = Auth::user();

            // Reset failed login attempts
            $this->resetFailedAttempts($username);

            // Check if 2FA is required
            if ($this->use2FA && $user->two_factor_enabled) {
                Auth::logout();

                $code = $this->generateTwoFactorCode($user);

                $this->loggingService->info("Two-factor authentication required for user: {$username}", [
                    'user_id' => $user->id,
                    'username' => $username,
                ]);

                return [
                    'success' => false,
                    'message' => 'Two-factor authentication required.',
                    'locked_out' => false,
                    'requires_2fa' => true,
                    'user_id' => $user->id,
                ];
            }

            $this->loggingService->info("User logged in: {$username}", [
                'user_id' => $user->id,
                'username' => $username,
            ]);

            return [
                'success' => true,
                'message' => 'Login successful.',
                'locked_out' => false,
                'requires_2fa' => false,
                'user' => $user,
            ];
        }

        // Increment failed login attempts
        $this->incrementFailedAttempts($username);

        $this->loggingService->warning("Failed login attempt: {$username}", [
            'username' => $username,
            'ip' => request()->ip(),
        ]);

        return [
            'success' => false,
            'message' => 'Invalid credentials.',
            'locked_out' => false,
            'requires_2fa' => false,
        ];
    }

    /**
     * Verify a two-factor authentication code.
     */
    public function verifyTwoFactorCode(int $userId, string $code, bool $remember = false): array
    {
        $user = User::find($userId);

        if (! $user) {
            return [
                'success' => false,
                'message' => 'User not found.',
            ];
        }

        $cacheKey = "2fa_code_{$userId}";
        $storedCode = Cache::get($cacheKey);

        if (! $storedCode || $storedCode !== $code) {
            $this->loggingService->warning("Invalid two-factor code for user: {$user->username}", [
                'user_id' => $user->id,
                'username' => $user->username,
            ]);

            return [
                'success' => false,
                'message' => 'Invalid two-factor authentication code.',
            ];
        }

        // Clear the code
        Cache::forget($cacheKey);

        // Log the user in
        Auth::login($user, $remember);

        $this->loggingService->info("User logged in with two-factor authentication: {$user->username}", [
            'user_id' => $user->id,
            'username' => $user->username,
        ]);

        return [
            'success' => true,
            'message' => 'Two-factor authentication successful.',
            'user' => $user,
        ];
    }

    /**
     * Generate a two-factor authentication code for a user.
     */
    public function generateTwoFactorCode(User $user): string
    {
        $code = sprintf('%06d', mt_rand(0, 999999));
        $cacheKey = "2fa_code_{$user->id}";

        Cache::put($cacheKey, $code, Carbon::now()->addMinutes($this->twoFactorExpiry));

        // In a real application, you would send this code to the user via SMS, email, etc.
        // For now, we'll just log it
        $this->loggingService->info("Two-factor code generated for user: {$user->username}", [
            'user_id' => $user->id,
            'username' => $user->username,
            'code' => $code, // Don't log this in production!
        ]);

        return $code;
    }

    /**
     * Enable two-factor authentication for a user.
     */
    public function enableTwoFactor(User $user): bool
    {
        $user->two_factor_enabled = true;
        $user->two_factor_secret = Str::random(40);

        $result = $user->save();

        if ($result) {
            $this->loggingService->info("Two-factor authentication enabled for user: {$user->username}", [
                'user_id' => $user->id,
                'username' => $user->username,
            ]);
        }

        return $result;
    }

    /**
     * Disable two-factor authentication for a user.
     */
    public function disableTwoFactor(User $user): bool
    {
        $user->two_factor_enabled = false;
        $user->two_factor_secret = null;

        $result = $user->save();

        if ($result) {
            $this->loggingService->info("Two-factor authentication disabled for user: {$user->username}", [
                'user_id' => $user->id,
                'username' => $user->username,
            ]);
        }

        return $result;
    }

    /**
     * Check if a user is locked out due to too many failed login attempts.
     */
    public function isLockedOut(string $username): bool
    {
        $cacheKey = "login_attempts_{$username}";
        $attempts = Cache::get($cacheKey, 0);

        return $attempts >= $this->maxAttempts;
    }

    /**
     * Increment the number of failed login attempts for a user.
     */
    protected function incrementFailedAttempts(string $username): int
    {
        $cacheKey = "login_attempts_{$username}";
        $attempts = Cache::get($cacheKey, 0) + 1;

        $duration = $attempts >= $this->maxAttempts ? $this->lockoutDuration : 60;

        Cache::put($cacheKey, $attempts, Carbon::now()->addMinutes($duration));

        return $attempts;
    }

    /**
     * Reset the number of failed login attempts for a user.
     */
    protected function resetFailedAttempts(string $username): void
    {
        $cacheKey = "login_attempts_{$username}";
        Cache::forget($cacheKey);
    }

    /**
     * Set the maximum number of failed login attempts before lockout.
     *
     * @return $this
     */
    public function setMaxAttempts(int $attempts): static
    {
        $this->maxAttempts = $attempts;

        return $this;
    }

    /**
     * Set the lockout duration in minutes.
     *
     * @return $this
     */
    public function setLockoutDuration(int $minutes): static
    {
        $this->lockoutDuration = $minutes;

        return $this;
    }

    /**
     * Set whether to use two-factor authentication.
     *
     * @return $this
     */
    public function setUse2FA(bool $use2FA): static
    {
        $this->use2FA = $use2FA;

        return $this;
    }

    /**
     * Set the two-factor authentication code expiry in minutes.
     *
     * @return $this
     */
    public function setTwoFactorExpiry(int $minutes): static
    {
        $this->twoFactorExpiry = $minutes;

        return $this;
    }
}
