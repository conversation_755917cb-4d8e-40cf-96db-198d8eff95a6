<?php

namespace App\Services;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class AuthorizationService
{
    /**
     * Cache duration in minutes.
     */
    protected int $cacheDuration = 60;

    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Cache service instance.
         */
        protected CacheService $cacheService,
        /**
         * Logging service instance.
         */
        protected LoggingService $loggingService
    )
    {
    }

    /**
     * Check if a user has a specific permission.
     */
    public function hasPermission(User $user, string $permission): bool
    {
        $permissions = $this->getUserPermissions($user);

        return in_array($permission, $permissions);
    }

    /**
     * Check if a user has any of the specified permissions.
     */
    public function hasAnyPermission(User $user, array $permissions): bool
    {
        $userPermissions = $this->getUserPermissions($user);

        foreach ($permissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a user has all of the specified permissions.
     */
    public function hasAllPermissions(User $user, array $permissions): bool
    {
        $userPermissions = $this->getUserPermissions($user);

        foreach ($permissions as $permission) {
            if (! in_array($permission, $userPermissions)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if a user has a specific role.
     */
    public function hasRole(User $user, string $role): bool
    {
        $roles = $this->getUserRoles($user);

        return in_array($role, $roles);
    }

    /**
     * Check if a user has any of the specified roles.
     */
    public function hasAnyRole(User $user, array $roles): bool
    {
        $userRoles = $this->getUserRoles($user);

        foreach ($roles as $role) {
            if (in_array($role, $userRoles)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a user has all of the specified roles.
     */
    public function hasAllRoles(User $user, array $roles): bool
    {
        $userRoles = $this->getUserRoles($user);

        foreach ($roles as $role) {
            if (! in_array($role, $userRoles)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all permissions for a user.
     */
    public function getUserPermissions(User $user): array
    {
        $cacheKey = $this->cacheService->modelKey('user_permissions', $user->id);

        return $this->cacheService->remember($cacheKey, function () use ($user): array {
            $permissions = [];

            // Get permissions from roles
            foreach ($user->roles as $role) {
                foreach ($role->permissions as $permission) {
                    $permissions[] = $permission->name;
                }
            }

            // Get direct permissions
            foreach ($user->permissions as $permission) {
                $permissions[] = $permission->name;
            }

            return array_unique($permissions);
        }, $this->cacheDuration);
    }

    /**
     * Get all roles for a user.
     */
    public function getUserRoles(User $user): array
    {
        $cacheKey = $this->cacheService->modelKey('user_roles', $user->id);

        return $this->cacheService->remember($cacheKey, fn() => $user->roles->pluck('name')->toArray(), $this->cacheDuration);
    }

    /**
     * Assign a role to a user.
     *
     * @param  string|Role  $role
     */
    public function assignRole(User $user, $role): bool
    {
        try {
            if (is_string($role)) {
                $role = Role::where('name', $role)->firstOrFail();
            }

            if (! $user->roles->contains($role->id)) {
                $user->roles()->attach($role->id);

                // Clear cache
                $this->clearUserCache($user);

                $this->loggingService->info("Role assigned to user: {$role->name}", [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'role_id' => $role->id,
                    'role_name' => $role->name,
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->loggingService->error("Error assigning role to user: {$e->getMessage()}", [
                'user_id' => $user->id,
                'username' => $user->username,
                'role' => is_string($role) ? $role : $role->name,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Remove a role from a user.
     *
     * @param  string|Role  $role
     */
    public function removeRole(User $user, $role): bool
    {
        try {
            if (is_string($role)) {
                $role = Role::where('name', $role)->firstOrFail();
            }

            if ($user->roles->contains($role->id)) {
                $user->roles()->detach($role->id);

                // Clear cache
                $this->clearUserCache($user);

                $this->loggingService->info("Role removed from user: {$role->name}", [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'role_id' => $role->id,
                    'role_name' => $role->name,
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->loggingService->error("Error removing role from user: {$e->getMessage()}", [
                'user_id' => $user->id,
                'username' => $user->username,
                'role' => is_string($role) ? $role : $role->name,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Assign a permission to a user.
     *
     * @param  string|Permission  $permission
     */
    public function assignPermission(User $user, $permission): bool
    {
        try {
            if (is_string($permission)) {
                $permission = Permission::where('name', $permission)->firstOrFail();
            }

            if (! $user->permissions->contains($permission->id)) {
                $user->permissions()->attach($permission->id);

                // Clear cache
                $this->clearUserCache($user);

                $this->loggingService->info("Permission assigned to user: {$permission->name}", [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'permission_id' => $permission->id,
                    'permission_name' => $permission->name,
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->loggingService->error("Error assigning permission to user: {$e->getMessage()}", [
                'user_id' => $user->id,
                'username' => $user->username,
                'permission' => is_string($permission) ? $permission : $permission->name,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Remove a permission from a user.
     *
     * @param  string|Permission  $permission
     */
    public function removePermission(User $user, $permission): bool
    {
        try {
            if (is_string($permission)) {
                $permission = Permission::where('name', $permission)->firstOrFail();
            }

            if ($user->permissions->contains($permission->id)) {
                $user->permissions()->detach($permission->id);

                // Clear cache
                $this->clearUserCache($user);

                $this->loggingService->info("Permission removed from user: {$permission->name}", [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'permission_id' => $permission->id,
                    'permission_name' => $permission->name,
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->loggingService->error("Error removing permission from user: {$e->getMessage()}", [
                'user_id' => $user->id,
                'username' => $user->username,
                'permission' => is_string($permission) ? $permission : $permission->name,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Create a new role.
     */
    public function createRole(string $name, string $description, array $permissions = []): ?Role
    {
        try {
            $role = Role::create([
                'name' => $name,
                'description' => $description,
            ]);

            if ($permissions !== []) {
                $permissionIds = Permission::whereIn('name', $permissions)->pluck('id')->toArray();
                $role->permissions()->attach($permissionIds);
            }

            $this->loggingService->info("Role created: {$name}", [
                'role_id' => $role->id,
                'role_name' => $role->name,
                'permissions' => $permissions,
            ]);

            return $role;
        } catch (\Exception $e) {
            $this->loggingService->error("Error creating role: {$e->getMessage()}", [
                'name' => $name,
                'description' => $description,
                'permissions' => $permissions,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Create a new permission.
     */
    public function createPermission(string $name, string $description): ?Permission
    {
        try {
            $permission = Permission::create([
                'name' => $name,
                'description' => $description,
            ]);

            $this->loggingService->info("Permission created: {$name}", [
                'permission_id' => $permission->id,
                'permission_name' => $permission->name,
            ]);

            return $permission;
        } catch (\Exception $e) {
            $this->loggingService->error("Error creating permission: {$e->getMessage()}", [
                'name' => $name,
                'description' => $description,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Clear cache for a user.
     */
    protected function clearUserCache(User $user): void
    {
        $this->cacheService->forget($this->cacheService->modelKey('user_permissions', $user->id));
        $this->cacheService->forget($this->cacheService->modelKey('user_roles', $user->id));
    }

    /**
     * Set the cache duration.
     *
     * @return $this
     */
    public function setCacheDuration(int $minutes): static
    {
        $this->cacheDuration = $minutes;

        return $this;
    }
}
