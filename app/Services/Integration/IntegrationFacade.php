<?php

namespace App\Services\Integration;

use App\Models\Campaign;
use App\Models\ElectoralEvent;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\PatrolGroup;
use App\Models\Person;

class IntegrationFacade
{
    /**
     * Create a new facade instance.
     */
    public function __construct(
        /**
         * Person integration service instance.
         */
        protected PersonIntegrationService $personIntegrationService,
        /**
         * Patrol integration service instance.
         */
        protected PatrolIntegrationService $patrolIntegrationService,
        /**
         * Electoral integration service instance.
         */
        protected ElectoralIntegrationService $electoralIntegrationService,
        /**
         * Tracking integration service instance.
         */
        protected TrackingIntegrationService $trackingIntegrationService
    )
    {
    }

    /**
     * Get integrated summary for a person across all modules.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPersonSummary(Person $person, bool $useCache = true): array
    {
        return $this->personIntegrationService->getPersonSummary($person, $useCache);
    }

    /**
     * Get statistics for a person across all modules.
     */
    public function getPersonStats(Person $person): array
    {
        return $this->personIntegrationService->getPersonStats($person);
    }

    /**
     * Get integrated data for a patrol group.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPatrolGroupIntegratedData(PatrolGroup $group, bool $useCache = true): array
    {
        return $this->patrolIntegrationService->getPatrolGroupIntegratedData($group, $useCache);
    }

    /**
     * Create a campaign for a patrol group.
     */
    public function createCampaignForPatrolGroup(int $groupId, array $data): ?Campaign
    {
        return $this->patrolIntegrationService->createCampaignForPatrolGroup($groupId, $data);
    }

    /**
     * Get integrated data for an electoral event.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getElectoralEventIntegratedData(ElectoralEvent $event, bool $useCache = true): array
    {
        return $this->electoralIntegrationService->getElectoralEventIntegratedData($event, $useCache);
    }

    /**
     * Get integrated data for an electoral voting center.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getVotingCenterIntegratedData(ElectoralVotingCenter $votingCenter, bool $useCache = true): array
    {
        return $this->electoralIntegrationService->getVotingCenterIntegratedData($votingCenter, $useCache);
    }

    /**
     * Get integrated data for an electoral mobilization.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getMobilizationIntegratedData(ElectoralMobilization $mobilization, bool $useCache = true): array
    {
        return $this->electoralIntegrationService->getMobilizationIntegratedData($mobilization, $useCache);
    }

    /**
     * Create a campaign for an electoral mobilization.
     */
    public function createCampaignForMobilization(ElectoralMobilization $mobilization, array $data): ?Campaign
    {
        return $this->electoralIntegrationService->createCampaignForMobilization($mobilization, $data);
    }

    /**
     * Get integrated tracking data for a person.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPersonTrackingData(Person $person, bool $useCache = true): array
    {
        return $this->trackingIntegrationService->getPersonTrackingData($person, $useCache);
    }

    /**
     * Get tracking statistics for a specific date range.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getTrackingStatsByDateRange(string $startDate, string $endDate, bool $useCache = true): array
    {
        return $this->trackingIntegrationService->getTrackingStatsByDateRange($startDate, $endDate, $useCache);
    }
}
