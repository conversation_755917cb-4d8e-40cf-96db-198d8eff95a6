<?php

namespace App\Services\Integration;

use App\Models\Campaign;
use App\Models\CampaignTarget;
use App\Models\PatrolGroup;
use App\Services\CacheService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PatrolIntegrationService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Cache service instance.
         */
        protected CacheService $cacheService
    )
    {
    }

    /**
     * Get integrated data for a patrol group.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPatrolGroupIntegratedData(PatrolGroup $group, bool $useCache = true): array
    {
        $cacheKey = "patrol_group_integrated_data_{$group->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get active members
        $activeMembers = $group->members()->where('status', 'active')->get();

        // Get inactive members
        $inactiveMembers = $group->members()->where('status', 'inactive')->get();

        // Get activities
        $activities = $group->activities()->orderBy('scheduled_date', 'desc')->get();

        // Calculate completion percentage
        $goalMembers = $group->goal_members > 0 ? $group->goal_members : 10;
        $completionPercentage = min(100, round(($activeMembers->count() / $goalMembers) * 100));

        // Calculate activity completion percentage
        $goalActivities = $group->goal_activities > 0 ? $group->goal_activities : 5;
        $completedActivities = $activities->where('status', 'completed')->count();
        $activityCompletionPercentage = min(100, round(($completedActivities / $goalActivities) * 100));

        // Get last activity
        $lastActivity = $activities->where('status', 'completed')->first();

        // Get next activity
        $nextActivity = $activities->where('status', 'planned')
            ->where('scheduled_date', '>=', now())
            ->sortBy('scheduled_date')
            ->first();

        $data = [
            'group' => $group->load(['leader', 'estate', 'municipality', 'parish']),
            'active_members' => $activeMembers,
            'inactive_members' => $inactiveMembers,
            'activities' => $activities,
            'completion_percentage' => $completionPercentage,
            'activity_completion_percentage' => $activityCompletionPercentage,
            'last_activity' => $lastActivity,
            'next_activity' => $nextActivity,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }

    /**
     * Create a campaign for a patrol group.
     */
    public function createCampaignForPatrolGroup(int $groupId, array $data): ?Campaign
    {
        try {
            $group = PatrolGroup::findOrFail($groupId);
            $leader = $group->leader;

            if (! $leader) {
                Log::error('Cannot create campaign for patrol group without a leader', ['group_id' => $groupId]);

                return null;
            }

            // Create the campaign
            $campaign = Campaign::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? 'Campaña para grupo de patrulla: '.$group->name,
                'type' => 'sms',
                'status' => 'scheduled',
                'message_template' => $data['message_template'],
                'scheduled_date' => $data['scheduled_date'],
                'created_by' => Auth::id(),
            ]);

            // Add all group members as targets
            $members = $group->members;
            foreach ($members as $member) {
                $target = new CampaignTarget([
                    'campaign_id' => $campaign->id,
                    'target_type' => \App\Models\Person::class,
                    'target_id' => $member->id,
                    'status' => 'pending',
                ]);
                $target->save();
            }

            return $campaign;
        } catch (\Exception $e) {
            Log::error('Error creating campaign for patrol group', [
                'group_id' => $groupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }
}
