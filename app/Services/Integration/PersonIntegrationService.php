<?php

namespace App\Services\Integration;

use App\Models\ElectoralEventAttendance;
use App\Models\ElectoralMobilizationDetail;
use App\Models\Person;
use App\Models\Tracking;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;

class PersonIntegrationService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Cache service instance.
         */
        protected CacheService $cacheService
    )
    {
    }

    /**
     * Get integrated summary for a person across all modules.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPersonSummary(Person $person, bool $useCache = true): array
    {
        $cacheKey = "person_summary_{$person->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $summary = [
            'person' => $person->load(['estate', 'municipality', 'parish', 'responsible']),
            'electoral_events' => $this->getPersonElectoralEvents($person),
            'electoral_mobilizations' => $this->getPersonElectoralMobilizations($person),
            'patrol_groups' => $this->getPersonPatrolGroups($person),
            'trackings' => $this->getPersonTrackings($person),
            'stats' => $this->getPersonStats($person),
        ];

        if ($useCache) {
            // Guardar en caché por 30 minutos
            Cache::put($cacheKey, $summary, now()->addMinutes(30));
        }

        return $summary;
    }

    /**
     * Get electoral events for a person.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getPersonElectoralEvents(Person $person)
    {
        return $person->electoralEventAttendances()
            ->with(['event' => function ($query): void {
                $query->with(['estate', 'municipality', 'parish']);
            }])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get electoral mobilizations for a person.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getPersonElectoralMobilizations(Person $person)
    {
        return $person->electoralMobilizationDetails()
            ->with(['mobilization' => function ($query): void {
                $query->with(['votingCenter', 'coordinator', 'estate', 'municipality', 'parish']);
            }])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get patrol groups for a person.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getPersonPatrolGroups(Person $person)
    {
        return $person->patrolGroups()
            ->with(['leader', 'estate', 'municipality', 'parish'])
            ->get();
    }

    /**
     * Get tracking records for a person.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getPersonTrackings(Person $person)
    {
        return Tracking::where('person_id', $person->id)
            ->with(['user', 'votingCenter'])
            ->orderBy('tracking_date', 'desc')
            ->get();
    }

    /**
     * Get statistics for a person across all modules.
     */
    public function getPersonStats(Person $person): array
    {
        return [
            'total_events_attended' => ElectoralEventAttendance::where('person_id', $person->id)->count(),
            'total_mobilizations' => ElectoralMobilizationDetail::where('person_id', $person->id)->count(),
            'total_patrol_activities' => $this->getPersonPatrolActivitiesCount($person),
            'total_trackings' => Tracking::where('person_id', $person->id)->count(),
            'last_activity_date' => $this->getLastActivityDate($person),
            'participation_score' => $this->calculateParticipationScore($person),
        ];
    }

    /**
     * Get the count of patrol activities for a person.
     */
    protected function getPersonPatrolActivitiesCount(Person $person): int
    {
        $count = 0;

        // Count activities where the person is a member of the patrol group
        foreach ($person->patrolGroups as $group) {
            $count += $group->activities()->count();
        }

        return $count;
    }

    /**
     * Get the last activity date for a person across all modules.
     */
    protected function getLastActivityDate(Person $person): ?string
    {
        $dates = [];

        // Last electoral event attendance
        $lastEventAttendance = $person->electoralEventAttendances()
            ->orderBy('created_at', 'desc')
            ->first();

        if ($lastEventAttendance) {
            $dates[] = $lastEventAttendance->created_at;
        }

        // Last electoral mobilization
        $lastMobilization = $person->electoralMobilizationDetails()
            ->orderBy('created_at', 'desc')
            ->first();

        if ($lastMobilization) {
            $dates[] = $lastMobilization->created_at;
        }

        // Last tracking
        $lastTracking = Tracking::where('person_id', $person->id)
            ->orderBy('tracking_date', 'desc')
            ->first();

        if ($lastTracking) {
            $dates[] = $lastTracking->tracking_date;
        }

        if ($dates === []) {
            return null;
        }

        // Sort dates in descending order and return the most recent
        usort($dates, fn($a, $b): int => $b <=> $a);

        return $dates[0]->format('Y-m-d H:i:s');
    }

    /**
     * Calculate a participation score for a person based on their activity across all modules.
     */
    protected function calculateParticipationScore(Person $person): int
    {
        $score = 0;

        // Points for electoral event attendance
        $score += $person->electoralEventAttendances()->count() * 5;

        // Points for electoral mobilization
        $score += $person->electoralMobilizationDetails()->count() * 3;

        // Points for being a 1x10 leader
        if ($person->is_1x10) {
            $score += 10;

            // Additional points based on number of people they're responsible for
            $score += $person->responsibleFor()->count() * 2;
        }

        // Points for patrol group membership
        $score += $person->patrolGroups()->count() * 5;

        // Points for tracking records
        $score += Tracking::where('person_id', $person->id)->count() * 2;

        return min(100, $score); // Cap at 100
    }
}
