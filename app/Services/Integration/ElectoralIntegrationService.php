<?php

namespace App\Services\Integration;

use App\Models\ElectoralEvent;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralVotingCenter;
use App\Models\Person;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;

class ElectoralIntegrationService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Cache service instance.
         */
        protected CacheService $cacheService
    )
    {
    }

    /**
     * Get integrated data for an electoral event.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getElectoralEventIntegratedData(ElectoralEvent $event, bool $useCache = true): array
    {
        $cacheKey = "electoral_event_integrated_data_{$event->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get attendances with person data
        $attendances = $event->attendances()
            ->with('person')
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculate attendance statistics
        $totalAttendees = $attendances->count();
        $confirmedAttendees = $attendances->where('status', 'confirmed')->count();
        $attendedAttendees = $attendances->where('status', 'attended')->count();
        $absentAttendees = $attendances->where('status', 'absent')->count();

        // Calculate attendance percentage
        $attendancePercentage = $totalAttendees > 0
            ? round(($attendedAttendees / $totalAttendees) * 100)
            : 0;

        // Get 1x10 leaders attending the event
        $leaders1x10 = $attendances
            ->where('status', 'attended')
            ->pluck('person')
            ->filter(fn($person): bool => $person && $person->is_1x10);

        $data = [
            'event' => $event->load(['estate', 'municipality', 'parish', 'organizer']),
            'attendances' => $attendances,
            'stats' => [
                'total_attendees' => $totalAttendees,
                'confirmed_attendees' => $confirmedAttendees,
                'attended_attendees' => $attendedAttendees,
                'absent_attendees' => $absentAttendees,
                'attendance_percentage' => $attendancePercentage,
                'leaders_1x10_count' => $leaders1x10->count(),
            ],
            'leaders_1x10' => $leaders1x10,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }

    /**
     * Get integrated data for an electoral voting center.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getVotingCenterIntegratedData(ElectoralVotingCenter $votingCenter, bool $useCache = true): array
    {
        $cacheKey = "voting_center_integrated_data_{$votingCenter->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get mobilizations for this voting center
        $mobilizations = $votingCenter->mobilizations()
            ->with(['coordinator', 'details.person'])
            ->orderBy('mobilization_date', 'desc')
            ->get();

        // Calculate mobilization statistics
        $totalMobilizations = $mobilizations->count();
        $completedMobilizations = $mobilizations->where('status', 'completed')->count();
        $inProgressMobilizations = $mobilizations->where('status', 'in_progress')->count();
        $scheduledMobilizations = $mobilizations->where('status', 'scheduled')->count();

        // Get all people mobilized to this voting center
        $mobilizedPeople = collect();
        foreach ($mobilizations as $mobilization) {
            $mobilizedPeople = $mobilizedPeople->merge(
                $mobilization->details->pluck('person')
            );
        }
        $mobilizedPeople = $mobilizedPeople->unique('id');

        // Get 1x10 leaders mobilized to this voting center
        $leaders1x10 = $mobilizedPeople->filter(fn($person): bool => $person && $person->is_1x10);

        $data = [
            'voting_center' => $votingCenter->load(['estate', 'municipality', 'parish']),
            'mobilizations' => $mobilizations,
            'stats' => [
                'total_mobilizations' => $totalMobilizations,
                'completed_mobilizations' => $completedMobilizations,
                'in_progress_mobilizations' => $inProgressMobilizations,
                'scheduled_mobilizations' => $scheduledMobilizations,
                'total_mobilized_people' => $mobilizedPeople->count(),
                'leaders_1x10_count' => $leaders1x10->count(),
            ],
            'mobilized_people' => $mobilizedPeople,
            'leaders_1x10' => $leaders1x10,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }

    /**
     * Get integrated data for an electoral mobilization.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getMobilizationIntegratedData(ElectoralMobilization $mobilization, bool $useCache = true): array
    {
        $cacheKey = "mobilization_integrated_data_{$mobilization->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get details with person data
        $details = $mobilization->details()
            ->with('person')
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculate mobilization statistics
        $totalPeople = $details->count();
        $contactedPeople = $details->whereNotNull('contact_time')->count();
        $confirmedPeople = $details->whereNotNull('confirmation_time')->count();
        $mobilizedPeople = $details->whereNotNull('mobilization_time')->count();
        $votedPeople = $details->whereNotNull('voting_time')->count();

        // Calculate mobilization percentage
        $mobilizationPercentage = $totalPeople > 0
            ? round(($mobilizedPeople / $totalPeople) * 100)
            : 0;

        // Calculate voting percentage
        $votingPercentage = $totalPeople > 0
            ? round(($votedPeople / $totalPeople) * 100)
            : 0;

        // Get 1x10 leaders in this mobilization
        $leaders1x10 = $details
            ->pluck('person')
            ->filter(fn($person): bool => $person && $person->is_1x10);

        $data = [
            'mobilization' => $mobilization->load(['votingCenter', 'coordinator', 'estate', 'municipality', 'parish']),
            'details' => $details,
            'stats' => [
                'total_people' => $totalPeople,
                'contacted_people' => $contactedPeople,
                'confirmed_people' => $confirmedPeople,
                'mobilized_people' => $mobilizedPeople,
                'voted_people' => $votedPeople,
                'mobilization_percentage' => $mobilizationPercentage,
                'voting_percentage' => $votingPercentage,
                'leaders_1x10_count' => $leaders1x10->count(),
            ],
            'leaders_1x10' => $leaders1x10,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }

    /**
     * Create a campaign for an electoral mobilization.
     */
    public function createCampaignForMobilization(ElectoralMobilization $mobilization, array $data): ?\App\Models\Campaign
    {
        try {
            // Create the campaign
            $campaign = \App\Models\Campaign::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? 'Campaña para movilización electoral: '.$mobilization->name,
                'type' => 'sms',
                'status' => 'scheduled',
                'message_template' => $data['message_template'],
                'scheduled_date' => $data['scheduled_date'],
                'created_by' => \Illuminate\Support\Facades\Auth::id(),
            ]);

            // Add all mobilization people as targets
            $details = $mobilization->details;
            foreach ($details as $detail) {
                $target = new \App\Models\CampaignTarget([
                    'campaign_id' => $campaign->id,
                    'target_type' => \App\Models\Person::class,
                    'target_id' => $detail->person_id,
                    'status' => 'pending',
                ]);
                $target->save();
            }

            return $campaign;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error creating campaign for electoral mobilization', [
                'mobilization_id' => $mobilization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }
}
