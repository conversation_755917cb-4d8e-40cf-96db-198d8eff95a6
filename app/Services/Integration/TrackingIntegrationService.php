<?php

namespace App\Services\Integration;

use App\Models\Person;
use App\Models\Tracking;
use App\Services\CacheService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class TrackingIntegrationService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Cache service instance.
         */
        protected CacheService $cacheService
    )
    {
    }

    /**
     * Get integrated tracking data for a person.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPersonTrackingData(Person $person, bool $useCache = true): array
    {
        $cacheKey = "person_tracking_data_{$person->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get tracking records for this person
        $trackings = Tracking::forPerson($person->id)
            ->with(['user', 'trackable', 'votingCenter'])
            ->orderBy('tracking_date', 'desc')
            ->get();

        // Calculate tracking statistics by type
        $trackingsByType = $trackings->groupBy('tracking_type');
        $trackingStats = [];

        foreach ($trackingsByType as $type => $records) {
            $trackingStats[$type] = [
                'count' => $records->count(),
                'last_date' => $records->first()->tracking_date,
                'completed' => $records->where('status', 'completed')->count(),
                'pending' => $records->where('status', 'pending')->count(),
            ];
        }

        // Get pending follow-ups
        $pendingFollowUps = $trackings
            ->filter(fn($tracking): bool => $tracking->follow_up_date &&
                   $tracking->follow_up_date >= now() &&
                   $tracking->status != 'completed')
            ->sortBy('follow_up_date');

        $data = [
            'person' => $person->load(['estate', 'municipality', 'parish']),
            'trackings' => $trackings,
            'stats' => [
                'total_trackings' => $trackings->count(),
                'completed_trackings' => $trackings->where('status', 'completed')->count(),
                'pending_trackings' => $trackings->where('status', 'pending')->count(),
                'pending_follow_ups' => $pendingFollowUps->count(),
                'tracking_by_type' => $trackingStats,
            ],
            'pending_follow_ups' => $pendingFollowUps,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }

    /**
     * Get tracking statistics for a specific date range.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getTrackingStatsByDateRange(string $startDate, string $endDate, bool $useCache = true): array
    {
        $cacheKey = "tracking_stats_{$startDate}_{$endDate}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Convert to Carbon instances
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        // Get tracking records for this date range
        $trackings = Tracking::whereBetween('tracking_date', [$start, $end])
            ->with(['user', 'person'])
            ->get();

        // Group by date
        $trackingsByDate = $trackings->groupBy(fn($tracking) => $tracking->tracking_date->format('Y-m-d'));

        // Group by type
        $trackingsByType = $trackings->groupBy('tracking_type');

        // Group by user
        $trackingsByUser = $trackings->groupBy('user_id');
        $userStats = [];

        foreach ($trackingsByUser as $userId => $records) {
            $user = $records->first()->user;
            if ($user) {
                $userStats[$userId] = [
                    'user' => $user->only(['id', 'name']),
                    'count' => $records->count(),
                ];
            }
        }

        // Calculate daily stats
        $dailyStats = [];
        $currentDate = clone $start;

        while ($currentDate <= $end) {
            $dateStr = $currentDate->format('Y-m-d');
            $dailyStats[$dateStr] = [
                'date' => $dateStr,
                'count' => $trackingsByDate->get($dateStr, collect())->count(),
            ];
            $currentDate->addDay();
        }

        $data = [
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
            'stats' => [
                'total_trackings' => $trackings->count(),
                'completed_trackings' => $trackings->where('status', 'completed')->count(),
                'pending_trackings' => $trackings->where('status', 'pending')->count(),
                'tracking_by_type' => $trackingsByType->map->count()->toArray(),
                'tracking_by_user' => $userStats,
            ],
            'daily_stats' => $dailyStats,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }
}
