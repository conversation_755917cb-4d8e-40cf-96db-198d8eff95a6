<?php

namespace App\Services;

use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;

class QueueService
{
    /**
     * Default queue name
     */
    protected string $defaultQueue = 'default';

    /**
     * Available queue connections
     */
    protected array $connections = [
        'sync', 'database', 'redis', 'beanstalkd', 'sqs',
    ];

    /**
     * Create a new service instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Dispatch a job to the queue.
     *
     * @param  mixed  $job
     * @return mixed
     */
    public function dispatch($job, ?string $queue = null)
    {
        $queue ??= $this->defaultQueue;

        try {
            return Queue::pushOn($queue, $job);
        } catch (\Exception $e) {
            Log::error("Queue dispatch error: {$e->getMessage()}", [
                'job' => $job::class,
                'queue' => $queue,
                'exception' => $e,
            ]);

            // Fallback to sync driver if queue fails
            return Queue::connection('sync')->push($job);
        }
    }

    /**
     * Dispatch a job to the queue with delay.
     *
     * @param  mixed  $job
     * @param  int  $delay  Delay in seconds
     * @return mixed
     */
    public function dispatchWithDelay($job, int $delay, ?string $queue = null)
    {
        $queue ??= $this->defaultQueue;

        try {
            return Queue::laterOn($queue, $delay, $job);
        } catch (\Exception $e) {
            Log::error("Queue dispatch with delay error: {$e->getMessage()}", [
                'job' => $job::class,
                'queue' => $queue,
                'delay' => $delay,
                'exception' => $e,
            ]);

            // Fallback to sync driver if queue fails
            return Queue::connection('sync')->push($job);
        }
    }

    /**
     * Dispatch a batch of jobs.
     *
     * @return \Illuminate\Bus\Batch
     */
    public function dispatchBatch(array $jobs, ?callable $then = null, ?callable $catch = null, ?callable $finally = null)
    {
        $batch = Bus::batch($jobs);

        if ($then) {
            $batch->then($then);
        }

        if ($catch) {
            $batch->catch($catch);
        }

        if ($finally) {
            $batch->finally($finally);
        }

        return $batch->dispatch();
    }

    /**
     * Get queue statistics.
     */
    public function getQueueStats(): array
    {
        $stats = [];

        try {
            // For Redis queues
            if (config('queue.default') === 'redis') {
                $redis = Redis::connection();
                $queues = $redis->keys('queues:*');

                foreach ($queues as $queue) {
                    $queueName = str_replace('queues:', '', $queue);
                    $stats[$queueName] = [
                        'size' => $redis->llen($queue),
                        'processed' => (int) $redis->get("stats:processed:{$queueName}") ?? 0,
                        'failed' => (int) $redis->get("stats:failed:{$queueName}") ?? 0,
                    ];
                }
            }

            // For database queues
            if (config('queue.default') === 'database') {
                $dbStats = \DB::table('jobs')
                    ->select('queue', \DB::raw('count(*) as size'))
                    ->groupBy('queue')
                    ->get();

                foreach ($dbStats as $stat) {
                    $stats[$stat->queue] = [
                        'size' => $stat->size,
                        'processed' => 0, // Not tracked in database driver
                        'failed' => 0, // Not tracked in database driver
                    ];
                }

                // Failed jobs
                $failedCount = \DB::table('failed_jobs')->count();
                $stats['failed'] = [
                    'size' => $failedCount,
                ];
            }
        } catch (\Exception $e) {
            Log::error("Error getting queue stats: {$e->getMessage()}", [
                'exception' => $e,
            ]);
        }

        return $stats;
    }

    /**
     * Clear a specific queue.
     */
    public function clearQueue(string $queue): bool
    {
        try {
            if (config('queue.default') === 'redis') {
                Redis::connection()->del("queues:{$queue}");

                return true;
            }

            if (config('queue.default') === 'database') {
                \DB::table('jobs')->where('queue', $queue)->delete();

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error("Error clearing queue: {$e->getMessage()}", [
                'queue' => $queue,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Retry failed jobs.
     *
     * @param  int|null  $id  Specific job ID to retry, or null for all
     * @return int Number of jobs retried
     */
    public function retryFailedJobs(?int $id = null): int
    {
        try {
            if ($id !== null && $id !== 0) {
                \Artisan::call('queue:retry', ['id' => [$id]]);

                return 1;
            }
            \Artisan::call('queue:retry all');
            $output = \Artisan::output();
            // Extract number of retried jobs from output
            if (preg_match('/([0-9]+) jobs/i', $output, $matches)) {
                return (int) $matches[1];
            }
            return 0;
        } catch (\Exception $e) {
            Log::error("Error retrying failed jobs: {$e->getMessage()}", [
                'id' => $id,
                'exception' => $e,
            ]);

            return 0;
        }
    }

    /**
     * Set the default queue.
     *
     * @return $this
     */
    public function setDefaultQueue(string $queue): static
    {
        $this->defaultQueue = $queue;

        return $this;
    }
}
