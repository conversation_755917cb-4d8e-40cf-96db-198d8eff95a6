<?php

namespace App\Services;

use App\Models\ElectoralEvent;
use App\Models\ElectoralMobilization;
use App\Models\Estate;
use App\Models\Municipality;
use App\Models\PatrolGroup;
use App\Models\Person;
use App\Models\Tracking;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class DashboardService
{
    /**
     * Get dashboard statistics.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getDashboardStats(bool $useCache = true): array
    {
        $cacheKey = 'dashboard_stats';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $stats = [
            'total_persons' => Person::count(),
            'persons_trend' => $this->calculateTrend('people', 'created_at'),
            'total_1x10_leaders' => Person::where('is_1x10', true)->count(),
            'leaders_trend' => $this->calculateTrend('people', 'created_at', ['is_1x10' => true]),
            'total_groups' => PatrolGroup::count(),
            'groups_trend' => $this->calculateTrend('patrol_groups', 'created_at'),
            'total_events' => ElectoralEvent::count(),
            'events_trend' => $this->calculateTrend('electoral_events', 'created_at'),
            'total_mobilizations' => ElectoralMobilization::count(),
            'mobilizations_trend' => $this->calculateTrend('electoral_mobilizations', 'created_at'),
            'total_trackings' => Tracking::count(),
            'trackings_trend' => $this->calculateTrend('trackings', 'created_at'),
            'total_users' => User::count(),
            'users_trend' => $this->calculateTrend('users', 'created_at'),
        ];

        if ($useCache) {
            Cache::put($cacheKey, $stats, now()->addHour());
        }

        return $stats;
    }

    /**
     * Get persons statistics by location.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPersonsByLocation(bool $useCache = true): array
    {
        $cacheKey = 'persons_by_location';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $estateData = Estate::withCount('persons')
            ->orderBy('persons_count', 'desc')
            ->limit(10)
            ->get()
            ->map(fn($estate): array => [
                'name' => $estate->name,
                'count' => $estate->persons_count,
            ]);

        $municipalityData = Municipality::withCount('persons')
            ->orderBy('persons_count', 'desc')
            ->limit(10)
            ->get()
            ->map(fn($municipality): array => [
                'name' => $municipality->name,
                'count' => $municipality->persons_count,
            ]);

        $data = [
            'estates' => $estateData,
            'municipalities' => $municipalityData,
        ];

        if ($useCache) {
            Cache::put($cacheKey, $data, now()->addHour());
        }

        return $data;
    }

    /**
     * Get persons statistics by role.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPersonsByRole(bool $useCache = true): array
    {
        $cacheKey = 'persons_by_role';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $data = DB::table('people')
            ->select('role', DB::raw('count(*) as count'))
            ->groupBy('role')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->role ?: 'Sin rol',
                'count' => $item->count,
            ]);

        if ($useCache) {
            Cache::put($cacheKey, $data, now()->addHour());
        }

        return $data;
    }

    /**
     * Get electoral events statistics.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getElectoralEventsStats(bool $useCache = true): array
    {
        $cacheKey = 'electoral_events_stats';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $byStatus = DB::table('electoral_events')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->status ?: 'Sin estado',
                'count' => $item->count,
            ]);

        $byType = DB::table('electoral_events')
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->type ?: 'Sin tipo',
                'count' => $item->count,
            ]);

        $byMonth = DB::table('electoral_events')
            ->select(DB::raw('MONTH(start_date) as month'), DB::raw('count(*) as count'))
            ->whereYear('start_date', date('Y'))
            ->groupBy(DB::raw('MONTH(start_date)'))
            ->orderBy('month')
            ->get()
            ->map(fn($item): array => [
                'name' => Carbon::create()->month($item->month)->format('F'),
                'count' => $item->count,
            ]);

        $data = [
            'by_status' => $byStatus,
            'by_type' => $byType,
            'by_month' => $byMonth,
        ];

        if ($useCache) {
            Cache::put($cacheKey, $data, now()->addHour());
        }

        return $data;
    }

    /**
     * Get tracking statistics.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getTrackingStats(bool $useCache = true): array
    {
        $cacheKey = 'tracking_stats';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $byType = DB::table('trackings')
            ->select('tracking_type', DB::raw('count(*) as count'))
            ->groupBy('tracking_type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->tracking_type ?: 'Sin tipo',
                'count' => $item->count,
            ]);

        $byStatus = DB::table('trackings')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->status ?: 'Sin estado',
                'count' => $item->count,
            ]);

        $byDay = DB::table('trackings')
            ->select(DB::raw('DATE(tracking_date) as day'), DB::raw('count(*) as count'))
            ->where('tracking_date', '>=', now()->subDays(30))
            ->groupBy(DB::raw('DATE(tracking_date)'))
            ->orderBy('day')
            ->get()
            ->map(fn($item): array => [
                'name' => Carbon::parse($item->day)->format('d/m'),
                'count' => $item->count,
            ]);

        $data = [
            'by_type' => $byType,
            'by_status' => $byStatus,
            'by_day' => $byDay,
        ];

        if ($useCache) {
            Cache::put($cacheKey, $data, now()->addHour());
        }

        return $data;
    }

    /**
     * Get patrol statistics.
     *
     * @param  bool  $useCache  Whether to use cached data
     */
    public function getPatrolStats(bool $useCache = true): array
    {
        $cacheKey = 'patrol_stats';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $byStatus = DB::table('patrol_groups')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->status ?: 'Sin estado',
                'count' => $item->count,
            ]);

        $byType = DB::table('patrol_groups')
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(fn($item): array => [
                'name' => $item->type ?: 'Sin tipo',
                'count' => $item->count,
            ]);

        $byLocation = Estate::withCount('patrolGroups')
            ->orderBy('patrol_groups_count', 'desc')
            ->limit(10)
            ->get()
            ->map(fn($estate): array => [
                'name' => $estate->name,
                'count' => $estate->patrol_groups_count,
            ]);

        $data = [
            'by_status' => $byStatus,
            'by_type' => $byType,
            'by_location' => $byLocation,
        ];

        if ($useCache) {
            Cache::put($cacheKey, $data, now()->addHour());
        }

        return $data;
    }

    /**
     * Calculate trend percentage for a table.
     */
    protected function calculateTrend(string $table, string $dateColumn, array $conditions = []): float
    {
        $now = Carbon::now();
        $lastMonth = $now->copy()->subMonth();
        $now->copy()->subMonths(2);

        $queryCurrentMonth = DB::table($table)->whereMonth($dateColumn, $now->month)->whereYear($dateColumn, $now->year);
        $queryLastMonth = DB::table($table)->whereMonth($dateColumn, $lastMonth->month)->whereYear($dateColumn, $lastMonth->year);

        // Apply additional conditions
        foreach ($conditions as $column => $value) {
            $queryCurrentMonth->where($column, $value);
            $queryLastMonth->where($column, $value);
        }

        $currentMonthCount = $queryCurrentMonth->count();
        $lastMonthCount = $queryLastMonth->count();

        if ($lastMonthCount == 0) {
            return $currentMonthCount > 0 ? 100 : 0;
        }

        return round((($currentMonthCount - $lastMonthCount) / $lastMonthCount) * 100, 2);
    }
}
