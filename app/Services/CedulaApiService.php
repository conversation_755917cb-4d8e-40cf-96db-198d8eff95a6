<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CedulaApiService
{
    protected $baseUrl = 'https://api.cedula.com.ve/';
    protected $appId;
    protected $token;
    
    public function __construct()
    {
        $this->appId = config('services.cedula.app_id');
        $this->token = config('services.cedula.token');
    }
    
    /**
     * Busca información de una persona por su número de cédula.
     *
     * @param string $cedula
     * @return array|null
     */
    public function getPersonByCedula($cedula)
    {
        // Verificar si los datos están en caché
        $cacheKey = "cedula_api_{$cedula}";
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "api/v1/search", [
                'app_id' => $this->appId,
                'token' => $this->token,
                'cedula' => $cedula,
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                
                // Guardar en caché por 1 día
                Cache::put($cacheKey, $data, 60 * 60 * 24);
                
                return $data;
            }
            
            Log::error('Error al consultar API de cédula', [
                'cedula' => $cedula,
                'status' => $response->status(),
                'response' => $response->body(),
            ]);
            
            return null;
        } catch (\Exception $e) {
            Log::error('Excepción al consultar API de cédula', [
                'cedula' => $cedula,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }
    
    /**
     * Verifica si una cédula existe en el registro nacional.
     *
     * @param string $cedula
     */
    public function validateCedula($cedula): bool
    {
        $result = $this->getPersonByCedula($cedula);
        
        return $result && isset($result['status']) && $result['status'] === 'success';
    }
    
    /**
     * Obtiene el nombre completo de una persona por su cédula.
     *
     * @param string $cedula
     */
    public function getFullName($cedula): ?string
    {
        $result = $this->getPersonByCedula($cedula);
        
        if ($result && isset($result['status']) && $result['status'] === 'success' && isset($result['data'])) {
            $data = $result['data'];
            return $data['nombres'] . ' ' . $data['apellidos'];
        }
        
        return null;
    }
    
    /**
     * Obtiene datos completos de una persona por su cédula.
     *
     * @param string $cedula
     * @return array|null
     */
    public function getPersonData($cedula)
    {
        $result = $this->getPersonByCedula($cedula);
        
        if ($result && isset($result['status']) && $result['status'] === 'success' && isset($result['data'])) {
            return $result['data'];
        }
        
        return null;
    }
}
