<?php

namespace App\Services;

class CedulaService
{
    protected $appId;

    protected $token;

    /**
     * Create a new service instance.
     */
    public function __construct()
    {
        $this->appId = config('cedula.app_id');
        $this->token = config('cedula.token');
    }

    /**
     * Get cURL data from a URL.
     *
     * @param  string  $url
     * @return string
     */
    protected function getCurlData($url): bool|string
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        $curlData = curl_exec($curl);
        curl_close($curl);

        return $curlData;
    }

    /**
     * Get information for a Venezuelan ID (cédula).
     *
     * @param  int  $cedula
     * @param  bool  $return_raw
     * @return mixed
     */
    public function getCI($cedula, $return_raw = false)
    {
        $res = $this->getCurlData('https://api.cedula.com.ve/api/v1?app_id='.$this->appId.'&token='.$this->token.'&cedula='.(int) $cedula);

        if ($return_raw) {
            return strlen($res) > 3 ? $res : false;
        }

        $res = json_decode($res, true);

        return isset($res['data']) && $res['data'] ? $res['data'] : $res['error_str'];
    }

    /**
     * Perform a test query with the given ID.
     *
     * @param  int  $cedula
     */
    public function testQuery($cedula = 0): string
    {
        $consulta = $this->getCI($cedula);

        if (is_array($consulta)) {
            return print_r($consulta, true);
        }
        return 'Ocurrio un error en la consulta: '.$consulta;
    }
}
