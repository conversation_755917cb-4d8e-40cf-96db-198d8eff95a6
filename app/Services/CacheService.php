<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class CacheService
{
    /**
     * Default cache duration in minutes
     */
    protected int $defaultDuration = 60;

    /**
     * Cache store to use
     */
    protected string $store;

    /**
     * Cache prefix
     */
    protected string $prefix;

    /**
     * Whether to track cache keys
     */
    protected bool $trackKeys = true;

    /**
     * Create a new service instance.
     */
    public function __construct()
    {
        $this->store = config('cache.default');
        $this->prefix = config('cache.prefix', 'laravel_cache');
    }

    /**
     * Get an item from the cache, or store it if it doesn't exist.
     *
     * @param  int|null  $duration  Duration in minutes
     * @param  string|null  $store  Cache store to use
     * @return mixed
     */
    public function remember(string $key, \Closure $callback, ?int $duration = null, ?string $store = null)
    {
        $duration ??= $this->defaultDuration;
        $store ??= $this->store;

        try {
            return Cache::store($store)->remember($key, Carbon::now()->addMinutes($duration), $callback);
        } catch (\Exception $e) {
            Log::error("Cache remember error: {$e->getMessage()}", [
                'key' => $key,
                'duration' => $duration,
                'store' => $store,
                'exception' => $e,
            ]);

            // If cache fails, execute the callback directly
            return $callback();
        }
    }

    /**
     * Generate a cache key for a model.
     *
     * @param  mixed  $id
     */
    public function modelKey(string $type, $id, ?string $suffix = null): string
    {
        $key = "{$type}_{$id}";

        if ($suffix !== null && $suffix !== '' && $suffix !== '0') {
            $key .= "_{$suffix}";
        }

        return $key . ('_' . config('app.cache_version', 1));
    }

    /**
     * Generate a cache key for a query with parameters.
     */
    public function queryKey(string $base, array $params = []): string
    {
        if ($params === []) {
            return $base.'_'.config('app.cache_version', 1);
        }

        // Sort params to ensure consistent cache keys
        ksort($params);

        // Convert params to a string
        $paramStr = md5(json_encode($params));

        return "{$base}_{$paramStr}_".config('app.cache_version', 1);
    }

    /**
     * Store an item in the cache.
     *
     * @param  mixed  $value
     * @param  int|null  $duration  Duration in minutes
     * @param  string|null  $store  Cache store to use
     * @return bool
     */
    public function put(string $key, $value, ?int $duration = null, ?string $store = null)
    {
        $duration ??= $this->defaultDuration;
        $store ??= $this->store;

        try {
            $result = Cache::store($store)->put($key, $value, Carbon::now()->addMinutes($duration));

            if ($this->trackKeys) {
                $this->trackCacheKey($key, $duration);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Cache put error: {$e->getMessage()}", [
                'key' => $key,
                'duration' => $duration,
                'store' => $store,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Retrieve an item from the cache.
     *
     * @param  mixed  $default
     * @param  string|null  $store  Cache store to use
     * @return mixed
     */
    public function get(string $key, $default = null, ?string $store = null)
    {
        $store ??= $this->store;

        try {
            return Cache::store($store)->get($key, $default);
        } catch (\Exception $e) {
            Log::error("Cache get error: {$e->getMessage()}", [
                'key' => $key,
                'store' => $store,
                'exception' => $e,
            ]);

            return $default;
        }
    }

    /**
     * Check if an item exists in the cache.
     *
     * @param  string|null  $store  Cache store to use
     */
    public function has(string $key, ?string $store = null): bool
    {
        $store ??= $this->store;

        try {
            return Cache::store($store)->has($key);
        } catch (\Exception $e) {
            Log::error("Cache has error: {$e->getMessage()}", [
                'key' => $key,
                'store' => $store,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Remove an item from the cache.
     *
     * @param  string|null  $store  Cache store to use
     * @return bool
     */
    public function forget(string $key, ?string $store = null)
    {
        $store ??= $this->store;

        try {
            $result = Cache::store($store)->forget($key);

            if ($this->trackKeys) {
                $this->untrackCacheKey($key);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Cache forget error: {$e->getMessage()}", [
                'key' => $key,
                'store' => $store,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Clear all cache.
     *
     * @param  string|null  $store  Cache store to use
     * @return bool
     */
    public function flush(?string $store = null)
    {
        $store ??= $this->store;

        try {
            return Cache::store($store)->flush();
        } catch (\Exception $e) {
            Log::error("Cache flush error: {$e->getMessage()}", [
                'store' => $store,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Clear cache for a specific model.
     *
     * @param  string|null  $store  Cache store to use
     * @return int Number of keys removed
     */
    public function flushModel(string $modelName, ?string $store = null): int
    {
        $store ??= $this->store;
        $pattern = "model_{$modelName}_*";
        $count = 0;

        try {
            $keys = $this->getTrackedKeys();
            $keysToRemove = [];

            foreach ($keys as $key) {
                if (Str::is($pattern, $key)) {
                    Cache::store($store)->forget($key);
                    $keysToRemove[] = $key;
                    $count++;
                }
            }

            if ($keysToRemove !== []) {
                $this->untrackCacheKeys($keysToRemove);
            }

            return $count;
        } catch (\Exception $e) {
            Log::error("Cache flushModel error: {$e->getMessage()}", [
                'modelName' => $modelName,
                'store' => $store,
                'exception' => $e,
            ]);

            return 0;
        }
    }

    /**
     * Clear cache for a specific tag.
     *
     * @param  string|array  $tags
     * @param  string|null  $store  Cache store to use
     */
    public function flushTag($tags, ?string $store = null): bool
    {
        $store ??= $this->store;

        try {
            if (method_exists(Cache::store($store), 'tags')) {
                Cache::store($store)->tags($tags)->flush();

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error("Cache flushTag error: {$e->getMessage()}", [
                'tags' => $tags,
                'store' => $store,
                'exception' => $e,
            ]);

            return false;
        }
    }

    // Este método está duplicado, se usa la implementación de arriba
    // /**
    //  * Generate a cache key for a model.
    //  *
    //  * @param string $modelName
    //  * @param mixed $identifier
    //  * @return string
    //  */
    // public function modelKey(string $modelName, $identifier): string
    // {
    //     $key = "model_{$modelName}_{$identifier}";
    //
    //     if ($this->trackKeys) {
    //         $this->trackCacheKey($key);
    //     }
    //
    //     return $key;
    // }

    // Este método está duplicado, se usa la implementación de arriba
    // /**
    //  * Generate a cache key for a query.
    //  *
    //  * @param string $name
    //  * @param array $params
    //  * @return string
    //  */
    // public function queryKey(string $name, array $params = []): string
    // {
    //     $paramString = empty($params) ? '' : '_' . md5(serialize($params));
    //     $key = "query_{$name}{$paramString}";
    //
    //     if ($this->trackKeys) {
    //         $this->trackCacheKey($key);
    //     }
    //
    //     return $key;
    // }

    /**
     * Generate a cache key for statistics.
     */
    public function statsKey(string $name, array $params = []): string
    {
        $paramString = $params === [] ? '' : '_'.md5(serialize($params));
        $key = "stats_{$name}{$paramString}";

        if ($this->trackKeys) {
            $this->trackCacheKey($key);
        }

        return $key;
    }

    /**
     * Track a cache key for later management.
     *
     * @param  int|null  $duration  Duration in minutes
     */
    protected function trackCacheKey(string $key, ?int $duration = null): void
    {
        try {
            $trackingKey = $this->getTrackingKey();
            $keys = $this->getTrackedKeys();

            if (! in_array($key, $keys)) {
                $keys[] = $key;
                Cache::put($trackingKey, $keys, Carbon::now()->addDays(30));
            }
        } catch (\Exception $e) {
            Log::error("Error tracking cache key: {$e->getMessage()}", [
                'key' => $key,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Remove a key from tracking.
     */
    protected function untrackCacheKey(string $key): void
    {
        try {
            $trackingKey = $this->getTrackingKey();
            $keys = $this->getTrackedKeys();

            if (($index = array_search($key, $keys)) !== false) {
                unset($keys[$index]);
                Cache::put($trackingKey, array_values($keys), Carbon::now()->addDays(30));
            }
        } catch (\Exception $e) {
            Log::error("Error untracking cache key: {$e->getMessage()}", [
                'key' => $key,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Remove multiple keys from tracking.
     */
    protected function untrackCacheKeys(array $keys): void
    {
        try {
            $trackingKey = $this->getTrackingKey();
            $trackedKeys = $this->getTrackedKeys();

            $trackedKeys = array_diff($trackedKeys, $keys);
            Cache::put($trackingKey, array_values($trackedKeys), Carbon::now()->addDays(30));
        } catch (\Exception $e) {
            Log::error("Error untracking multiple cache keys: {$e->getMessage()}", [
                'keys' => $keys,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Get the key used for tracking cache keys.
     */
    protected function getTrackingKey(): string
    {
        return "{$this->prefix}_tracked_cache_keys";
    }

    /**
     * Get all tracked cache keys.
     */
    public function getTrackedKeys(): array
    {
        try {
            $trackingKey = $this->getTrackingKey();

            return Cache::get($trackingKey, []);
        } catch (\Exception $e) {
            Log::error("Error getting tracked cache keys: {$e->getMessage()}", [
                'exception' => $e,
            ]);

            return [];
        }
    }

    /**
     * Get cache statistics.
     */
    public function getStats(): array
    {
        $stats = [
            'tracked_keys' => count($this->getTrackedKeys()),
            'stores' => [],
        ];

        try {
            // For Redis cache
            if (config('cache.default') === 'redis') {
                $redis = Redis::connection(config('cache.stores.redis.connection'));
                $keyCount = count($redis->keys("{$this->prefix}:*"));

                $stats['stores']['redis'] = [
                    'key_count' => $keyCount,
                    'memory_used' => $redis->info('memory')['used_memory_human'] ?? 'unknown',
                ];
            }

            // For file cache
            if (config('cache.default') === 'file') {
                $path = config('cache.stores.file.path');
                $fileCount = count(glob("{$path}/*.cache"));

                $stats['stores']['file'] = [
                    'key_count' => $fileCount,
                    'path' => $path,
                ];
            }
        } catch (\Exception $e) {
            Log::error("Error getting cache stats: {$e->getMessage()}", [
                'exception' => $e,
            ]);
        }

        return $stats;
    }

    /**
     * Set the default cache duration.
     *
     * @return $this
     */
    public function setDefaultDuration(int $minutes): static
    {
        $this->defaultDuration = $minutes;

        return $this;
    }

    /**
     * Set the cache store to use.
     *
     * @return $this
     */
    public function setStore(string $store): static
    {
        $this->store = $store;

        return $this;
    }

    /**
     * Set whether to track cache keys.
     *
     * @return $this
     */
    public function setTrackKeys(bool $track): static
    {
        $this->trackKeys = $track;

        return $this;
    }

    /**
     * Set the cache prefix.
     *
     * @return $this
     */
    public function setPrefix(string $prefix): static
    {
        $this->prefix = $prefix;

        return $this;
    }
}
