<?php

namespace App\Services;

use App\Models\ElectoralEvent;
use App\Models\ElectoralEventAttendance;
use App\Models\Person;
use App\Models\Tracking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class DataIntegrationService
{
    /**
     * Get integrated statistics for the dashboard.
     *
     * @param  bool  $useCache  Whether to use cached data
     * @return array
     */
    public function getDashboardStats($useCache = true)
    {
        $cacheKey = 'dashboard_stats';

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $now = Carbon::now();
        $startOfMonth = $now->copy()->startOfMonth();
        $startOfPrevMonth = $now->copy()->subMonth()->startOfMonth();
        $endOfPrevMonth = $now->copy()->subMonth()->endOfMonth();

        // Total persons
        $totalPersons = Person::count();
        $personsThisMonth = Person::where('created_at', '>=', $startOfMonth)->count();
        $personsPrevMonth = Person::whereBetween('created_at', [$startOfPrevMonth, $endOfPrevMonth])->count();
        $personsTrend = $personsPrevMonth > 0 ? (($personsThisMonth - $personsPrevMonth) / $personsPrevMonth) * 100 : 0;

        // 1x10 leaders
        $total1x10Leaders = Person::where('is_1x10', true)->count();
        $leadersThisMonth = Person::where('is_1x10', true)->where('created_at', '>=', $startOfMonth)->count();
        $leadersPrevMonth = Person::where('is_1x10', true)->whereBetween('created_at', [$startOfPrevMonth, $endOfPrevMonth])->count();
        $leadersTrend = $leadersPrevMonth > 0 ? (($leadersThisMonth - $leadersPrevMonth) / $leadersPrevMonth) * 100 : 0;

        // Patrol groups
        $totalGroups = \App\Models\PatrolGroup::count();
        $groupsThisMonth = \App\Models\PatrolGroup::where('created_at', '>=', $startOfMonth)->count();
        $groupsPrevMonth = \App\Models\PatrolGroup::whereBetween('created_at', [$startOfPrevMonth, $endOfPrevMonth])->count();
        $groupsTrend = $groupsPrevMonth > 0 ? (($groupsThisMonth - $groupsPrevMonth) / $groupsPrevMonth) * 100 : 0;

        // Events
        $totalEvents = ElectoralEvent::count();
        $eventsThisMonth = ElectoralEvent::where('created_at', '>=', $startOfMonth)->count();
        $eventsPrevMonth = ElectoralEvent::whereBetween('created_at', [$startOfPrevMonth, $endOfPrevMonth])->count();
        $eventsTrend = $eventsPrevMonth > 0 ? (($eventsThisMonth - $eventsPrevMonth) / $eventsPrevMonth) * 100 : 0;

        // Trackings
        $totalTrackings = Tracking::count();
        $trackingsThisMonth = Tracking::where('created_at', '>=', $startOfMonth)->count();
        $trackingsPrevMonth = Tracking::whereBetween('created_at', [$startOfPrevMonth, $endOfPrevMonth])->count();
        $trackingsTrend = $trackingsPrevMonth > 0 ? (($trackingsThisMonth - $trackingsPrevMonth) / $trackingsPrevMonth) * 100 : 0;

        // Role distribution
        $militantsCount = Person::where('role', 'Militante')->count();
        $votersCount = Person::where('role', 'Votante')->count();

        // Event attendance
        $totalAttendance = ElectoralEventAttendance::count();
        $attendanceThisMonth = ElectoralEventAttendance::where('created_at', '>=', $startOfMonth)->count();
        $attendancePrevMonth = ElectoralEventAttendance::whereBetween('created_at', [$startOfPrevMonth, $endOfPrevMonth])->count();
        $attendanceTrend = $attendancePrevMonth > 0 ? (($attendanceThisMonth - $attendancePrevMonth) / $attendancePrevMonth) * 100 : 0;

        $stats = [
            'total_persons' => $totalPersons,
            'persons_trend' => round($personsTrend),
            'total_1x10_leaders' => $total1x10Leaders,
            'leaders_trend' => round($leadersTrend),
            'total_groups' => $totalGroups,
            'groups_trend' => round($groupsTrend),
            'total_events' => $totalEvents,
            'events_trend' => round($eventsTrend),
            'total_trackings' => $totalTrackings,
            'trackings_trend' => round($trackingsTrend),
            'militants_count' => $militantsCount,
            'voters_count' => $votersCount,
            'total_attendance' => $totalAttendance,
            'attendance_trend' => round($attendanceTrend),
        ];

        if ($useCache) {
            // Cache for 1 hour
            Cache::put($cacheKey, $stats, now()->addHour());
        }

        return $stats;
    }

    /**
     * Get integrated data for a specific person.
     *
     * @param  bool  $useCache  Whether to use cached data
     * @return array
     */
    public function getPersonIntegratedData(Person $person, $useCache = true)
    {
        $cacheKey = "person_integrated_data_{$person->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get patrol group membership
        $patrolGroupMembership = $person->patrolGroups()
            ->with(['leader', 'estate', 'municipality', 'parish'])
            ->first();

        // Get leading patrol group
        $leadingPatrolGroup = $person->leadingPatrolGroups()
            ->with(['members' => function ($query): void {
                $query->where('status', 'active');
            }])
            ->first();

        // Get event attendance
        $eventAttendance = ElectoralEventAttendance::where('person_id', $person->id)
            ->with(['event' => function ($query): void {
                $query->with(['estate', 'municipality', 'parish', 'organizer']);
            }])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get trackings
        $trackings = Tracking::where('person_id', $person->id)
            ->with(['user', 'votingCenter'])
            ->orderBy('tracking_date', 'desc')
            ->get();

        // Calculate participation score
        $participationScore = $this->calculateParticipationScore($person);

        $data = [
            'person' => $person->load(['estate', 'municipality', 'parish', 'responsible']),
            'patrol_group_membership' => $patrolGroupMembership,
            'leading_patrol_group' => $leadingPatrolGroup,
            'event_attendance' => $eventAttendance,
            'trackings' => $trackings,
            'participation_score' => $participationScore,
            'last_activity_date' => $this->getLastActivityDate($person),
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }

    /**
     * Calculate a participation score for a person.
     *
     * @return int
     */
    private function calculateParticipationScore(Person $person): int|float
    {
        $score = 0;

        // Points for event attendance
        $eventsCount = ElectoralEventAttendance::where('person_id', $person->id)->count();
        $score += $eventsCount * 5; // 5 points per event

        // Points for trackings
        $trackingsCount = Tracking::where('person_id', $person->id)->count();
        $score += $trackingsCount * 2; // 2 points per tracking

        // Points for being a 1x10 leader
        if ($person->is_1x10) {
            $score += 50; // 50 points for being a leader

            // Additional points for persons assigned (responsible_id)
            $assignedCount = Person::where('responsible_id', $person->id)->count();
            $score += $assignedCount * 5; // 5 points per assigned person
        }

        return $score;
    }

    /**
     * Get the date of the last activity for a person.
     *
     * @return string|null
     */
    private function getLastActivityDate(Person $person)
    {
        $dates = [];

        // Last event attendance
        $lastEventAttendance = ElectoralEventAttendance::where('person_id', $person->id)
            ->orderBy('created_at', 'desc')
            ->first();
        if ($lastEventAttendance) {
            $dates[] = $lastEventAttendance->created_at;
        }

        // Last tracking
        $lastTracking = Tracking::where('person_id', $person->id)
            ->orderBy('tracking_date', 'desc')
            ->first();
        if ($lastTracking) {
            $dates[] = $lastTracking->tracking_date;
        }

        if ($dates === []) {
            return null;
        }

        // Sort dates and get the most recent
        usort($dates, fn($a, $b): int|float => $b->timestamp - $a->timestamp);

        return $dates[0]->format('Y-m-d H:i:s');
    }

    /**
     * Get integrated data for a patrol group.
     *
     * @param  bool  $useCache  Whether to use cached data
     * @return array
     */
    public function getPatrolGroupIntegratedData(\App\Models\PatrolGroup $group, $useCache = true)
    {
        $cacheKey = "patrol_group_integrated_data_{$group->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Get active members
        $activeMembers = $group->members()->where('status', 'active')->get();

        // Get inactive members
        $inactiveMembers = $group->members()->where('status', 'inactive')->get();

        // Get activities
        $activities = $group->activities()->orderBy('scheduled_date', 'desc')->get();

        // Calculate completion percentage
        $goalMembers = $group->goal_members > 0 ? $group->goal_members : 10;
        $completionPercentage = min(100, round(($activeMembers->count() / $goalMembers) * 100));

        // Calculate activity completion percentage
        $goalActivities = $group->goal_activities > 0 ? $group->goal_activities : 5;
        $completedActivities = $activities->where('status', 'completed')->count();
        $activityCompletionPercentage = min(100, round(($completedActivities / $goalActivities) * 100));

        // Get last activity
        $lastActivity = $activities->where('status', 'completed')->first();

        // Get next activity
        $nextActivity = $activities->where('status', 'planned')->where('scheduled_date', '>=', now())->sortBy('scheduled_date')->first();

        $data = [
            'group' => $group->load(['leader', 'estate', 'municipality', 'parish']),
            'active_members' => $activeMembers,
            'inactive_members' => $inactiveMembers,
            'activities' => $activities,
            'completion_percentage' => $completionPercentage,
            'activity_completion_percentage' => $activityCompletionPercentage,
            'last_activity' => $lastActivity,
            'next_activity' => $nextActivity,
        ];

        if ($useCache) {
            // Cache for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        return $data;
    }
}
