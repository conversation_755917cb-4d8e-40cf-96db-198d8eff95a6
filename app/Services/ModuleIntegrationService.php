<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\CampaignTarget;
use App\Models\ElectoralEventAttendance;
use App\Models\ElectoralMobilization;
use App\Models\ElectoralMobilizationDetail;
use App\Models\Person;
use App\Models\Tracking;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ModuleIntegrationService
{
    /**
     * Default cache duration in minutes.
     */
    protected int $cacheDuration = 30;

    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Cache service instance.
         */
        protected CacheService $cacheService,
        /**
         * Logging service instance.
         */
        protected LoggingService $loggingService,
        /**
         * Event bus service instance.
         */
        protected EventBusService $eventBusService
    )
    {
    }

    /**
     * Create a campaign for a patrol group.
     *
     * @return Campaign|null
     */
    public function createCampaignForPatrolGroup(int $groupId, array $data): null
    {
        // This method is kept as a placeholder but its functionality has been removed
        Log::info('createCampaignForPatrolGroup method called but functionality has been removed');
        return null;
    }

    /**
     * Crear una campaña dirigida a los participantes de una movilización electoral.
     */
    public function createCampaignForMobilization(ElectoralMobilization $mobilization, array $data)
    {
        // Crear la campaña
        $campaign = Campaign::create([
            'name' => $data['name'] ?? 'Campaña para movilización '.$mobilization->name,
            'description' => $data['description'] ?? 'Campaña automática para la movilización '.$mobilization->name,
            'type' => $data['type'] ?? 'sms',
            'status' => $data['status'] ?? 'draft',
            'scheduled_date' => $data['scheduled_date'] ?? now()->addDay(),
            'message_template' => $data['message_template'] ?? 'Hola {name}, este es un mensaje para la movilización electoral '.$mobilization->name,
            'created_by' => auth()->id(),
        ]);

        // Crear el target para la movilización
        CampaignTarget::create([
            'campaign_id' => $campaign->id,
            'target_type' => \App\Models\ElectoralMobilization::class,
            'target_id' => $mobilization->id,
            'status' => 'pending',
        ]);

        // Añadir targets individuales para cada persona en la movilización
        $details = $mobilization->details()->with('person')->get();

        foreach ($details as $detail) {
            $person = $detail->person;

            if ($person && $person->phone) {
                $personTarget = CampaignTarget::create([
                    'campaign_id' => $campaign->id,
                    'target_type' => \App\Models\Person::class,
                    'target_id' => $person->id,
                    'status' => 'pending',
                ]);

                // Crear detalle de campaña para esta persona
                $campaign->details()->create([
                    'campaign_target_id' => $personTarget->id,
                    'phone_number' => $person->phone,
                    'message' => str_replace(
                        ['{name}', '{mobilization}', '{voting_center}', '{date}'],
                        [
                            $person->name,
                            $mobilization->name,
                            $mobilization->votingCenter->name ?? 'Centro de votación',
                            $mobilization->mobilization_date->format('d/m/Y'),
                        ],
                        $campaign->message_template
                    ),
                    'status' => 'pending',
                ]);
            }
        }

        return $campaign;
    }

    /**
     * Crear tracking para una persona basado en su actividad en diferentes módulos.
     * @return mixed[]
     */
    public function createIntegratedTracking(Person $person): array
    {
        $trackings = [];

        // Patrol group module handles this functionality

        // Tracking de asistencia a eventos electorales
        $eventAttendances = ElectoralEventAttendance::where('person_id', $person->id)
            ->with('event')
            ->get();

        foreach ($eventAttendances as $attendance) {
            $trackings[] = Tracking::create([
                'person_id' => $person->id,
                'trackable_type' => $attendance::class,
                'trackable_id' => $attendance->id,
                'tracking_type' => 'electoral_event_attendance',
                'status' => $attendance->status,
                'tracking_date' => $attendance->check_in_time ?? now(),
                'notes' => 'Asistencia a evento electoral: '.$attendance->event->name,
            ]);
        }

        // Tracking de movilización electoral
        $mobilizationDetails = ElectoralMobilizationDetail::where('person_id', $person->id)
            ->with('mobilization')
            ->get();

        foreach ($mobilizationDetails as $detail) {
            $trackings[] = Tracking::create([
                'person_id' => $person->id,
                'trackable_type' => $detail::class,
                'trackable_id' => $detail->id,
                'tracking_type' => 'electoral_mobilization',
                'status' => $detail->status,
                'tracking_date' => now(),
                'notes' => 'Movilización electoral: '.$detail->mobilization->name,
            ]);
        }

        // Tracking de campañas
        $campaignTargets = CampaignTarget::where('target_type', \App\Models\Person::class)
            ->where('target_id', $person->id)
            ->with('campaign')
            ->get();

        foreach ($campaignTargets as $target) {
            $details = $target->details;

            foreach ($details as $detail) {
                $trackings[] = Tracking::create([
                    'person_id' => $person->id,
                    'trackable_type' => $detail::class,
                    'trackable_id' => $detail->id,
                    'tracking_type' => 'campaign_message',
                    'status' => $detail->status,
                    'tracking_date' => $detail->sent_at ?? now(),
                    'notes' => 'Mensaje de campaña: '.$target->campaign->name,
                ]);
            }
        }

        return $trackings;
    }

    /**
     * Obtener un resumen integrado de la actividad de una persona en todos los módulos.
     *
     * @param  bool  $useCache  Usar caché para mejorar rendimiento
     * @return array
     */
    public function getPersonIntegratedSummary(Person $person, bool $useCache = true)
    {
        $cacheKey = "person_summary_{$person->id}";

        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $summary = [
            'person' => $person->load(['estate', 'municipality', 'parish']),
            'patrol_groups' => collect(), // Patrol groups functionality has been removed
            'electoral_events' => ElectoralEventAttendance::where('person_id', $person->id)
                ->with(['event' => function ($query): void {
                    $query->with(['estate', 'municipality', 'parish', 'organizer']);
                }])
                ->orderBy('created_at', 'desc')
                ->get(),
            'mobilizations' => ElectoralMobilizationDetail::where('person_id', $person->id)
                ->with(['mobilization' => function ($query): void {
                    $query->with(['votingCenter', 'coordinator']);
                }])
                ->orderBy('created_at', 'desc')
                ->get(),
            'campaigns' => CampaignTarget::where('target_type', \App\Models\Person::class)
                ->where('target_id', $person->id)
                ->with(['campaign', 'details'])
                ->orderBy('created_at', 'desc')
                ->get(),
            'trackings' => Tracking::where('person_id', $person->id)
                ->with(['user', 'votingCenter'])
                ->orderBy('tracking_date', 'desc')
                ->get(),
            'stats' => $this->getPersonStats($person),
        ];

        if ($useCache) {
            // Guardar en caché por 30 minutos
            Cache::put($cacheKey, $summary, now()->addMinutes(30));
        }

        return $summary;
    }

    /**
     * Obtener estadísticas de la persona en todos los módulos
     *
     * @return array
     */
    public function getPersonStats(Person $person)
    {
        // Usar caché para mejorar rendimiento
        $cacheKey = "person_stats_{$person->id}";

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $stats = [
            'total_events_attended' => ElectoralEventAttendance::where('person_id', $person->id)->count(),
            'total_mobilizations' => ElectoralMobilizationDetail::where('person_id', $person->id)->count(),
            'total_group_activities' => 0, // Patrol group activities
            'total_trackings' => Tracking::where('person_id', $person->id)->count(),
            'last_activity_date' => $this->getLastActivityDate($person),
            'participation_score' => $this->calculateParticipationScore($person),
        ];

        // Guardar en caché por 15 minutos
        Cache::put($cacheKey, $stats, now()->addMinutes(15));

        return $stats;
    }

    /**
     * Obtener la fecha de la última actividad de la persona
     *
     * @return string|null
     */
    private function getLastActivityDate(Person $person)
    {
        // Usar caché para mejorar rendimiento
        $cacheKey = "person_last_activity_{$person->id}";

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $dates = [];

        // Optimizar consultas usando select para obtener solo la columna necesaria
        // Última asistencia a evento electoral
        $lastEventAttendance = ElectoralEventAttendance::where('person_id', $person->id)
            ->orderBy('created_at', 'desc')
            ->select('created_at')
            ->first();
        if ($lastEventAttendance) {
            $dates[] = $lastEventAttendance->created_at;
        }

        // Última movilización
        $lastMobilization = ElectoralMobilizationDetail::where('person_id', $person->id)
            ->orderBy('created_at', 'desc')
            ->select('created_at')
            ->first();
        if ($lastMobilization) {
            $dates[] = $lastMobilization->created_at;
        }

        // Último seguimiento
        $lastTracking = Tracking::where('person_id', $person->id)
            ->orderBy('tracking_date', 'desc')
            ->select('tracking_date')
            ->first();
        if ($lastTracking) {
            $dates[] = $lastTracking->tracking_date;
        }

        // Patrol group activities are tracked separately

        if ($dates === []) {
            return null;
        }

        // Ordenar fechas y obtener la más reciente
        usort($dates, fn($a, $b): int|float => $b->timestamp - $a->timestamp);

        $result = $dates[0]->format('Y-m-d H:i:s');

        // Guardar en caché por 15 minutos
        Cache::put("person_last_activity_{$person->id}", $result, now()->addMinutes(15));

        return $result;
    }

    /**
     * Calcular una puntuación de participación para la persona
     *
     * @return int
     */
    private function calculateParticipationScore(Person $person)
    {
        // Usar caché para mejorar rendimiento
        $cacheKey = "person_participation_score_{$person->id}";

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $score = 0;

        // Puntos por asistencia a eventos electorales
        $eventsCount = ElectoralEventAttendance::where('person_id', $person->id)->count();
        $score += $eventsCount * 5; // 5 puntos por cada evento

        // Puntos por movilizaciones
        $mobilizationsCount = ElectoralMobilizationDetail::where('person_id', $person->id)->count();
        $score += $mobilizationsCount * 10; // 10 puntos por cada movilización

        // Patrol group participation is calculated separately

        // Puntos por seguimientos
        $trackingsCount = Tracking::where('person_id', $person->id)->count();
        $score += $trackingsCount * 2; // 2 puntos por cada seguimiento

        // Puntos por ser líder 1x10
        if ($person->is_1x10) {
            $score += 50; // 50 puntos por ser líder

            // Puntos adicionales por personas asignadas (responsible_id)
            $assignedCount = Person::where('responsible_id', $person->id)->count();
            $score += $assignedCount * 5; // 5 puntos por cada persona asignada
        }

        // Guardar en caché por 30 minutos
        Cache::put("person_participation_score_{$person->id}", $score, now()->addMinutes(30));

        return $score;
    }
}
