<?php

namespace App\Services;

use App\Models\Audit;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuditService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Logging service instance.
         */
        protected LoggingService $loggingService
    )
    {
    }

    /**
     * Log an audit event.
     *
     * @param  mixed  $entityId
     */
    public function log(string $action, string $entity, $entityId, array $oldValues = [], array $newValues = [], ?string $notes = null): ?Audit
    {
        try {
            $userId = Auth::id();

            $audit = Audit::create([
                'user_id' => $userId,
                'action' => $action,
                'entity' => $entity,
                'entity_id' => $entityId,
                'old_values' => $oldValues === [] ? null : json_encode($oldValues),
                'new_values' => $newValues === [] ? null : json_encode($newValues),
                'notes' => $notes,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'created_at' => Carbon::now(),
            ]);

            $this->loggingService->info("Audit log created: {$action} {$entity} #{$entityId}", [
                'audit_id' => $audit->id,
                'user_id' => $userId,
                'action' => $action,
                'entity' => $entity,
                'entity_id' => $entityId,
            ]);

            return $audit;
        } catch (\Exception $e) {
            $this->loggingService->error("Error creating audit log: {$e->getMessage()}", [
                'action' => $action,
                'entity' => $entity,
                'entity_id' => $entityId,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Log a create action.
     *
     * @param  mixed  $entityId
     */
    public function logCreate(string $entity, $entityId, array $values = [], ?string $notes = null): ?Audit
    {
        return $this->log('create', $entity, $entityId, [], $values, $notes);
    }

    /**
     * Log an update action.
     *
     * @param  mixed  $entityId
     */
    public function logUpdate(string $entity, $entityId, array $oldValues = [], array $newValues = [], ?string $notes = null): ?Audit
    {
        return $this->log('update', $entity, $entityId, $oldValues, $newValues, $notes);
    }

    /**
     * Log a delete action.
     *
     * @param  mixed  $entityId
     */
    public function logDelete(string $entity, $entityId, array $values = [], ?string $notes = null): ?Audit
    {
        return $this->log('delete', $entity, $entityId, $values, [], $notes);
    }

    /**
     * Log a login action.
     */
    public function logLogin(int $userId, bool $success, ?string $notes = null): ?Audit
    {
        $action = $success ? 'login' : 'login_failed';

        return $this->log($action, 'user', $userId, [], [], $notes);
    }

    /**
     * Log a logout action.
     */
    public function logLogout(int $userId, ?string $notes = null): ?Audit
    {
        return $this->log('logout', 'user', $userId, [], [], $notes);
    }

    /**
     * Log a permission change action.
     */
    public function logPermissionChange(int $userId, array $oldPermissions, array $newPermissions, ?string $notes = null): ?Audit
    {
        return $this->log('permission_change', 'user', $userId, $oldPermissions, $newPermissions, $notes);
    }

    /**
     * Log a role change action.
     */
    public function logRoleChange(int $userId, array $oldRoles, array $newRoles, ?string $notes = null): ?Audit
    {
        return $this->log('role_change', 'user', $userId, $oldRoles, $newRoles, $notes);
    }

    /**
     * Get audit logs for a specific entity.
     *
     * @param  mixed  $entityId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getEntityAuditLogs(string $entity, $entityId, int $limit = 100, int $offset = 0)
    {
        return Audit::where('entity', $entity)
            ->where('entity_id', $entityId)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }

    /**
     * Get audit logs for a specific user.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserAuditLogs(int $userId, int $limit = 100, int $offset = 0)
    {
        return Audit::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }

    /**
     * Get audit logs for a specific action.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActionAuditLogs(string $action, int $limit = 100, int $offset = 0)
    {
        return Audit::where('action', $action)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }

    /**
     * Get audit logs for a specific date range.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDateRangeAuditLogs(\DateTime $startDate, \DateTime $endDate, int $limit = 100, int $offset = 0)
    {
        return Audit::whereBetween('created_at', [$startDate, $endDate])
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }

    /**
     * Search audit logs.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function searchAuditLogs(array $criteria, int $limit = 100, int $offset = 0)
    {
        $query = Audit::query();

        if (isset($criteria['user_id'])) {
            $query->where('user_id', $criteria['user_id']);
        }

        if (isset($criteria['action'])) {
            $query->where('action', $criteria['action']);
        }

        if (isset($criteria['entity'])) {
            $query->where('entity', $criteria['entity']);
        }

        if (isset($criteria['entity_id'])) {
            $query->where('entity_id', $criteria['entity_id']);
        }

        if (isset($criteria['start_date']) && isset($criteria['end_date'])) {
            $query->whereBetween('created_at', [$criteria['start_date'], $criteria['end_date']]);
        }

        if (isset($criteria['ip_address'])) {
            $query->where('ip_address', $criteria['ip_address']);
        }

        if (isset($criteria['search'])) {
            $search = $criteria['search'];
            $query->where(function ($q) use ($search): void {
                $q->where('notes', 'like', "%{$search}%")
                    ->orWhere('old_values', 'like', "%{$search}%")
                    ->orWhere('new_values', 'like', "%{$search}%");
            });
        }

        return $query->with('user')
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }
}
