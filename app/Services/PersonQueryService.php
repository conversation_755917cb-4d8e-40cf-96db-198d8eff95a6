<?php

namespace App\Services;

use App\Models\Person;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class PersonQueryService
{
    /**
     * Obtiene personas con filtros optimizados para rendimiento.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getFilteredPersons(array $filters = [], array $relations = [], int $perPage = 15)
    {
        $query = Person::query();

        // Aplicar relaciones
        if ($relations !== []) {
            $query->with($relations);
        }

        // Aplicar filtros
        $query = $this->applyFilters($query, $filters);

        // Aplicar ordenamiento
        $sortField = $filters['sort_field'] ?? 'name';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        // Aplicar paginación
        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Aplica filtros a la consulta de personas.
     */
    protected function applyFilters(Builder $query, array $filters): Builder
    {
        // Filtro por búsqueda
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search): void {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('cedula', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filtro por rol
        if (!empty($filters['role'])) {
            $query->where('role', $filters['role']);
        }

        // Filtro por estado de actividad
        if (!empty($filters['activity_status'])) {
            $query->where('activity_status', $filters['activity_status']);
        }

        // Filtro por líder 1x10
        if (isset($filters['is_1x10'])) {
            $query->where('is_1x10', $filters['is_1x10'] === 'yes');
        }

        // Filtro por responsable
        if (!empty($filters['responsible_id'])) {
            $query->where('responsible_id', $filters['responsible_id']);
        } elseif (isset($filters['has_responsible']) && $filters['has_responsible'] === 'yes') {
            $query->whereNotNull('responsible_id');
        } elseif (isset($filters['has_responsible']) && $filters['has_responsible'] === 'no') {
            $query->whereNull('responsible_id');
        }

        // Filtros de ubicación
        if (!empty($filters['estate_id'])) {
            $query->where('estate_id', $filters['estate_id']);
        }

        if (!empty($filters['municipality_id'])) {
            $query->where('municipality_id', $filters['municipality_id']);
        }

        if (!empty($filters['parish_id'])) {
            $query->where('parish_id', $filters['parish_id']);
        }

        return $query;
    }

    /**
     * Obtiene estadísticas de personas con caché para mejorar rendimiento.
     */
    public function getPersonStatistics(): array
    {
        return Cache::remember('person_statistics', 60 * 5, fn(): array => [
            'total' => Person::count(),
            'militants' => Person::where('role', 'Militante')->count(),
            'voters' => Person::where('role', 'Votante')->count(),
            'leaders_1x10' => Person::where('is_1x10', true)->count(),
            'with_responsible' => Person::whereNotNull('responsible_id')->count(),
            'without_responsible' => Person::whereNull('responsible_id')->where('is_1x10', false)->count(),
            'active' => Person::where('activity_status', 'Activo')->count(),
            'inactive' => Person::where('activity_status', 'Inactivo')->count(),
        ]);
    }

    /**
     * Obtiene personas asignadas a un líder 1x10 específico.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPersonsAssignedToLeader(int $leaderId, array $filters = [], int $perPage = 15)
    {
        $query = Person::where('responsible_id', $leaderId);

        // Aplicar filtro de búsqueda
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search): void {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('cedula', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Ordenar por nombre por defecto
        $query->orderBy('name');

        return $query->paginate($perPage)->withQueryString();
    }
}
