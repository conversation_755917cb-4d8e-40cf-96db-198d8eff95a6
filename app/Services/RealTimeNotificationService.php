<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class RealTimeNotificationService
{
    /**
     * Create a new real-time notification.
     *
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string $type The notification type (info, success, warning, error)
     * @param int|null $userId The user ID to notify (null for current user)
     * @param string|null $link Optional link to include in the notification
     * @param array $data Optional additional data
     * @return Notification|null
     */
    public function create(string $title, string $message, string $type = 'info', ?int $userId = null, ?string $link = null, array $data = [])
    {
        $userId ??= Auth::id();

        if (!$userId) {
            return null;
        }

        try {
            $notification = Notification::create([
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'link' => $link,
                'data' => $data,
            ]);

            // Trigger real-time event
            $this->triggerRealTimeEvent($userId, $notification);

            return $notification;
        } catch (\Exception $e) {
            Log::error('Failed to create notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Create notifications for multiple users.
     *
     * @param array $userIds Array of user IDs
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string $type The notification type (info, success, warning, error)
     * @param string|null $link Optional link to include in the notification
     * @param array $data Optional additional data
     * @return int Number of notifications created
     */
    public function createForMultipleUsers(array $userIds, string $title, string $message, string $type = 'info', ?string $link = null, array $data = []): int
    {
        $count = 0;

        foreach ($userIds as $userId) {
            $notification = $this->create($title, $message, $type, $userId, $link, $data);
            if ($notification) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Create a notification for all users with a specific role.
     *
     * @param string $role The role name
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string $type The notification type (info, success, warning, error)
     * @param string|null $link Optional link to include in the notification
     * @param array $data Optional additional data
     * @return int Number of notifications created
     */
    public function createForUsersWithRole(string $role, string $title, string $message, string $type = 'info', ?string $link = null, array $data = []): int
    {
        $userIds = User::role($role)->pluck('id')->toArray();
        return $this->createForMultipleUsers($userIds, $title, $message, $type, $link, $data);
    }

    /**
     * Create a notification for all 1x10 leaders.
     *
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string $type The notification type (info, success, warning, error)
     * @param string|null $link Optional link to include in the notification
     * @param array $data Optional additional data
     * @return int Number of notifications created
     */
    public function createFor1x10Leaders(string $title, string $message, string $type = 'info', ?string $link = null, array $data = [])
    {
        return $this->createForUsersWithRole('1x10_leader', $title, $message, $type, $link, $data);
    }

    /**
     * Create a notification for all users.
     *
     * @param string $title The notification title
     * @param string $message The notification message
     * @param string $type The notification type (info, success, warning, error)
     * @param string|null $link Optional link to include in the notification
     * @param array $data Optional additional data
     * @return int Number of notifications created
     */
    public function createForAllUsers(string $title, string $message, string $type = 'info', ?string $link = null, array $data = []): int
    {
        $userIds = User::pluck('id')->toArray();
        return $this->createForMultipleUsers($userIds, $title, $message, $type, $link, $data);
    }

    /**
     * Create an electoral event notification.
     *
     * @param int $userId User ID to notify
     * @param int $eventId Event ID
     * @param string $eventName Event name
     * @param Carbon $eventDate Event date
     * @return Notification|null
     */
    public function createEventNotification(int $userId, int $eventId, string $eventName, Carbon $eventDate)
    {
        $title = 'Evento Electoral';
        $message = "Nuevo evento: {$eventName} el {$eventDate->format('d/m/Y H:i')}";
        $link = route('admin.electoral.events.show', $eventId);
        $data = [
            'event_id' => $eventId,
            'event_name' => $eventName,
            'event_date' => $eventDate->format('Y-m-d H:i:s'),
        ];

        return $this->create($title, $message, 'info', $userId, $link, $data);
    }

    /**
     * Create a mobilization notification.
     *
     * @param int $userId User ID to notify
     * @param int $mobilizationId Mobilization ID
     * @param string $mobilizationName Mobilization name
     * @param Carbon $mobilizationDate Mobilization date
     * @return Notification|null
     */
    public function createMobilizationNotification(int $userId, int $mobilizationId, string $mobilizationName, Carbon $mobilizationDate)
    {
        $title = 'Movilización Electoral';
        $message = "Nueva movilización: {$mobilizationName} el {$mobilizationDate->format('d/m/Y H:i')}";
        $link = route('admin.electoral.mobilization.show', $mobilizationId);
        $data = [
            'mobilization_id' => $mobilizationId,
            'mobilization_name' => $mobilizationName,
            'mobilization_date' => $mobilizationDate->format('Y-m-d H:i:s'),
        ];

        return $this->create($title, $message, 'info', $userId, $link, $data);
    }

    /**
     * Create a voting center notification.
     *
     * @param int $userId User ID to notify
     * @param int $votingCenterId Voting center ID
     * @param string $votingCenterName Voting center name
     * @return Notification|null
     */
    public function createVotingCenterNotification(int $userId, int $votingCenterId, string $votingCenterName)
    {
        $title = 'Centro de Votación';
        $message = "Nuevo centro de votación: {$votingCenterName}";
        $link = route('admin.electoral.voting-centers');
        $data = [
            'voting_center_id' => $votingCenterId,
            'voting_center_name' => $votingCenterName,
        ];

        return $this->create($title, $message, 'info', $userId, $link, $data);
    }

    /**
     * Create a person assignment notification.
     *
     * @param int $userId User ID to notify
     * @param int $personId Person ID
     * @param string $personName Person name
     * @return Notification|null
     */
    public function createPersonAssignmentNotification(int $userId, int $personId, string $personName)
    {
        $title = 'Asignación de Persona';
        $message = "Nueva persona asignada: {$personName}";
        $link = route('1x10.manage');
        $data = [
            'person_id' => $personId,
            'person_name' => $personName,
        ];

        return $this->create($title, $message, 'success', $userId, $link, $data);
    }

    /**
     * Trigger a real-time event for a notification.
     *
     * @param int $userId User ID
     * @param Notification $notification Notification object
     * @return void
     */
    protected function triggerRealTimeEvent(int $userId, Notification $notification)
    {
        try {
            // Dispatch Livewire event
            event(new \App\Events\NotificationCreated($userId, $notification));
        } catch (\Exception $e) {
            Log::error('Failed to trigger real-time event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
