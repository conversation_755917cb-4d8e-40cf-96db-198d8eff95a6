<?php

namespace App\Services\UltraMsg;

class UltraMsgService
{
    protected string $baseUrl;

    protected string $token;

    public function __construct()
    {
        $this->token = config('services.ultramsg.token');
        $this->baseUrl = config('services.ultramsg.base_url');
    }

    public function sendMessage(string $to, string $message): array
    {
        $params = [
            'token' => $this->token,
            'to' => $this->formatPhoneNumber($to),
            'body' => $message,
        ];

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "{$this->baseUrl}/messages/chat",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_SSL_VERIFYPEER => 0,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_HTTPHEADER => [
                'content-type: application/x-www-form-urlencoded',
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err !== '' && $err !== '0') {
            throw new UltraMsgException('cURL Error: '.$err);
        }

        $decodedResponse = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new UltraMsgException('Invalid JSON response: '.$response);
        }

        return $decodedResponse;
    }

    protected function formatPhoneNumber(string $number): string
    {
        // Eliminar todos los caracteres no numéricos
        $number = preg_replace('/[^0-9]/', '', $number);

        // Si el número no comienza con +, agregarlo
        if (! str_starts_with((string) $number, '+')) {
            return '+'.$number;
        }

        return $number;
    }
}
