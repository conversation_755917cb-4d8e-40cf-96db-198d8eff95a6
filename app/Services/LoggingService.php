<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LoggingService
{
    /**
     * Default log channel
     */
    protected string $channel = 'stack';

    /**
     * Whether to include context data automatically
     */
    protected bool $includeContext = true;

    /**
     * Whether to store logs in database
     */
    protected bool $storeInDatabase = true;

    /**
     * Create a new service instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Log an emergency message.
     */
    public function emergency(string $message, array $context = []): void
    {
        $this->log('emergency', $message, $context);
    }

    /**
     * Log an alert message.
     */
    public function alert(string $message, array $context = []): void
    {
        $this->log('alert', $message, $context);
    }

    /**
     * Log a critical message.
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log('critical', $message, $context);
    }

    /**
     * Log an error message.
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('error', $message, $context);
    }

    /**
     * Log a warning message.
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('warning', $message, $context);
    }

    /**
     * Log a notice message.
     */
    public function notice(string $message, array $context = []): void
    {
        $this->log('notice', $message, $context);
    }

    /**
     * Log an info message.
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('info', $message, $context);
    }

    /**
     * Log a debug message.
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('debug', $message, $context);
    }

    /**
     * Log a message with the specified level.
     */
    public function log(string $level, string $message, array $context = []): void
    {
        if ($this->includeContext) {
            $context = array_merge($this->getDefaultContext(), $context);
        }

        Log::channel($this->channel)->$level($message, $context);

        if ($this->storeInDatabase) {
            $this->storeLogInDatabase($level, $message, $context);
        }
    }

    /**
     * Log an API request.
     *
     * @param  mixed  $response
     */
    public function logApiRequest(string $method, string $endpoint, array $request, $response, int $statusCode, float $duration): void
    {
        $level = $statusCode >= 400 ? 'error' : 'info';

        $context = [
            'method' => $method,
            'endpoint' => $endpoint,
            'request' => $this->sanitizeData($request),
            'response' => $this->sanitizeData($response),
            'status_code' => $statusCode,
            'duration_ms' => round($duration * 1000, 2),
        ];

        $message = "[API] {$method} {$endpoint} - {$statusCode} ({$context['duration_ms']}ms)";

        $this->log($level, $message, $context);
    }

    /**
     * Log a database query.
     */
    public function logQuery(string $query, array $bindings, float $duration): void
    {
        $context = [
            'query' => $query,
            'bindings' => $this->sanitizeData($bindings),
            'duration_ms' => round($duration * 1000, 2),
        ];

        $this->log('debug', "[DB] Query executed ({$context['duration_ms']}ms)", $context);
    }

    /**
     * Log a model event.
     *
     * @param  mixed  $modelId
     */
    public function logModelEvent(string $event, string $model, $modelId, array $attributes = []): void
    {
        $context = [
            'model' => $model,
            'model_id' => $modelId,
            'attributes' => $this->sanitizeData($attributes),
        ];

        $this->log('info', "[Model] {$event} {$model} #{$modelId}", $context);
    }

    /**
     * Log an authentication event.
     *
     * @param  mixed  $userId
     */
    public function logAuthEvent(string $event, $userId, array $context = []): void
    {
        $context = array_merge([
            'user_id' => $userId,
        ], $context);

        $this->log('info', "[Auth] {$event}", $context);
    }

    /**
     * Log an exception.
     */
    public function logException(\Throwable $exception, array $context = []): void
    {
        $context = array_merge([
            'exception' => $exception::class,
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ], $context);

        $this->log('error', "[Exception] {$context['exception']}: {$context['message']}", $context);
    }

    /**
     * Get default context data to include in logs.
     */
    protected function getDefaultContext(): array
    {
        $context = [
            'request_id' => $this->getRequestId(),
            'timestamp' => Carbon::now()->toIso8601String(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
        ];

        if (Auth::check()) {
            $context['user_id'] = Auth::id();
            $context['username'] = Auth::user()->username;
        }

        return $context;
    }

    /**
     * Get or generate a unique request ID.
     */
    protected function getRequestId(): string
    {
        if (! app()->has('request_id')) {
            app()->instance('request_id', (string) Str::uuid());
        }

        return app('request_id');
    }

    /**
     * Sanitize sensitive data for logging.
     *
     * @param  mixed  $data
     * @return mixed
     */
    protected function sanitizeData($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (in_array(strtolower($key), ['password', 'token', 'secret', 'key', 'api_key', 'auth', 'authorization'])) {
                    $data[$key] = '********';
                } elseif (is_array($value)) {
                    $data[$key] = $this->sanitizeData($value);
                }
            }
        }

        return $data;
    }

    /**
     * Store a log entry in the database.
     */
    protected function storeLogInDatabase(string $level, string $message, array $context): void
    {
        try {
            DB::table('system_logs')->insert([
                'level' => $level,
                'message' => $message,
                'context' => json_encode($context),
                'created_at' => Carbon::now(),
            ]);
        } catch (\Exception $e) {
            // Don't log this exception to avoid infinite loops
            Log::channel('single')->error("Failed to store log in database: {$e->getMessage()}");
        }
    }

    /**
     * Set the log channel.
     *
     * @return $this
     */
    public function channel(string $channel): static
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * Set whether to include context data automatically.
     *
     * @return $this
     */
    public function includeContext(bool $include): static
    {
        $this->includeContext = $include;

        return $this;
    }

    /**
     * Set whether to store logs in database.
     *
     * @return $this
     */
    public function storeInDatabase(bool $store): static
    {
        $this->storeInDatabase = $store;

        return $this;
    }
}
