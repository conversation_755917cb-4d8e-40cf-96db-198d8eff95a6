<?php

namespace App\Services;

use App\Models\Person;
use App\Models\Tracking;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TrackingService
{
    /**
     * Crea un nuevo registro de seguimiento.
     */
    public function createTracking(array $data): Tracking
    {
        // Asegurarse de que el usuario actual esté asignado
        $data['user_id'] ??= Auth::id();
        
        // Crear el registro de seguimiento
        $tracking = Tracking::create($data);
        
        // Invalidar caché de estadísticas
        $this->invalidateTrackingStatisticsCache();
        
        return $tracking;
    }
    
    /**
     * Crea registros de seguimiento para múltiples personas.
     *
     * @return int Número de registros creados
     */
    public function createBulkTrackings(array $personIds, array $data): int
    {
        $userId = $data['user_id'] ?? Auth::id();
        $trackings = [];
        
        foreach ($personIds as $personId) {
            $trackings[] = array_merge($data, [
                'person_id' => $personId,
                'user_id' => $userId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        // Insertar en lotes para mejor rendimiento
        $chunkedTrackings = array_chunk($trackings, 100);
        $count = 0;
        
        DB::beginTransaction();
        try {
            foreach ($chunkedTrackings as $chunk) {
                $count += DB::table('trackings')->insert($chunk);
            }
            DB::commit();
            
            // Invalidar caché de estadísticas
            $this->invalidateTrackingStatisticsCache();
            
            return $count;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * Obtiene estadísticas de seguimiento con caché para mejorar rendimiento.
     */
    public function getTrackingStatistics(): array
    {
        $cacheKey = 'tracking_statistics';
        
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $statistics = [
            'total' => Tracking::count(),
            'by_type' => [
                'contact' => Tracking::ofType('contact')->count(),
                'mobilization' => Tracking::ofType('mobilization')->count(),
                'voter_mark' => Tracking::ofType('voter_mark')->count(),
                'follow_up' => Tracking::ofType('follow_up')->count(),
            ],
            'by_status' => [
                'pending' => Tracking::withStatus('pending')->count(),
                'completed' => Tracking::withStatus('completed')->count(),
                'cancelled' => Tracking::withStatus('cancelled')->count(),
            ],
            'by_priority' => [
                'low' => Tracking::withPriority('low')->count(),
                'medium' => Tracking::withPriority('medium')->count(),
                'high' => Tracking::withPriority('high')->count(),
            ],
            'recent' => [
                'today' => Tracking::whereDate('tracking_date', today())->count(),
                'this_week' => Tracking::whereBetween('tracking_date', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'this_month' => Tracking::whereMonth('tracking_date', now()->month)->whereYear('tracking_date', now()->year)->count(),
            ],
        ];
        
        // Guardar en caché por 5 minutos
        Cache::put($cacheKey, $statistics, 60 * 5);
        
        return $statistics;
    }
    
    /**
     * Obtiene los usuarios más activos en seguimiento.
     */
    public function getTopUsers(int $limit = 5, array $dateRange = []): \Illuminate\Support\Collection
    {
        $query = Tracking::query()
            ->join('users', 'trackings.user_id', '=', 'users.id')
            ->select('users.id', 'users.name', DB::raw('count(*) as count'));
        
        if (!empty($dateRange['start']) && !empty($dateRange['end'])) {
            $query->whereBetween('tracking_date', [$dateRange['start'], $dateRange['end']]);
        }
        
        return $query->groupBy('users.id', 'users.name')
            ->orderBy('count', 'desc')
            ->limit($limit)
            ->get();
    }
    
    /**
     * Invalida la caché de estadísticas de seguimiento.
     */
    protected function invalidateTrackingStatisticsCache(): void
    {
        Cache::forget('tracking_statistics');
    }
}
