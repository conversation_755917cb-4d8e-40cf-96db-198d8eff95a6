<?php

namespace App\Services;

use App\Jobs\SendEmailNotification;
use App\Jobs\SendSmsNotification;
use App\Models\Notification;
use App\Models\Person;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Create a new in-app notification.
     *
     * @param  string  $title  The notification title
     * @param  string  $message  The notification message
     * @param  string  $type  The notification type (info, success, warning, error)
     * @param  int|null  $userId  The user ID to notify (null for current user)
     * @param  string|null  $link  Optional link to include in the notification
     * @param  array  $data  Optional additional data
     * @return Notification|null
     */
    public function create(string $title, string $message, string $type = 'info', ?int $userId = null, ?string $link = null, array $data = [])
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return null;
        }

        return DB::transaction(fn() => Notification::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'link' => $link,
            'data' => $data,
            'read_at' => null,
        ]));
    }

    /**
     * Create a notification for multiple users.
     *
     * @param  array  $userIds  Array of user IDs
     * @param  string  $title  The notification title
     * @param  string  $message  The notification message
     * @param  string  $type  The notification type (info, success, warning, error)
     * @param  string|null  $link  Optional link to include in the notification
     * @param  array  $data  Optional additional data
     * @return int Number of notifications created
     */
    public function createForMultipleUsers(array $userIds, string $title, string $message, string $type = 'info', ?string $link = null, array $data = []): int
    {
        $count = 0;

        foreach ($userIds as $userId) {
            $notification = $this->create($title, $message, $type, $userId, $link, $data);
            if ($notification) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Create a notification for all users.
     *
     * @param  string  $title  The notification title
     * @param  string  $message  The notification message
     * @param  string  $type  The notification type (info, success, warning, error)
     * @param  string|null  $link  Optional link to include in the notification
     * @param  array  $data  Optional additional data
     * @return int Number of notifications created
     */
    public function createForAllUsers(string $title, string $message, string $type = 'info', ?string $link = null, array $data = []): int
    {
        $userIds = User::pluck('id')->toArray();

        return $this->createForMultipleUsers($userIds, $title, $message, $type, $link, $data);
    }

    /**
     * Mark a notification as read.
     *
     * @param  int  $notificationId  The notification ID
     * @param  int|null  $userId  The user ID (null for current user)
     * @return bool
     */
    public function markAsRead(int $notificationId, ?int $userId = null)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return false;
        }

        $notification = Notification::where('id', $notificationId)
            ->where('user_id', $userId)
            ->first();

        if (! $notification) {
            return false;
        }

        $notification->read_at = Carbon::now();

        return $notification->save();
    }

    /**
     * Mark all notifications as read for a user.
     *
     * @param  int|null  $userId  The user ID (null for current user)
     * @return int Number of notifications marked as read
     */
    public function markAllAsRead(?int $userId = null)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return 0;
        }

        return Notification::where('user_id', $userId)
            ->whereNull('read_at')
            ->update(['read_at' => Carbon::now()]);
    }

    /**
     * Get unread notifications for a user.
     *
     * @param  int|null  $userId  The user ID (null for current user)
     * @param  int  $limit  Maximum number of notifications to return
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUnreadNotifications(?int $userId = null, int $limit = 10)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return collect();
        }

        return Notification::where('user_id', $userId)
            ->whereNull('read_at')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get all notifications for a user.
     *
     * @param  int|null  $userId  The user ID (null for current user)
     * @param  int  $limit  Maximum number of notifications to return
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllNotifications(?int $userId = null, int $limit = 50)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return collect();
        }

        return Notification::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Count unread notifications for a user.
     *
     * @param  int|null  $userId  The user ID (null for current user)
     * @return int
     */
    public function countUnreadNotifications(?int $userId = null)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return 0;
        }

        return Notification::where('user_id', $userId)
            ->whereNull('read_at')
            ->count();
    }

    /**
     * Delete a notification.
     *
     * @param  int  $notificationId  The notification ID
     * @param  int|null  $userId  The user ID (null for current user)
     * @return bool
     */
    public function deleteNotification(int $notificationId, ?int $userId = null)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return false;
        }

        return Notification::where('id', $notificationId)
            ->where('user_id', $userId)
            ->delete() > 0;
    }

    /**
     * Delete all notifications for a user.
     *
     * @param  int|null  $userId  The user ID (null for current user)
     * @return int Number of notifications deleted
     */
    public function deleteAllNotifications(?int $userId = null)
    {
        $userId ??= Auth::id();

        if (! $userId) {
            return 0;
        }

        return Notification::where('user_id', $userId)->delete();
    }

    /**
     * Send an email notification.
     */
    public function sendEmailNotification(string $email, string $subject, string $message, array $data = []): bool
    {
        try {
            SendEmailNotification::dispatch($email, $subject, $message, $data);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to queue email notification', [
                'email' => $email,
                'subject' => $subject,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send an SMS notification.
     */
    public function sendSmsNotification(string $phoneNumber, string $message): bool
    {
        try {
            SendSmsNotification::dispatch($phoneNumber, $message);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to queue SMS notification', [
                'phone_number' => $phoneNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Schedule a follow-up reminder.
     */
    public function scheduleFollowUpReminder(int $userId, int $personId, int $trackingId, Carbon $followUpDate): bool
    {
        try {
            $person = Person::find($personId);
            if (! $person) {
                return false;
            }

            $title = 'Recordatorio de seguimiento';
            $message = "Tienes un seguimiento programado para {$person->name} (Cédula: {$person->cedula})";
            $data = [
                'tracking_id' => $trackingId,
                'person_id' => $personId,
                'follow_up_date' => $followUpDate->format('Y-m-d H:i:s'),
            ];

            // Create a scheduled notification
            Notification::create([
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => 'reminder',
                'data' => json_encode($data),
                'read_at' => null,
                'scheduled_for' => $followUpDate,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to schedule follow-up reminder', [
                'user_id' => $userId,
                'person_id' => $personId,
                'tracking_id' => $trackingId,
                'follow_up_date' => $followUpDate,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send an event reminder.
     */
    public function sendEventReminder(int $personId, int $eventId, string $eventName, Carbon $eventDate): bool
    {
        try {
            $person = Person::find($personId);
            if (! $person) {
                return false;
            }

            // If person has a user account, send in-app notification
            if ($person->user_id) {
                $title = 'Recordatorio de evento';
                $message = "Tienes un evento programado: {$eventName} el {$eventDate->format('d/m/Y H:i')}";
                $data = [
                    'event_id' => $eventId,
                    'event_name' => $eventName,
                    'event_date' => $eventDate->format('Y-m-d H:i:s'),
                ];

                $this->create($title, $message, 'info', $person->user_id, null, $data);
            }

            // If person has an email, send email notification
            if ($person->email) {
                $subject = "Recordatorio: {$eventName}";
                $message = "Estimado/a {$person->name},\n\nTe recordamos que tienes un evento programado: {$eventName} el {$eventDate->format('d/m/Y H:i')}.\n\nSaludos,\nEl equipo de SICAP";
                $data = [
                    'event_id' => $eventId,
                    'event_name' => $eventName,
                    'event_date' => $eventDate->format('Y-m-d H:i:s'),
                ];

                $this->sendEmailNotification($person->email, $subject, $message, $data);
            }

            // If person has a phone number, send SMS notification
            if ($person->phone) {
                $message = "Recordatorio: {$eventName} el {$eventDate->format('d/m/Y H:i')}";
                $this->sendSmsNotification($person->phone, $message);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send event reminder', [
                'person_id' => $personId,
                'event_id' => $eventId,
                'event_name' => $eventName,
                'event_date' => $eventDate,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send a report ready notification.
     */
    public function sendReportReadyNotification(User $user, string $reportType, string $filePath): bool
    {
        try {
            $title = 'Reporte generado';
            $message = "Tu reporte de {$reportType} está listo para descargar";
            $data = [
                'report_type' => $reportType,
                'file_path' => $filePath,
            ];

            $this->create($title, $message, 'info', $user->id, null, $data);

            if ($user->email) {
                $subject = "Reporte de {$reportType} generado";
                $message = "Estimado/a {$user->name},\n\nTu reporte de {$reportType} ha sido generado y está listo para descargar.\n\nPuedes acceder al reporte desde el panel de administración.\n\nSaludos,\nEl equipo de SICAP";
                $this->sendEmailNotification($user->email, $subject, $message, $data);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send report ready notification', [
                'user_id' => $user->id,
                'report_type' => $reportType,
                'file_path' => $filePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Process pending notifications.
     *
     * @return int Number of notifications processed
     */
    public function processPendingNotifications(): int
    {
        try {
            $count = 0;
            $now = Carbon::now();

            // Get all scheduled notifications that are due
            $notifications = Notification::whereNotNull('scheduled_for')
                ->where('scheduled_for', '<=', $now)
                ->whereNull('processed_at')
                ->get();

            foreach ($notifications as $notification) {
                // Mark as processed
                $notification->processed_at = $now;
                $notification->save();

                if ($notification->type === 'reminder') {
                    // Send email if user has email
                    $user = User::find($notification->user_id);
                    if ($user && $user->email) {
                        $this->sendEmailNotification(
                            $user->email,
                            $notification->title,
                            $notification->message,
                            json_decode((string) $notification->data, true) ?? []
                        );
                    }
                }

                $count++;
            }

            return $count;
        } catch (\Exception $e) {
            Log::error('Failed to process pending notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 0;
        }
    }
}
