<?php

namespace App\Services;

use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EventBusService
{
    /**
     * Whether to log events.
     */
    protected bool $logEvents = true;

    /**
     * Create a new service instance.
     */
    public function __construct(
        /**
         * Logging service instance.
         */
        protected LoggingService $loggingService
    )
    {
    }

    /**
     * Dispatch an event.
     */
    public function dispatch(string $eventName, array $payload = []): void
    {
        try {
            // Add event ID to payload
            $payload['event_id'] = (string) Str::uuid();
            $payload['event_name'] = $eventName;
            $payload['event_time'] = now()->toIso8601String();

            // Log the event
            if ($this->logEvents) {
                $this->loggingService->info("Event dispatched: {$eventName}", [
                    'event_id' => $payload['event_id'],
                    'event_name' => $eventName,
                    'payload' => $this->sanitizePayload($payload),
                ]);
            }

            // Dispatch the event
            Event::dispatch($eventName, $payload);
        } catch (\Exception $e) {
            $this->loggingService->error("Error dispatching event: {$e->getMessage()}", [
                'event_name' => $eventName,
                'payload' => $this->sanitizePayload($payload),
                'exception' => $e,
            ]);
        }
    }

    /**
     * Register a listener for an event.
     */
    public function listen(string $eventName, callable $listener): void
    {
        try {
            Event::listen($eventName, function ($payload) use ($eventName, $listener) {
                try {
                    // Log the event handling
                    if ($this->logEvents) {
                        $this->loggingService->debug("Handling event: {$eventName}", [
                            'event_id' => $payload['event_id'] ?? null,
                            'event_name' => $eventName,
                        ]);
                    }

                    // Call the listener
                    return $listener($payload);
                } catch (\Exception $e) {
                    $this->loggingService->error("Error handling event: {$e->getMessage()}", [
                        'event_name' => $eventName,
                        'event_id' => $payload['event_id'] ?? null,
                        'exception' => $e,
                    ]);
                }
            });
        } catch (\Exception $e) {
            $this->loggingService->error("Error registering event listener: {$e->getMessage()}", [
                'event_name' => $eventName,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Register multiple listeners for an event.
     */
    public function listenMany(string $eventName, array $listeners): void
    {
        foreach ($listeners as $listener) {
            $this->listen($eventName, $listener);
        }
    }

    /**
     * Register a listener for multiple events.
     */
    public function listenToMany(array $eventNames, callable $listener): void
    {
        foreach ($eventNames as $eventName) {
            $this->listen($eventName, $listener);
        }
    }

    /**
     * Sanitize the payload for logging.
     */
    protected function sanitizePayload(array $payload): array
    {
        $sanitized = [];

        foreach ($payload as $key => $value) {
            if (in_array(strtolower($key), ['password', 'token', 'secret', 'key', 'api_key', 'auth', 'authorization'])) {
                $sanitized[$key] = '********';
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizePayload($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Set whether to log events.
     *
     * @return $this
     */
    public function setLogEvents(bool $logEvents): static
    {
        $this->logEvents = $logEvents;

        return $this;
    }
}
