<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApiGatewayService
{
    /**
     * Base URL for internal API calls
     */
    protected string $baseUrl;

    /**
     * Default timeout for API calls in seconds
     */
    protected int $timeout = 30;

    /**
     * Default cache duration in minutes
     */
    protected int $cacheDuration = 60;

    /**
     * Create a new service instance.
     */
    public function __construct(/**
     * Cache service instance
     */
    protected CacheService $cacheService)
    {
        $this->baseUrl = config('services.api_gateway.url', 'http://localhost/api');
    }

    /**
     * Make a GET request to the API.
     *
     * @return mixed
     */
    public function get(string $endpoint, array $params = [], bool $useCache = true, ?int $cacheDuration = null)
    {
        $url = $this->buildUrl($endpoint);
        $cacheKey = $this->cacheService->queryKey("api_gateway_get_{$endpoint}", $params);

        if ($useCache) {
            return $this->cacheService->remember($cacheKey, fn() => $this->makeRequest('get', $url, $params), $cacheDuration ?? $this->cacheDuration);
        }

        return $this->makeRequest('get', $url, $params);
    }

    /**
     * Make a POST request to the API.
     *
     * @return mixed
     */
    public function post(string $endpoint, array $data = [])
    {
        $url = $this->buildUrl($endpoint);

        return $this->makeRequest('post', $url, $data);
    }

    /**
     * Make a PUT request to the API.
     *
     * @return mixed
     */
    public function put(string $endpoint, array $data = [])
    {
        $url = $this->buildUrl($endpoint);

        return $this->makeRequest('put', $url, $data);
    }

    /**
     * Make a DELETE request to the API.
     *
     * @return mixed
     */
    public function delete(string $endpoint, array $params = [])
    {
        $url = $this->buildUrl($endpoint);

        return $this->makeRequest('delete', $url, $params);
    }

    /**
     * Build the full URL for an API endpoint.
     */
    protected function buildUrl(string $endpoint): string
    {
        return rtrim($this->baseUrl, '/').'/'.ltrim($endpoint, '/');
    }

    /**
     * Make an HTTP request to the API.
     *
     * @return mixed
     */
    protected function makeRequest(string $method, string $url, array $data = [])
    {
        try {
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'Accept' => 'application/json',
                    'X-Internal-Request' => 'true',
                ])
                ->{$method}($url, $data);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error("API Gateway Error: {$response->status()}", [
                'url' => $url,
                'method' => $method,
                'data' => $data,
                'response' => $response->body(),
            ]);

            return [
                'success' => false,
                'error' => "API Error: {$response->status()}",
                'message' => $response->json()['message'] ?? 'Unknown error',
            ];
        } catch (\Exception $e) {
            Log::error("API Gateway Exception: {$e->getMessage()}", [
                'url' => $url,
                'method' => $method,
                'data' => $data,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'error' => 'API Gateway Exception',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Set the timeout for API calls.
     *
     * @return $this
     */
    public function setTimeout(int $seconds): static
    {
        $this->timeout = $seconds;

        return $this;
    }

    /**
     * Set the cache duration.
     *
     * @return $this
     */
    public function setCacheDuration(int $minutes): static
    {
        $this->cacheDuration = $minutes;

        return $this;
    }
}
