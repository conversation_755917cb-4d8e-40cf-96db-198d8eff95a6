<?php

namespace App\Listeners;

use App\Events\PersonAssignedTo1x10Leader;
use App\Models\Tracking;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;

class CreateTrackingForPersonAssignment implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PersonAssignedTo1x10Leader $event): void
    {
        // Create a tracking record for the assignment
        Tracking::create([
            'person_id' => $event->person->id,
            'trackable_type' => \App\Models\Person::class,
            'trackable_id' => $event->leader->id,
            'user_id' => Auth::id() ?? 1, // Default to admin user if not authenticated
            'tracking_type' => 'person_assignment',
            'status' => 'completed',
            'notes' => "Persona asignada al líder 1x10: {$event->leader->name}",
            'tracking_date' => now(),
            'last_updated' => now(),
        ]);
    }
}
