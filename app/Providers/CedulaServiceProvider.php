<?php

namespace App\Providers;

use App\Services\CedulaService;
use Illuminate\Support\ServiceProvider;

class CedulaServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(CedulaService::class, fn($app): \App\Services\CedulaService => new CedulaService);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
