<?php

namespace App\Providers;

use App\Services\CacheService;
use App\Services\Integration\ElectoralIntegrationService;
use App\Services\Integration\IntegrationFacade;
use App\Services\Integration\PatrolIntegrationService;
use App\Services\Integration\PersonIntegrationService;
use App\Services\Integration\TrackingIntegrationService;
use Illuminate\Support\ServiceProvider;

class IntegrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register individual integration services
        $this->app->singleton(PersonIntegrationService::class, fn($app): \App\Services\Integration\PersonIntegrationService => new PersonIntegrationService($app->make(CacheService::class)));

        $this->app->singleton(PatrolIntegrationService::class, fn($app): \App\Services\Integration\PatrolIntegrationService => new PatrolIntegrationService($app->make(CacheService::class)));

        $this->app->singleton(ElectoralIntegrationService::class, fn($app): \App\Services\Integration\ElectoralIntegrationService => new ElectoralIntegrationService($app->make(CacheService::class)));

        $this->app->singleton(TrackingIntegrationService::class, fn($app): \App\Services\Integration\TrackingIntegrationService => new TrackingIntegrationService($app->make(CacheService::class)));

        // Register the integration facade
        $this->app->singleton(IntegrationFacade::class, fn($app): \App\Services\Integration\IntegrationFacade => new IntegrationFacade(
            $app->make(PersonIntegrationService::class),
            $app->make(PatrolIntegrationService::class),
            $app->make(ElectoralIntegrationService::class),
            $app->make(TrackingIntegrationService::class)
        ));
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
