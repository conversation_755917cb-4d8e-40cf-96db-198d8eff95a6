<?php

namespace App\Providers;

use App\Services\CacheService;
use Illuminate\Support\ServiceProvider;

class CacheServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(CacheService::class, fn($app): \App\Services\CacheService => new CacheService);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
