<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RegistrationTokenNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        /**
         * The registration token.
         */
        protected $token,
        /**
         * The person's name.
         */
        protected $name
    )
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        try {
            // Obtener la ruta con el token
            $url = url(route('register.complete', [
                'token' => $this->token
            ]));

            // Registrar la URL generada para depuración
            \Log::info('URL de registro generada', [
                'token' => $this->token,
                'email' => $notifiable->routes['mail'] ?? 'no_email',
                'url' => $url
            ]);

            return (new MailMessage)
                ->subject('Completa tu registro')
                ->greeting('¡Hola ' . $this->name . '!')
                ->line('Has sido registrado en nuestro sistema. Para completar tu registro, haz clic en el botón de abajo.')
                ->action('Completar Registro', $url)
                ->line('Este enlace expirará en 60 minutos.')
                ->line('Si no solicitaste este registro, puedes ignorar este correo.')
                ->salutation('Saludos, ' . config('app.name'));
        } catch (\Exception $e) {
            \Log::error('Error al generar el correo de registro: ' . $e->getMessage(), [
                'token' => $this->token,
                'name' => $this->name,
                'exception' => $e::class
            ]);

            // Devolver un mensaje básico en caso de error
            return (new MailMessage)
                ->subject('Completa tu registro')
                ->greeting('¡Hola ' . $this->name . '!')
                ->line('Has sido registrado en nuestro sistema.')
                ->line('Por favor, contacta al administrador para completar tu registro.')
                ->salutation('Saludos, ' . config('app.name'));
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
