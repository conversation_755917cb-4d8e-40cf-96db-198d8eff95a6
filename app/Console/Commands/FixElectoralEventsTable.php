<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

class FixElectoralEventsTable extends Command
{
    protected $signature = 'db:fix-electoral-events';
    protected $description = 'Corrige la tabla electoral_events y sus índices';

    public function handle(): int
    {
        $this->info('Iniciando corrección de la tabla electoral_events...');

        // 1. Desactivar temporalmente las restricciones de clave foránea
        $this->info('Desactivando restricciones de clave foránea...');
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // 2. Verificar si existen tablas que dependen de electoral_events
        $this->info('Verificando tablas dependientes...');
        $dependentTables = [
            'electoral_event_attendances'
        ];

        foreach ($dependentTables as $table) {
            if (Schema::hasTable($table)) {
                $this->info("Eliminando tabla dependiente: {$table}");
                DB::statement("DROP TABLE IF EXISTS {$table}");
            }
        }

        // 3. Eliminar la tabla electoral_events si existe
        if (Schema::hasTable('electoral_events')) {
            $this->info('Eliminando tabla electoral_events...');
            DB::statement('DROP TABLE IF EXISTS electoral_events');
        }

        // 4. Crear la tabla electoral_events con SQL directo
        $this->info('Creando tabla electoral_events...');
        DB::statement('
            CREATE TABLE `electoral_events` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `start_date` datetime NOT NULL,
                `end_date` datetime DEFAULT NULL,
                `location` varchar(255) DEFAULT NULL,
                `address` varchar(255) DEFAULT NULL,
                `estate_id` bigint(20) UNSIGNED DEFAULT NULL,
                `municipality_id` bigint(20) UNSIGNED DEFAULT NULL,
                `parish_id` bigint(20) UNSIGNED DEFAULT NULL,
                `type` enum("training","mobilization","voting","meeting","other") NOT NULL DEFAULT "meeting",
                `status` enum("scheduled","in_progress","completed","cancelled") NOT NULL DEFAULT "scheduled",
                `capacity` int(11) DEFAULT NULL,
                `organizer_id` bigint(20) UNSIGNED DEFAULT NULL,
                `qr_code` varchar(255) DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                `deleted_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `ee_start_date_idx` (`start_date`),
                KEY `ee_status_idx` (`status`),
                KEY `ee_type_idx` (`type`),
                KEY `ee_name_idx` (`name`),
                KEY `ee_end_date_idx` (`end_date`),
                KEY `ee_location_idx` (`estate_id`,`municipality_id`,`parish_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');

        // 5. Agregar las claves foráneas una por una
        $this->info('Agregando claves foráneas a electoral_events...');
        
        try {
            DB::statement('
                ALTER TABLE `electoral_events`
                ADD CONSTRAINT `electoral_events_estate_id_foreign` 
                FOREIGN KEY (`estate_id`) REFERENCES `estates` (`id`) 
                ON DELETE SET NULL
            ');
            $this->info('Clave foránea estate_id agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea estate_id: ' . $e->getMessage());
        }
        
        try {
            DB::statement('
                ALTER TABLE `electoral_events`
                ADD CONSTRAINT `electoral_events_municipality_id_foreign` 
                FOREIGN KEY (`municipality_id`) REFERENCES `municipalities` (`id`) 
                ON DELETE SET NULL
            ');
            $this->info('Clave foránea municipality_id agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea municipality_id: ' . $e->getMessage());
        }
        
        try {
            DB::statement('
                ALTER TABLE `electoral_events`
                ADD CONSTRAINT `electoral_events_parish_id_foreign` 
                FOREIGN KEY (`parish_id`) REFERENCES `parishes` (`id`) 
                ON DELETE SET NULL
            ');
            $this->info('Clave foránea parish_id agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea parish_id: ' . $e->getMessage());
        }
        
        try {
            DB::statement('
                ALTER TABLE `electoral_events`
                ADD CONSTRAINT `electoral_events_organizer_id_foreign` 
                FOREIGN KEY (`organizer_id`) REFERENCES `users` (`id`) 
                ON DELETE SET NULL
            ');
            $this->info('Clave foránea organizer_id agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea organizer_id: ' . $e->getMessage());
        }

        // 6. Crear la tabla electoral_event_attendances
        $this->info('Creando tabla electoral_event_attendances...');
        DB::statement('
            CREATE TABLE `electoral_event_attendances` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `event_id` bigint(20) UNSIGNED NOT NULL,
                `person_id` bigint(20) UNSIGNED NOT NULL,
                `check_in_time` datetime DEFAULT NULL,
                `check_out_time` datetime DEFAULT NULL,
                `status` enum("registered","confirmed","attended","absent") NOT NULL DEFAULT "registered",
                `confirmation_code` varchar(255) DEFAULT NULL,
                `qr_code` varchar(255) DEFAULT NULL,
                `notes` text DEFAULT NULL,
                `registered_by` bigint(20) UNSIGNED DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                `deleted_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `eea_event_idx` (`event_id`),
                KEY `eea_person_idx` (`person_id`),
                KEY `eea_status_idx` (`status`),
                KEY `eea_event_person_idx` (`event_id`,`person_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');

        // 7. Agregar las claves foráneas a electoral_event_attendances
        $this->info('Agregando claves foráneas a electoral_event_attendances...');
        
        try {
            DB::statement('
                ALTER TABLE `electoral_event_attendances`
                ADD CONSTRAINT `electoral_event_attendances_event_id_foreign` 
                FOREIGN KEY (`event_id`) REFERENCES `electoral_events` (`id`) 
                ON DELETE CASCADE
            ');
            $this->info('Clave foránea event_id agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea event_id: ' . $e->getMessage());
        }
        
        try {
            DB::statement('
                ALTER TABLE `electoral_event_attendances`
                ADD CONSTRAINT `electoral_event_attendances_person_id_foreign` 
                FOREIGN KEY (`person_id`) REFERENCES `people` (`id`) 
                ON DELETE CASCADE
            ');
            $this->info('Clave foránea person_id agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea person_id: ' . $e->getMessage());
        }
        
        try {
            DB::statement('
                ALTER TABLE `electoral_event_attendances`
                ADD CONSTRAINT `electoral_event_attendances_registered_by_foreign` 
                FOREIGN KEY (`registered_by`) REFERENCES `users` (`id`) 
                ON DELETE SET NULL
            ');
            $this->info('Clave foránea registered_by agregada correctamente.');
        } catch (\Exception $e) {
            $this->error('Error al agregar clave foránea registered_by: ' . $e->getMessage());
        }

        // 8. Reactivar las restricciones de clave foránea
        $this->info('Reactivando restricciones de clave foránea...');
        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        // 9. Marcar las migraciones problemáticas como ejecutadas
        $this->info('Marcando migraciones problemáticas como ejecutadas...');
        $problematicMigrations = [
            '2025_05_15_000001_add_indexes_to_tables',
            '2025_05_20_000001_add_performance_indexes',
            '2025_05_30_000001_add_missing_indexes',
            '2025_06_20_000001_recreate_electoral_events_table',
            '2025_06_25_000001_drop_and_recreate_electoral_events_table',
            '2025_06_26_000001_fix_electoral_events_with_raw_sql'
        ];

        $batch = DB::table('migrations')->max('batch') + 1;
        
        foreach ($problematicMigrations as $migration) {
            if (!DB::table('migrations')->where('migration', $migration)->exists()) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => $batch
                ]);
                $this->info("Migración {$migration} marcada como ejecutada.");
            }
        }

        $this->info('Corrección de la tabla electoral_events completada correctamente.');
        
        return 0;
    }
}
