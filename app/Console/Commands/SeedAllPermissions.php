<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SeedAllPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:seed-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ejecuta todos los seeders de permisos';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Ejecutando todos los seeders de permisos...');

        $seeders = [
            'GeneralPermissionsSeeder',
            'UserPermissionsSeeder',
            'RolePermissionsSeeder',
            'PermissionPermissionsSeeder',

            'PersonPermissionsSeeder',
            'ElectoralPermissionsSeeder',
            'PatrolPermissionsSeeder',
            'TrackingPermissionsSeeder',
            'AnalyticsPermissionsSeeder',
            'CampaignPermissionsSeeder',
            'CommunicationPermissionsSeeder',
            'MaterialPermissionsSeeder',
            'ContactPermissionsSeeder',
            'FrontendPermissionsSeeder',
            'IntegrationPermissionsSeeder',
            'LocationPermissionsSeeder',
            'MobilizationPermissionsSeeder',
            'UserSettingsPermissionsSeeder',
            // 'Group1x10PermissionsSeeder', // Eliminado
            'MissingPermissionsSeeder',
        ];

        foreach ($seeders as $seeder) {
            $this->call('db:seed', [
                '--class' => "Database\\Seeders\\$seeder",
            ]);
        }

        $this->info('¡Todos los permisos han sido agregados exitosamente!');

        return Command::SUCCESS;
    }
}
