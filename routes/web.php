<?php

use App\Http\Controllers\CedulaController;
use App\Models\Person;
use App\Models\RegistrationToken;
use App\Notifications\RegistrationTokenNotification;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Route;

// Public home page - accessible to everyone
Route::get('/', \App\Livewire\Home::class)->name('home');

// Cedula test route
Route::get('/cedula/test', [CedulaController::class, 'test'])->name('cedula.test');

// Test mail route
Route::get('/test-mail', function (): string {
    try {
        $email = '<EMAIL>';
        $name = 'Test User';

        Notification::route('mail', $email)
            ->notify(new RegistrationTokenNotification('test-token', $name));

        return 'Email sent successfully!';
    } catch (\Exception $e) {
        return 'Error sending email: ' . $e->getMessage();
    }
});

// Dashboard - only accessible to 1x10 leaders
Route::get('/dashboard', \App\Livewire\Dashboard::class)->middleware(['auth', 'verified', 'leader1x10', 'can:view dashboard'])->name('dashboard');

// Notifications - only accessible to 1x10 leaders
Route::get('/notifications', \App\Livewire\Notifications\Index::class)->middleware(['auth', 'verified', 'leader1x10', 'can:view notifications'])->name('notifications');

// Frontend 1x10 Person Management - only accessible to 1x10 leaders
Route::middleware(['auth', 'verified', 'leader1x10'])->prefix('1x10')->as('1x10.')->group(function (): void {
    Route::get('/', \App\Livewire\Frontend\Person\ManageAssignedPersons::class)->name('manage')->middleware('can:manage own 1x10 group');
});


Route::middleware(['auth'])->group(function (): void {

    // Impersonations
    Route::post('/impersonate/{user}', [\App\Http\Controllers\ImpersonationController::class, 'store'])->name('impersonate.store')->middleware('can:impersonate');
    Route::delete('/impersonate/stop', [\App\Http\Controllers\ImpersonationController::class, 'destroy'])->name('impersonate.destroy');

    // Settings
    Route::prefix('settings')->as('settings.')->group(function (): void {
        Route::redirect('/', 'settings/profile');
        Route::get('/profile', \App\Livewire\Settings\Profile::class)->name('profile')->middleware('can:update profile');
        Route::get('/password', \App\Livewire\Settings\Password::class)->name('password')->middleware('can:update password');
        Route::get('/appearance', \App\Livewire\Settings\Appearance::class)->name('appearance')->middleware('can:update appearance');
        Route::get('/locale', \App\Livewire\Settings\Locale::class)->name('locale')->middleware('can:update locale');
    });

    // Admin
    Route::prefix('admin')->as('admin.')->group(function (): void {
        Route::get('/', \App\Livewire\Admin\Index::class)->middleware(['auth', 'verified', 'can:access dashboard'])->name('index');

        // Persons - TEMPORARILY DISABLED - MISSING FILES
        /*
        Route::prefix('persons')->as('persons.')->group(function (): void {
            Route::get('/', \App\Livewire\Admin\Persons\Pages\IndexPerson::class)->name('index')->middleware(['can:view persons', 'admin_or_leader1x10']);
            Route::get('/create', \App\Livewire\Admin\Persons\Pages\CreatePerson::class)->name('create')->middleware(['can:create persons', 'admin_or_leader1x10']);
            Route::get('/search', \App\Livewire\Admin\Persons\Pages\SearchPerson::class)->name('search')->middleware(['can:view persons', 'admin_or_leader1x10']);
            Route::get('/assign', \App\Livewire\Admin\Persons\Pages\Assign::class)->name('assign')->middleware(['admin_or_leader1x10', 'can:assign 1x10']);
            Route::get('/{person}', \App\Livewire\Admin\Persons\Pages\ShowPerson::class)->name('show')->middleware(['can:view persons', 'admin_or_leader1x10']);
            Route::get('/{person}/edit', \App\Livewire\Admin\Persons\Pages\EditPerson::class)->name('edit')->middleware(['can:update persons', 'admin_or_leader1x10']);
        });
        */
        // Tracking - TEMPORARILY DISABLED - MISSING FILES
        /*
        Route::prefix('tracking')->as('tracking.')->group(function (): void {
            Route::get('/', \App\Livewire\Admin\Tracking\Pages\IndexTracking::class)->name('index')->middleware('can:view tracking');
            Route::get('/mobilization', \App\Livewire\Admin\Tracking\Pages\MobilizationTracking::class)->name('mobilization')->middleware('can:view tracking');
            Route::get('/mark-voters', \App\Livewire\Admin\Tracking\Pages\MarkVotersTracking::class)->name('mark-voters')->middleware('can:update tracking');
            Route::get('/contacts', \App\Livewire\Admin\Tracking\Pages\ContactsTracking::class)->name('contacts')->middleware('can:view tracking');
            Route::get('/history', \App\Livewire\Admin\Tracking\Pages\HistoryTracking::class)->name('history')->middleware('can:view tracking');
            Route::get('/reports', \App\Livewire\Admin\Tracking\Pages\ReportsTracking::class)->name('reports')->middleware('can:view tracking');
        });
        */

        // Groups 1x10 module has been removed

        // Patrol - TEMPORARILY DISABLED - MISSING FILES
        /*
        Route::prefix('patrol')->as('patrol.')->group(function (): void {
            Route::get('/', \App\Livewire\Admin\Patrol\Pages\IndexPatrol::class)->name('index')->middleware('can:view patrol');
            Route::get('/create', \App\Livewire\Admin\Patrol\Pages\CreatePatrolGroup::class)->name('create')->middleware('can:create patrol');
            Route::get('/{group}', \App\Livewire\Admin\Patrol\Pages\ShowPatrolGroup::class)->name('show')->middleware('can:view patrol');
            Route::get('/{group}/members', \App\Livewire\Admin\Patrol\Pages\ManagePatrolGroupMembers::class)->name('members')->middleware('can:manage patrol members');
            Route::get('/{group}/activities', \App\Livewire\Admin\Patrol\Pages\ManagePatrolActivities::class)->name('activities')->middleware('can:manage patrol activities');
            Route::get('/reports', \App\Livewire\Admin\Patrol\Pages\PatrolReports::class)->name('reports')->middleware('can:view patrol reports');
        });
        */

        // Electoral - TEMPORARILY DISABLED - MISSING FILES
        /*
        Route::prefix('electoral')->as('electoral.')->group(function (): void {
            // Dashboard
            Route::get('/', \App\Livewire\Admin\Electoral\Pages\IndexElectoral::class)->name('index')->middleware('can:view electoral');

            // Eventos Electorales
            Route::get('/events', \App\Livewire\Admin\Electoral\Pages\EventsElectoral::class)->name('events')->middleware('can:view electoral events');
            Route::get('/events/create', \App\Livewire\Admin\Electoral\Pages\CreateElectoralEvent::class)->name('events.create')->middleware('can:create electoral events');
            Route::get('/events/{event}', \App\Livewire\Admin\Electoral\Pages\ShowElectoralEvent::class)->name('events.show')->middleware('can:view electoral events');
            Route::get('/events/{event}/edit', \App\Livewire\Admin\Electoral\Pages\EditElectoralEvent::class)->name('events.edit')->middleware('can:update electoral events');
            Route::get('/events/{event}/attendance', \App\Livewire\Admin\Electoral\Pages\ManageElectoralEventAttendance::class)->name('events.attendance')->middleware('can:manage electoral event attendance');

            // Centros de Votación
            Route::get('/voting-centers', \App\Livewire\Admin\Electoral\Pages\VotingCentersElectoral::class)->name('voting-centers')->middleware('can:view voting centers');
            Route::get('/voting-centers/create', \App\Livewire\Admin\Electoral\Pages\CreateElectoralVotingCenter::class)->name('voting-centers.create')->middleware('can:create voting centers');
            Route::get('/voting-centers/{center}', \App\Livewire\Admin\Electoral\Pages\ShowElectoralVotingCenter::class)->name('voting-centers.show')->middleware('can:view voting centers');
            Route::get('/voting-centers/{center}/edit', \App\Livewire\Admin\Electoral\Pages\EditElectoralVotingCenter::class)->name('voting-centers.edit')->middleware('can:update voting centers');

            // Movilización Electoral
            Route::get('/mobilization', \App\Livewire\Admin\Electoral\Pages\MobilizationElectoral::class)->name('mobilization')->middleware('can:view electoral mobilization');
            Route::get('/mobilization/create', \App\Livewire\Admin\Electoral\Pages\CreateElectoralMobilization::class)->name('mobilization.create')->middleware('can:create electoral mobilization');
            Route::get('/mobilization/{mobilization}', \App\Livewire\Admin\Electoral\Pages\ShowElectoralMobilization::class)->name('mobilization.show')->middleware('can:view electoral mobilization');
            Route::get('/mobilization/{mobilization}/edit', \App\Livewire\Admin\Electoral\Pages\EditElectoralMobilization::class)->name('mobilization.edit')->middleware('can:update electoral mobilization');
            Route::get('/mobilization/{mobilization}/details', \App\Livewire\Admin\Electoral\Pages\ManageElectoralMobilizationDetails::class)->name('mobilization.details')->middleware('can:manage electoral mobilization details');

            // Mapa de Movilización
            Route::get('/mobilization-map', \App\Livewire\Admin\Electoral\Pages\MobilizationMap::class)->name('mobilization-map')->middleware('can:view mobilization map');

            // Seguimiento de Votación
            Route::get('/voting-tracker', \App\Livewire\Admin\Electoral\Pages\VotingTracker::class)->name('voting-tracker')->middleware('can:view voting tracker');

            // Reportes
            Route::get('/reports', \App\Livewire\Admin\Electoral\Pages\ElectoralReports::class)->name('reports')->middleware('can:view electoral reports');
            Route::get('/reports/export', \App\Livewire\Admin\Electoral\Pages\ExportElectoralReports::class)->name('reports.export')->middleware('can:export electoral data');

            // Configuración
            Route::get('/settings', \App\Livewire\Admin\Electoral\Pages\ElectoralSettings::class)->name('settings')->middleware('can:manage electoral settings');
        });
        */

        // campaigns - TEMPORARILY DISABLED - MISSING FILES
        /*
        Route::get('/campaigns', \App\Livewire\Admin\Campaigns\Pages\IndexCampaign::class)->name('campaigns')->middleware('can:view campaigns');
        Route::get('/campaigns/create', \App\Livewire\Admin\Campaigns\Pages\CreateCampaign::class)->name('campaigns.create')->middleware('can:create campaigns');
        Route::get('/campaigns/reports', \App\Livewire\Admin\Campaigns\Pages\CampaignReports::class)->name('campaigns.reports')->middleware('can:view campaigns');
        Route::get('/campaigns/{campaign}', \App\Livewire\Admin\Campaigns\Pages\ShowCampaign::class)->name('campaigns.show')->middleware('can:view campaigns');
        Route::get('/campaigns/{campaign}/edit', \App\Livewire\Admin\Campaigns\Pages\EditCampaign::class)->name('campaigns.edit')->middleware('can:update campaigns');
        Route::get('/campaigns/{campaign}/destroy', \App\Livewire\Admin\Campaigns\Pages\DestroyCampaign::class)->name('campaigns.destroy')->middleware('can:delete campaigns');
        */





        // Analytics - TEMPORARILY DISABLED - MISSING FILES
        // Route::get('/analytics', \App\Livewire\Admin\Analytics\Index::class)->name('analytics')->middleware('can:view analytics');

        // Settings
        Route::prefix('settings')->as('settings.')->group(function (): void {
            Route::get('/general', \App\Livewire\Admin\Settings\Pages\GeneralSettings::class)->name('general')->middleware('can:manage settings');
            Route::get('/locations', \App\Livewire\Admin\Settings\Pages\LocationsSettings::class)->name('locations')->middleware('can:manage settings');
            Route::get('/system', \App\Livewire\Admin\Settings\Pages\SystemSettings::class)->name('system')->middleware('can:manage settings');
        });

        // Users
        Route::get('/users', \App\Livewire\Admin\Users::class)->name('users.index')->middleware('can:view users');
        Route::get('/users/create', \App\Livewire\Admin\Users\CreateUser::class)->name('users.create')->middleware('can:create users');
        Route::get('/users/{user}', \App\Livewire\Admin\Users\ViewUser::class)->name('users.show')->middleware('can:view users');
        Route::get('/users/{user}/edit', \App\Livewire\Admin\Users\EditUser::class)->name('users.edit')->middleware('can:update users');

        // Roles
        Route::get('/roles', \App\Livewire\Admin\Roles::class)->name('roles.index')->middleware('can:view roles');
        Route::get('/roles/create', \App\Livewire\Admin\Roles\CreateRole::class)->name('roles.create')->middleware('can:create roles');
        Route::get('/roles/{role}/edit', \App\Livewire\Admin\Roles\EditRole::class)->name('roles.edit')->middleware('can:update roles');

        // Permissions
        Route::get('/permissions', \App\Livewire\Admin\Permissions::class)->name('permissions.index')->middleware('can:view permissions');
        Route::get('/permissions/create', \App\Livewire\Admin\Permissions\CreatePermission::class)->name('permissions.create')->middleware('can:create permissions');
        Route::get('/permissions/{permission}/edit', \App\Livewire\Admin\Permissions\EditPermission::class)->name('permissions.edit')->middleware('can:update permissions');
    });
});

require __DIR__.'/auth.php';
require __DIR__.'/integration.php';
