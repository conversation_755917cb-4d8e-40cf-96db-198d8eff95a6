<?php

use App\Http\Controllers\ModuleIntegrationController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->prefix('admin/integration')->as('admin.integration.')->group(function (): void {
    // Panel de integración
    Route::get('/', [ModuleIntegrationController::class, 'index'])->name('index')->middleware('can:view integration');

    // Resumen integrado de persona
    Route::get('/person/{person}', [ModuleIntegrationController::class, 'personSummary'])->name('person-summary')->middleware('can:view integration');

    // Ruta eliminada: create-campaign-for-group

    // Crear campaña para movilización electoral
    Route::post('/create-campaign-for-mobilization', [ModuleIntegrationController::class, 'createCampaignForMobilization'])->name('create-campaign-for-mobilization')->middleware('can:manage integration');

    // Crear tracking integrado para persona
    Route::post('/create-integrated-tracking/{person}', [ModuleIntegrationController::class, 'createIntegratedTracking'])->name('create-integrated-tracking')->middleware('can:manage integration');
});
