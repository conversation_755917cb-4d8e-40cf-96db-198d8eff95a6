<?php

use App\Http\Controllers\Api\ElectoralController;
use App\Http\Controllers\Api\PatrolController;
use App\Http\Controllers\Api\PersonController;
use App\Http\Controllers\Api\TrackingController;
use App\Http\Controllers\CedulaController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', fn(Request $request) => $request->user());

// Cedula API routes
Route::get('/cedula/consultar', [CedulaController::class, 'consultar']);

// API V1 Routes
Route::prefix('v1')->group(function (): void {
    // Public routes
    Route::get('/health-check', fn() => response()->json(['status' => 'ok', 'message' => 'API is running']));

    // Protected routes
    Route::middleware('auth:sanctum')->group(function (): void {
        // Persons API
        Route::prefix('persons')->group(function (): void {
            Route::get('/', [PersonController::class, 'index'])->middleware('can:view persons');
            Route::post('/', [PersonController::class, 'store'])->middleware('can:create persons');
            Route::get('/{id}', [PersonController::class, 'show'])->middleware('can:view persons');
            Route::put('/{id}', [PersonController::class, 'update'])->middleware('can:update persons');
            Route::delete('/{id}', [PersonController::class, 'destroy'])->middleware('can:delete persons');
            Route::get('/leaders', [PersonController::class, 'getLeaders'])->middleware('can:view persons');
            Route::get('/leaders/{leaderId}/assigned', [PersonController::class, 'getAssignedPersons'])->middleware('can:view persons');
        });

        // Electoral API
        Route::prefix('electoral')->group(function (): void {
            // Events
            Route::get('/events', [ElectoralController::class, 'indexEvents'])->middleware('can:view electoral events');
            Route::post('/events', [ElectoralController::class, 'storeEvent'])->middleware('can:create electoral events');
            Route::get('/events/{id}', [ElectoralController::class, 'showEvent'])->middleware('can:view electoral events');
            Route::put('/events/{id}', [ElectoralController::class, 'updateEvent'])->middleware('can:update electoral events');
            Route::delete('/events/{id}', [ElectoralController::class, 'destroyEvent'])->middleware('can:delete electoral events');
            Route::post('/events/attendance', [ElectoralController::class, 'registerAttendance'])->middleware('can:manage electoral event attendance');

            // Voting Centers
            Route::get('/voting-centers', [ElectoralController::class, 'indexVotingCenters'])->middleware('can:view electoral voting centers');

            // Mobilizations
            Route::get('/mobilizations', [ElectoralController::class, 'indexMobilizations'])->middleware('can:view electoral mobilization');

            // Statistics
            Route::get('/statistics', [ElectoralController::class, 'getStatistics'])->middleware('can:view electoral statistics');
        });

        // Patrol API
        Route::prefix('patrol')->group(function (): void {
            // Groups
            Route::get('/groups', [PatrolController::class, 'indexGroups'])->middleware('can:view patrol');
            Route::post('/groups', [PatrolController::class, 'storeGroup'])->middleware('can:create patrol');
            Route::get('/groups/{id}', [PatrolController::class, 'showGroup'])->middleware('can:view patrol');
            Route::put('/groups/{id}', [PatrolController::class, 'updateGroup'])->middleware('can:update patrol');
            Route::delete('/groups/{id}', [PatrolController::class, 'destroyGroup'])->middleware('can:delete patrol');

            // Members
            Route::post('/members/add', [PatrolController::class, 'addMember'])->middleware('can:manage patrol members');
            Route::post('/members/remove', [PatrolController::class, 'removeMember'])->middleware('can:manage patrol members');

            // Activities
            Route::get('/activities', [PatrolController::class, 'indexActivities'])->middleware('can:view patrol activities');

            // Statistics
            Route::get('/statistics', [PatrolController::class, 'getStatistics'])->middleware('can:view patrol');
        });

        // Tracking API
        Route::prefix('tracking')->group(function (): void {
            Route::get('/', [TrackingController::class, 'index'])->middleware('can:view tracking');
            Route::post('/', [TrackingController::class, 'store'])->middleware('can:create tracking');
            Route::get('/{id}', [TrackingController::class, 'show'])->middleware('can:view tracking');
            Route::put('/{id}', [TrackingController::class, 'update'])->middleware('can:update tracking');
            Route::delete('/{id}', [TrackingController::class, 'destroy'])->middleware('can:delete tracking');
            Route::post('/bulk', [TrackingController::class, 'bulkStore'])->middleware('can:create tracking');
            Route::get('/statistics', [TrackingController::class, 'getStatistics'])->middleware('can:view tracking');
        });
    });
});
